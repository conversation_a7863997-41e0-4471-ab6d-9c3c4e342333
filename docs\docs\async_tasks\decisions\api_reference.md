# 异步决策智能体 API 参考

本文档提供了异步决策智能体模块的完整API参考,包括所有类、方法、参数和返回值的详细说明。

## 目录



## 模块导入

```python
from industrytslib.core_aysnc.async_decision_agents import (
    AsyncDecisionMaking,
    AsyncMultiOutputDecisionMaking,
    create_async_decision_agent,
    run_async_decision_agent
)
```

## AsyncDecisionMaking 类

### 类定义

```python
class AsyncDecisionMaking(AsyncScheduledTask):
    """异步决策智能体基础类
    
    提供异步优化决策流程支持,包括数据库连接管理、模型构建、
    优化问题求解、决策结果写入等功能。
    """
```

### 构造函数

```python
def __init__(
    self,
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False
) -> None:
```

**参数:**
- `project_name` (str): 项目名称,用于标识优化项目
- `dbconfig` (Dict[str, Any]): 数据库配置字典
- `local_test_mode` (bool, 可选): 是否启用本地测试模式,默认为 False

**数据库配置格式:**
```python
dbconfig = {
    "web_database": {
        "host": str,           # 数据库主机地址
        "port": int,           # 数据库端口
        "database": str,       # 数据库名称
        "username": str,       # 用户名
        "password": str,       # 密码
        "driver": str          # ODBC驱动名称
    },
    "ts_database": {
        "host": str,           # 时序数据库主机地址
        "port": int,           # 时序数据库端口
        "database": str,       # 时序数据库名称
        "username": str,       # 用户名
        "password": str,       # 密码
        "driver": str          # ODBC驱动名称
    }
}
```

### 核心属性

```python
# 项目信息
self.project_name: str                    # 项目名称
self.local_test_mode: bool                # 本地测试模式标志

# 数据库客户端
self.web_database_client: Optional[Any]   # Web数据库客户端
self.ts_database_client: Optional[Any]    # 时序数据库客户端

# 优化相关属性
self.optimization_interval_time: float    # 优化间隔时间
self.termination: Dict[str, Any]          # 优化算法终止条件
self.prediction_models: Dict[str, Any]    # 预测模型字典
self.optimization_function: str           # 优化函数类型
self.optimization_algorithm: str          # 优化算法类型
self.device_operation_flag: bool          # 设备运行标志位
self.constraint_table_data: List[Dict]    # 约束表数据
self.target_value: float                  # 目标值
self.optimization_variables_bounds: List[Tuple[float, float]]  # 优化变量边界
```

### 异步初始化和连接管理

#### async_database_connection

```python
async def async_database_connection(self) -> None:
    """异步建立数据库连接
    
    根据运行模式(本地测试或生产环境)异步创建并连接Web数据库和时序数据库客户端。
    在本地测试模式下,跳过实际的数据库连接。
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
```

#### async_check_database

```python
async def async_check_database(self) -> bool:
    """异步检查数据库连接状态
    
    检查Web数据库和时序数据库的连接状态。
    
    Returns:
        bool: 如果所有数据库连接正常返回 True,否则返回 False
    """
```

#### async_reconnect_database

```python
async def async_reconnect_database(self) -> None:
    """异步重新连接数据库
    
    当数据库连接断开时,尝试重新建立连接。
    
    Raises:
        Exception: 重新连接失败时抛出异常
    """
```

### 优化参数管理

#### get_optimization_parameter

```python
async def get_optimization_parameter(self) -> Dict[str, Any]:
    """异步获取项目的优化配置参数
    
    从Web数据库获取项目的优化配置参数。在本地测试模式下,返回默认参数。
    
    Returns:
        Dict[str, Any]: 优化配置参数字典,包含以下键:
            - optimization_interval_time: 优化间隔时间
            - optimization_algorithm: 优化算法类型
            - optimization_function_type: 优化函数类型
            - population_size: 种群大小
            - max_generations: 最大迭代次数
            - crossover_prob: 交叉概率
            - mutation_prob: 变异概率
            - device_operation_flag: 设备运行标志位
            - 其他优化相关参数
    
    Raises:
        Exception: 参数获取失败时抛出异常
    """
```

#### _set_optimization_interval_time

```python
def _set_optimization_interval_time(self, opt_params: Dict[str, Any]) -> None:
    """设置优化间隔时间
    
    Args:
        opt_params (Dict[str, Any]): 优化参数字典
    """
```

#### _set_termination

```python
def _set_termination(self, opt_params: Dict[str, Any]) -> None:
    """设置优化算法终止条件
    
    Args:
        opt_params (Dict[str, Any]): 优化参数字典
    """
```

### 模型和数据管理

#### get_model_data_and_build_models

```python
async def get_model_data_and_build_models(self) -> None:
    """异步获取模型数据并构建预测模型
    
    从数据库获取模型相关数据,包括模型文件路径、归一化参数等,
    然后构建预测模型实例。
    
    Raises:
        Exception: 模型构建失败时抛出异常
    """
```

#### get_history_data

```python
async def get_history_data(self) -> np.ndarray:
    """异步获取历史数据
    
    从时序数据库获取用于优化的历史数据。
    
    Returns:
        np.ndarray: 历史数据数组
    
    Raises:
        Exception: 数据获取失败时抛出异常
    """
```

### 优化问题构建和求解

#### _update_constraint_table

```python
async def _update_constraint_table(self, history_data: np.ndarray) -> None:
    """异步更新约束表
    
    根据历史数据和历史决策结果动态调整优化变量的上下限。
    
    Args:
        history_data (np.ndarray): 历史数据
    
    Raises:
        Exception: 约束表更新失败时抛出异常
    """
```

#### _update_target_value

```python
async def _update_target_value(self) -> None:
    """异步更新目标值
    
    根据项目名称从时序数据库获取最新的目标值。
    支持的项目类型:"水泥A磨"、"水泥B磨"、"水泥磨"等。
    
    Raises:
        Exception: 目标值更新失败时抛出异常
    """
```

#### _set_optimization_problem

```python
def _set_optimization_problem(self) -> Any:
    """构建优化问题实例
    
    根据优化函数类型(单目标、双目标、三目标、多目标)和约束条件
    构建相应的优化问题实例。
    
    Returns:
        Any: 优化问题实例
    
    Raises:
        ValueError: 不支持的优化函数类型
    """
```

#### _solve_optimization_problem

```python
def _solve_optimization_problem(self, problem: Any) -> Tuple[np.ndarray, np.ndarray]:
    """求解优化问题
    
    使用指定的优化算法求解优化问题,并选择最优解。
    
    Args:
        problem (Any): 优化问题实例
    
    Returns:
        Tuple[np.ndarray, np.ndarray]: (最优解, 目标函数值)
    
    Raises:
        Exception: 优化求解失败时抛出异常
    """
```

### 决策执行方法

#### decision_flag

```python
async def decision_flag(self) -> None:
    """带标志位的决策方法
    
    检查设备运行状态,如果设备运行则执行正常的优化决策流程,
    否则将决策值置零并写入数据库。
    
    决策流程:
    1. 检查设备运行状态
    2. 如果设备运行:
       - 获取历史数据
       - 更新约束表和目标值
       - 构建并求解优化问题
       - 写入优化结果
    3. 如果设备停止:
       - 将决策值置零
       - 写入零值结果
    
    Raises:
        Exception: 决策执行失败时抛出异常
    """
```

#### decision_no_flag

```python
async def decision_no_flag(self) -> None:
    """无标志位的决策方法
    
    直接执行优化决策流程,不检查设备运行状态。
    
    决策流程:
    1. 获取历史数据
    2. 更新约束表和目标值
    3. 构建并求解优化问题
    4. 写入优化结果
    
    Raises:
        Exception: 决策执行失败时抛出异常
    """
```

### 结果存储和资源管理

#### _write_decision_results

```python
async def _write_decision_results(
    self,
    optimization_solution: np.ndarray,
    objective_values: np.ndarray,
    device_status: bool = True
) -> None:
    """异步写入决策结果
    
    将优化解、目标函数值和设备运行状态写入时序数据库。
    
    Args:
        optimization_solution (np.ndarray): 优化解
        objective_values (np.ndarray): 目标函数值
        device_status (bool, 可选): 设备运行状态,默认为 True
    
    Raises:
        Exception: 结果写入失败时抛出异常
    """
```

#### clean_up

```python
async def clean_up(self) -> None:
    """清理资源
    
    关闭数据库连接,释放相关资源。
    """
```

### 主执行方法

#### main

```python
async def main(self) -> None:
    """异步主执行方法
    
    完整的决策执行流程:
    1. 建立数据库连接
    2. 获取优化参数
    3. 设置优化参数
    4. 构建预测模型
    5. 根据设备运行标志位选择执行决策方法
    6. 清理资源
    
    Raises:
        Exception: 执行过程中的任何异常
    """
```

## AsyncMultiOutputDecisionMaking 类

### 类定义

```python
class AsyncMultiOutputDecisionMaking(AsyncDecisionMaking):
    """异步多输出决策智能体类
    
    继承自 AsyncDecisionMaking,专门用于处理模型多输出场景。
    支持模型多输出时使用均值计算适应度函数。
    """
```

### 构造函数

```python
def __init__(
    self,
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False
) -> None:
```

**参数:**与 `AsyncDecisionMaking` 相同。

### 重写方法

#### _set_optimization_problem

```python
def _set_optimization_problem(self) -> Any:
    """构建多输出优化问题实例
    
    根据优化函数类型构建支持多输出的优化问题实例:
    - AsyncMultiOutputSingleObjectiveProblem: 多输出单目标优化
    - AsyncMultiOutputBiObjectiveProblem: 多输出双目标优化
    - AsyncMultiOutputTriObjectiveProblem: 多输出三目标优化
    - AsyncMultiOutputMOProblem: 多输出多目标优化
    
    Returns:
        Any: 多输出优化问题实例
    
    Raises:
        ValueError: 不支持的优化函数类型
    """
```

## 优化问题类

### AsyncMultiOutputSingleObjectiveProblem

```python
class AsyncMultiOutputSingleObjectiveProblem:
    """异步多输出单目标优化问题
    
    支持模型多输出时使用均值计算适应度函数的单目标优化问题。
    """
    
    def __init__(
        self,
        prediction_models: Dict[str, Any],
        history_data: np.ndarray,
        target_value: float,
        bounds: List[Tuple[float, float]]
    ) -> None:
        """初始化单目标优化问题
        
        Args:
            prediction_models (Dict[str, Any]): 预测模型字典
            history_data (np.ndarray): 历史数据
            target_value (float): 目标值
            bounds (List[Tuple[float, float]]): 变量边界
        """
    
    async def _evaluate(self, x: np.ndarray) -> float:
        """异步评估目标函数
        
        Args:
            x (np.ndarray): 决策变量
        
        Returns:
            float: 目标函数值
        """
```

### AsyncMultiOutputBiObjectiveProblem

```python
class AsyncMultiOutputBiObjectiveProblem:
    """异步多输出双目标优化问题
    
    支持模型多输出时使用均值计算适应度函数的双目标优化问题。
    自动识别并匹配"电耗"和"煤耗"目标。
    """
    
    def __init__(
        self,
        prediction_models: Dict[str, Any],
        history_data: np.ndarray,
        target_value: float,
        bounds: List[Tuple[float, float]]
    ) -> None:
        """初始化双目标优化问题
        
        Args:
            prediction_models (Dict[str, Any]): 预测模型字典
            history_data (np.ndarray): 历史数据
            target_value (float): 目标值
            bounds (List[Tuple[float, float]]): 变量边界
        """
    
    async def _evaluate(self, x: np.ndarray) -> Tuple[float, float]:
        """异步评估双目标函数
        
        Args:
            x (np.ndarray): 决策变量
        
        Returns:
            Tuple[float, float]: (电耗目标值, 煤耗目标值)
        """
```

### AsyncMultiOutputTriObjectiveProblem

```python
class AsyncMultiOutputTriObjectiveProblem:
    """异步多输出三目标优化问题
    
    支持模型多输出时使用均值计算适应度函数的三目标优化问题。
    自动识别并匹配"电耗"、"煤耗"和"质量"目标。
    """
    
    def __init__(
        self,
        prediction_models: Dict[str, Any],
        history_data: np.ndarray,
        target_value: float,
        bounds: List[Tuple[float, float]]
    ) -> None:
        """初始化三目标优化问题
        
        Args:
            prediction_models (Dict[str, Any]): 预测模型字典
            history_data (np.ndarray): 历史数据
            target_value (float): 目标值
            bounds (List[Tuple[float, float]]): 变量边界
        """
    
    async def _evaluate(self, x: np.ndarray) -> Tuple[float, float, float]:
        """异步评估三目标函数
        
        Args:
            x (np.ndarray): 决策变量
        
        Returns:
            Tuple[float, float, float]: (电耗目标值, 煤耗目标值, 质量目标值)
        """
```

### AsyncMultiOutputMOProblem

```python
class AsyncMultiOutputMOProblem:
    """异步多输出多目标优化问题
    
    支持模型多输出时使用均值计算适应度函数的多目标优化问题。
    自动识别并匹配"电耗"、"煤耗"、"质量"、"产量"、"水泥细度"等目标。
    """
    
    def __init__(
        self,
        prediction_models: Dict[str, Any],
        history_data: np.ndarray,
        target_value: float,
        bounds: List[Tuple[float, float]]
    ) -> None:
        """初始化多目标优化问题
        
        Args:
            prediction_models (Dict[str, Any]): 预测模型字典
            history_data (np.ndarray): 历史数据
            target_value (float): 目标值
            bounds (List[Tuple[float, float]]): 变量边界
        """
    
    async def _evaluate(self, x: np.ndarray) -> List[float]:
        """异步评估多目标函数
        
        Args:
            x (np.ndarray): 决策变量
        
        Returns:
            List[float]: 多个目标函数值列表
        """
```

## 工厂函数

### create_async_decision_agent

```python
def create_async_decision_agent(
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False,
    multi_output: bool = False
) -> Union[AsyncDecisionMaking, AsyncMultiOutputDecisionMaking]:
    """创建异步决策智能体实例的工厂函数
    
    Args:
        project_name (str): 项目名称
        dbconfig (Dict[str, Any]): 数据库配置
        local_test_mode (bool, 可选): 本地测试模式,默认为 False
        multi_output (bool, 可选): 是否使用多输出模型,默认为 False
    
    Returns:
        Union[AsyncDecisionMaking, AsyncMultiOutputDecisionMaking]: 决策智能体实例
    
    Example:
        >>> agent = create_async_decision_agent(
        ...     project_name="水泥A磨优化",
        ...     dbconfig=db_config,
        ...     multi_output=True
        ... )
    """
```

### run_async_decision_agent

```python
async def run_async_decision_agent(
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False,
    multi_output: bool = False
) -> None:
    """创建并运行异步决策智能体的便捷函数
    
    Args:
        project_name (str): 项目名称
        dbconfig (Dict[str, Any]): 数据库配置
        local_test_mode (bool, 可选): 本地测试模式,默认为 False
        multi_output (bool, 可选): 是否使用多输出模型,默认为 False
    
    Example:
        >>> await run_async_decision_agent(
        ...     project_name="水泥B磨优化",
        ...     dbconfig=db_config,
        ...     local_test_mode=False
        ... )
    """
```

## 异常类

### DecisionAgentError

```python
class DecisionAgentError(Exception):
    """决策智能体基础异常类"""
    pass
```

### DatabaseConnectionError

```python
class DatabaseConnectionError(DecisionAgentError):
    """数据库连接异常"""
    pass
```

### ModelLoadingError

```python
class ModelLoadingError(DecisionAgentError):
    """模型加载异常"""
    pass
```

### OptimizationError

```python
class OptimizationError(DecisionAgentError):
    """优化求解异常"""
    pass
```

### DataProcessingError

```python
class DataProcessingError(DecisionAgentError):
    """数据处理异常"""
    pass
```

## 类型定义

### 基础类型

```python
from typing import Dict, List, Tuple, Union, Optional, Any, Callable
import numpy as np

# 数据库配置类型
DatabaseConfig = Dict[str, Dict[str, Union[str, int]]]

# 优化参数类型
OptimizationParams = Dict[str, Any]

# 模型字典类型
ModelDict = Dict[str, Any]

# 约束边界类型
Bounds = List[Tuple[float, float]]

# 优化解类型
Solution = np.ndarray

# 目标函数值类型
ObjectiveValues = Union[float, Tuple[float, ...], List[float]]
```

### 配置类型

```python
# 终止条件配置
TerminationConfig = Dict[str, Union[int, float, bool]]

# 约束表数据类型
ConstraintTableData = List[Dict[str, Union[str, float, int]]]

# 历史数据类型
HistoryData = np.ndarray

# 设备状态类型
DeviceStatus = bool
```

## 常量定义

### 优化算法类型

```python
OPTIMIZATION_ALGORITHMS = {
    "GA": "遗传算法",
    "NSGA2": "非支配排序遗传算法II",
    "NSGA3": "非支配排序遗传算法III",
    "MOEAD": "基于分解的多目标进化算法",
    "PSO": "粒子群优化算法",
    "DE": "差分进化算法"
}
```

### 优化函数类型

```python
OPTIMIZATION_FUNCTION_TYPES = {
    "single_objective": "单目标优化",
    "dual_objective": "双目标优化",
    "tri_objective": "三目标优化",
    "multi_objective": "多目标优化"
}
```

### 项目类型

```python
PROJECT_TYPES = {
    "水泥A磨": "cement_mill_a",
    "水泥B磨": "cement_mill_b",
    "水泥磨": "cement_mill",
    "电力系统": "power_system",
    "化工生产": "chemical_production"
}
```

### 默认配置

```python
DEFAULT_OPTIMIZATION_PARAMS = {
    "optimization_interval_time": 300.0,      # 5分钟
    "optimization_algorithm": "GA",
    "optimization_function_type": "single_objective",
    "population_size": 100,
    "max_generations": 50,
    "crossover_prob": 0.8,
    "mutation_prob": 0.1,
    "device_operation_flag": True,
    "target_value": 350.0
}

DEFAULT_TERMINATION = {
    "max_generations": 50,
    "tolerance": 1e-6,
    "max_time": 300.0,  # 5分钟
    "convergence_check": True
}

DEFAULT_BOUNDS = [
    (0.0, 100.0),  # 变量1边界
    (0.0, 100.0),  # 变量2边界
    (0.0, 100.0),  # 变量3边界
    # ... 更多变量边界
]
```

### 数据库表名

```python
DATABASE_TABLES = {
    "optimization_params": "optimization_parameters",
    "model_data": "model_information",
    "constraint_table": "optimization_constraints",
    "history_data": "time_series_data",
    "decision_results": "optimization_results",
    "device_status": "device_operation_status"
}
```

### 模型输出目标映射

```python
MODEL_OUTPUT_TARGETS = {
    "电耗": "power_consumption",
    "煤耗": "coal_consumption",
    "质量": "quality",
    "产量": "production",
    "水泥细度": "cement_fineness",
    "比表面积": "specific_surface_area",
    "强度": "strength"
}
```

## 使用示例

### 基础使用

```python
import asyncio
from industrytslib.core_aysnc.async_decision_agents import AsyncDecisionMaking

async def basic_example():
    # 数据库配置
    dbconfig = {
        "web_database": {
            "host": "localhost",
            "port": 1433,
            "database": "IndustryDB",
            "username": "admin",
            "password": "password123",
            "driver": "ODBC Driver 17 for SQL Server"
        },
        "ts_database": {
            "host": "localhost",
            "port": 1433,
            "database": "TimeSeriesDB",
            "username": "admin",
            "password": "password123",
            "driver": "ODBC Driver 17 for SQL Server"
        }
    }
    
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="API测试项目",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行决策
        await agent.main()
        print("决策执行成功")
    except Exception as e:
        print(f"决策执行失败: {e}")
    finally:
        # 清理资源
        await agent.clean_up()

# 运行示例
asyncio.run(basic_example())
```

### 多输出模型使用

```python
from industrytslib.core_aysnc.async_decision_agents import AsyncMultiOutputDecisionMaking

async def multi_output_example():
    # 创建多输出决策智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="多输出测试项目",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        await agent.main()
        print("多输出决策执行成功")
    except Exception as e:
        print(f"多输出决策执行失败: {e}")
    finally:
        await agent.clean_up()

asyncio.run(multi_output_example())
```

### 工厂函数使用

```python
from industrytslib.core_aysnc.async_decision_agents import (
    create_async_decision_agent,
    run_async_decision_agent
)

async def factory_example():
    # 使用工厂函数创建智能体
    agent = create_async_decision_agent(
        project_name="工厂函数测试",
        dbconfig=dbconfig,
        multi_output=True
    )
    
    try:
        await agent.main()
    finally:
        await agent.clean_up()
    
    # 或者直接运行
    await run_async_decision_agent(
        project_name="直接运行测试",
        dbconfig=dbconfig,
        local_test_mode=True
    )

asyncio.run(factory_example())
```

## 注意事项

### 🔧 配置要求
- 确保数据库配置正确,包括主机地址、端口、用户名和密码
- 在生产环境中使用环境变量管理敏感配置信息
- 本地测试模式下可以跳过数据库连接,使用模拟数据

### 📊 性能考虑
- 优化算法的种群大小和迭代次数会影响执行时间
- 多输出模型的计算复杂度较高,建议合理设置超时时间
- 大量历史数据可能影响内存使用,建议定期清理

### 🛡️ 安全性
- 使用参数化查询防止SQL注入
- 敏感配置信息应加密存储
- 定期更新数据库连接密码

### 🔄 资源管理
- 始终在 `finally` 块中调用 `clean_up()` 方法
- 避免创建过多并发的决策智能体实例
- 监控内存和CPU使用情况

### ⚠️ 异常处理
- 捕获并处理特定的异常类型
- 记录详细的错误日志用于调试
- 实现重试机制处理临时性故障

---

> **提示**:本API参考文档基于当前版本的代码结构。在使用过程中,请参考最新的代码实现和文档更新。如有疑问,请查看源代码或联系开发团队。