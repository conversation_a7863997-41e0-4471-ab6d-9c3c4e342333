# AsyncDecisionMaking - 基础异步决策智能体

`AsyncDecisionMaking` 是异步决策智能体模块的核心类,继承自 `AsyncScheduledTask`,提供完整的异步优化决策功能。该类专门设计用于工业生产环境中的实时优化决策,支持多目标优化、设备状态监控、模型预测和决策结果存储等功能。

## 概述

`AsyncDecisionMaking` 类实现了一个完整的异步优化决策流程,从数据获取、模型构建、优化求解到结果存储,所有操作都采用异步方式执行,确保系统的高效性和响应性。

### 主要特性
- **异步数据库连接管理**:支持Web数据库和时序数据库的异步连接
- **智能优化求解**:集成多种优化算法和目标函数
- **设备状态感知**:实时监控设备运行状态,支持带标志位和无标志位决策
- **模型集成**:无缝集成机器学习预测模型
- **结果持久化**:异步写入优化结果到数据库
- **故障容错**:完善的异常处理和资源管理

## 初始化参数

### 构造函数
```python
def __init__(
    self,
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False,
    **kwargs,
) -> None
```

### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_name` | `str` | ✅ | - | 项目名称,用于标识决策任务和数据库表 |
| `dbconfig` | `Dict[str, Any]` | ✅ | - | 数据库配置字典,包含Web和时序数据库连接信息 |
| `local_test_mode` | `bool` | ❌ | `False` | 本地测试模式,True时使用离线模式 |
| `**kwargs` | `Any` | ❌ | - | 其他初始化参数 |

### 数据库配置示例
```python
dbconfig = {
    "web_database": {
        "host": "localhost",
        "port": 1433,
        "database": "IndustryDB",
        "username": "user",
        "password": "password",
        "driver": "ODBC Driver 17 for SQL Server"
    },
    "ts_database": {
        "host": "localhost",
        "port": 1433, 
        "database": "TimeSeriesDB",
        "username": "user",
        "password": "password",
        "driver": "ODBC Driver 17 for SQL Server"
    }
}
```

## 核心属性

### 项目信息
- **`project_name`**: 项目名称,用于数据库表识别
- **`local_test_mode`**: 本地测试模式标志
- **`logger_decision`**: 决策专用日志记录器

### 数据库客户端
- **`web_mssql_client`**: Web数据库异步客户端
- **`ts_mssql_client`**: 时序数据库异步客户端
- **`dbconfig`**: 数据库配置信息

### 优化相关属性
- **`optimization_interval_time`**: 优化间隔时间(秒)
- **`termination`**: 优化算法终止条件
- **`prediction_models`**: 预测模型列表
- **`optimization_function`**: 优化函数类型
- **`optimization_algorithm`**: 优化算法类型(如"GA", "NSGA2")
- **`device_operation_flag`**: 设备运行标志位
- **`constraint_table`**: 约束表数据
- **`function_parameter`**: 目标函数参数
- **`optimization_variables`**: 优化变量名列表
- **`variable_lower_bounds`**: 变量下界
- **`variable_upper_bounds`**: 变量上界
- **`history_data`**: 历史数据

## 核心方法

### 异步初始化和连接管理

#### `async_database_connection()`
```python
async def async_database_connection(self) -> None
```
异步创建并连接数据库客户端。根据运行模式选择性地创建Web数据库和时序数据库连接。

**功能**:
- 根据 `local_test_mode` 决定是否创建数据库连接
- 异步初始化Web数据库客户端
- 异步初始化时序数据库客户端
- 记录连接状态和错误信息

#### `async_check_database()`
```python
async def async_check_database(self) -> bool
```
异步检查数据库连接状态。

**返回值**:
- `bool`: 数据库连接是否正常

#### `async_reconnect_database()`
```python
async def async_reconnect_database(self) -> None
```
异步重新连接数据库。当检测到连接异常时自动重连。

### 优化参数管理

#### `get_optimization_parameter()`
```python
async def get_optimization_parameter(self) -> Dict[str, Any]
```
异步获取项目的优化配置参数。

**返回值**:
- `Dict[str, Any]`: 优化参数字典,包含算法配置、目标函数参数等

**支持的参数**:
- `optimization_function_type`: 优化函数类型
- `optimization_algorithm`: 优化算法类型
- `population_size`: 种群大小
- `max_generations`: 最大迭代次数
- `device_operation_flag`: 设备运行标志位

#### `_set_optimization_interval_time()`
```python
def _set_optimization_interval_time(self, optimization_parameter: Dict[str, Any]) -> None
```
设置优化间隔时间。

#### `_set_termination()`
```python
def _set_termination(self, optimization_parameter: Dict[str, Any]) -> None
```
设置优化算法终止条件。

### 模型和数据管理

#### `get_model_data_and_build_models()`
```python
async def get_model_data_and_build_models(self) -> None
```
异步获取模型数据并构建预测模型。

**功能**:
- 从数据库获取模型配置信息
- 加载预训练模型文件
- 构建模型列表用于优化
- 处理模型加载异常

#### `get_history_data()`
```python
async def get_history_data(self, hours: int = 24) -> pl.DataFrame
```
异步获取指定时间范围的历史数据。

**参数**:
- `hours`: 获取数据的时间范围(小时)

**返回值**:
- `pl.DataFrame`: 历史数据DataFrame

### 优化问题构建和求解

#### `_update_constraint_table()`
```python
async def _update_constraint_table(self, history_data: pl.DataFrame) -> None
```
异步更新约束表。根据历史数据和历史决策结果动态调整优化变量的上下限。

#### `_update_target_value()`
```python
async def _update_target_value(self) -> None
```
异步更新目标值。根据项目类型从时序数据库获取最新的目标值。

**支持的项目类型**:
- 水泥A磨:获取A磨目标值
- 水泥B磨:获取B磨目标值
- 水泥磨:获取水泥磨目标值
- 其他:使用默认值350.0

#### `_set_optimization_problem()`
```python
def _set_optimization_problem(
    self, optimization_parameter: Dict[str, Any]
) -> Union[SingleObjectiveIndustryProblem, BiObjectiveIndustryProblem, TriObjectiveIndustryProblem, MOIndustryProblem]
```
根据优化函数类型构建相应的优化问题实例。

**支持的优化问题类型**:
- `single_objective`: 单目标优化
- `dual_objective`: 双目标优化
- `triple_objective`: 三目标优化
- `multi_objective`: 多目标优化

#### `_solve_optimization_problem()`
```python
def _solve_optimization_problem(
    self, optimization_parameter: Dict[str, Any]
) -> Tuple[np.ndarray, float]
```
求解优化问题。

**参数**:
- `optimization_parameter`: 优化参数字典

**返回值**:
- `Tuple[np.ndarray, float]`: (最优解向量, 最优目标值)

**支持的优化算法**:
- `GA`: 遗传算法
- `NSGA2`: 非支配排序遗传算法II

### 决策执行方法

#### `decision_flag()`
```python
async def decision_flag(self) -> None
```
异步带标志位的决策方法。检查设备运行状态,如果设备运行正常则执行优化决策,否则将决策值置为0。

**执行流程**:
1. 检查设备运行状态
2. 如果设备运行:
   - 获取历史数据
   - 更新约束表和目标值
   - 执行优化求解
   - 写入优化结果
3. 如果设备停机:
   - 决策值置为0
   - 写入零值结果

#### `decision_no_flag()`
```python
async def decision_no_flag(self) -> None
```
异步无标志位的决策方法。直接执行优化决策流程,不检查设备运行状态。

**执行流程**:
1. 获取历史数据
2. 更新约束表和目标值
3. 执行优化求解
4. 写入优化结果

#### `main()`
```python
async def main(self) -> None
```
异步主执行方法。完整的决策流程入口。

**执行流程**:
1. 建立数据库连接
2. 获取优化参数
3. 设置优化参数(间隔时间、终止条件)
4. 构建预测模型
5. 根据设备运行标志位选择执行 `decision_flag` 或 `decision_no_flag`
6. 清理资源

### 结果存储和资源管理

#### `_write_decision_results()`
```python
async def _write_decision_results(
    self,
    solution: np.ndarray,
    objective_value: float,
    device_running: bool,
) -> None
```
异步写入决策结果到数据库。

**参数**:
- `solution`: 优化解向量
- `objective_value`: 目标函数值
- `device_running`: 设备运行状态

**写入内容**:
- 时间戳
- 项目名称
- 目标函数值
- 设备运行状态
- 各决策变量的值

#### `clean_up()`
```python
async def clean_up(self) -> None
```
异步清理任务。关闭数据库连接、清理资源等。

## 使用示例

### 基础使用
```python
import asyncio
from industrytslib.core_aysnc.async_decision_agents import AsyncDecisionMaking

async def basic_decision_example():
    """基础决策智能体使用示例"""
    
    # 数据库配置
    dbconfig = {
        "web_database": {
            "host": "*************",
            "port": 1433,
            "database": "IndustryDB",
            "username": "admin",
            "password": "password123"
        },
        "ts_database": {
            "host": "*************",
            "port": 1433,
            "database": "TimeSeriesDB", 
            "username": "admin",
            "password": "password123"
        }
    }
    
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="水泥A磨优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行决策任务
        await agent.main()
        print("决策任务执行完成")
    except Exception as e:
        print(f"决策任务执行失败: {e}")
    finally:
        # 清理资源
        await agent.clean_up()

# 运行示例
asyncio.run(basic_decision_example())
```

### 本地测试模式
```python
async def local_test_example():
    """本地测试模式示例"""
    
    # 本地测试不需要真实数据库配置
    dbconfig = {}
    
    # 创建测试智能体
    agent = AsyncDecisionMaking(
        project_name="测试项目",
        dbconfig=dbconfig,
        local_test_mode=True  # 启用本地测试模式
    )
    
    try:
        await agent.main()
        print("本地测试完成")
    except Exception as e:
        print(f"本地测试失败: {e}")

asyncio.run(local_test_example())
```

### 自定义优化参数
```python
async def custom_optimization_example():
    """自定义优化参数示例"""
    
    agent = AsyncDecisionMaking(
        project_name="自定义优化项目",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    # 建立数据库连接
    await agent.async_database_connection()
    
    # 获取并修改优化参数
    opt_params = await agent.get_optimization_parameter()
    opt_params.update({
        "optimization_algorithm": "NSGA2",
        "population_size": 200,
        "max_generations": 100,
        "optimization_function_type": "dual_objective"
    })
    
    # 设置优化参数
    agent._set_optimization_interval_time(opt_params)
    agent._set_termination(opt_params)
    
    # 构建模型
    await agent.get_model_data_and_build_models()
    
    # 执行决策
    if opt_params.get("device_operation_flag"):
        await agent.decision_flag()
    else:
        await agent.decision_no_flag()
    
    # 清理资源
    await agent.clean_up()

asyncio.run(custom_optimization_example())
```

### 并发决策任务
```python
async def concurrent_decision_example():
    """并发决策任务示例"""
    
    projects = ["水泥A磨优化", "水泥B磨优化", "煤磨优化"]
    
    async def run_single_decision(project_name):
        """运行单个决策任务"""
        agent = AsyncDecisionMaking(
            project_name=project_name,
            dbconfig=dbconfig,
            local_test_mode=False
        )
        
        try:
            await agent.main()
            print(f"{project_name} 决策完成")
        except Exception as e:
            print(f"{project_name} 决策失败: {e}")
        finally:
            await agent.clean_up()
    
    # 并发执行多个决策任务
    tasks = [run_single_decision(project) for project in projects]
    await asyncio.gather(*tasks)
    
    print("所有决策任务完成")

asyncio.run(concurrent_decision_example())
```

## 最佳实践

### 🔧 配置管理
```python
# 使用环境变量管理敏感信息
import os

dbconfig = {
    "web_database": {
        "host": os.getenv("WEB_DB_HOST", "localhost"),
        "port": int(os.getenv("WEB_DB_PORT", "1433")),
        "database": os.getenv("WEB_DB_NAME", "IndustryDB"),
        "username": os.getenv("WEB_DB_USER", "admin"),
        "password": os.getenv("WEB_DB_PASSWORD", "")
    }
}
```

### 📊 错误处理和日志
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def robust_decision_example():
    """健壮的决策执行示例"""
    agent = None
    try:
        agent = AsyncDecisionMaking(
            project_name="生产优化",
            dbconfig=dbconfig,
            local_test_mode=False
        )
        
        # 检查数据库连接
        await agent.async_database_connection()
        if not await agent.async_check_database():
            await agent.async_reconnect_database()
        
        # 执行决策
        await agent.main()
        
    except Exception as e:
        logging.error(f"决策执行失败: {e}")
        # 可以添加告警通知逻辑
    finally:
        if agent:
            await agent.clean_up()
```

### ⚡ 性能优化
```python
async def optimized_decision_example():
    """性能优化示例"""
    
    # 使用连接池减少连接开销
    dbconfig_optimized = {
        "web_database": {
            **dbconfig["web_database"],
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30
        }
    }
    
    agent = AsyncDecisionMaking(
        project_name="高性能优化",
        dbconfig=dbconfig_optimized,
        local_test_mode=False
    )
    
    # 预热连接
    await agent.async_database_connection()
    
    # 批量执行决策
    for i in range(10):
        try:
            await agent.decision_no_flag()
            await asyncio.sleep(1)  # 控制执行频率
        except Exception as e:
            logging.warning(f"第{i+1}次决策失败: {e}")
    
    await agent.clean_up()
```

## 注意事项

### ⚠️ 数据库连接管理
- 确保数据库服务器支持异步连接
- 合理设置连接超时和重试参数
- 在生产环境中使用连接池
- 及时关闭连接避免资源泄露

### 🔄 模型管理
- 确保模型文件路径正确且可访问
- 定期验证模型预测精度
- 考虑模型热重载机制
- 处理模型加载失败的情况

### 📈 性能考虑
- 合理设置优化算法参数(种群大小、迭代次数)
- 监控决策执行时间,避免超时
- 考虑使用缓存减少重复计算
- 在高并发场景下注意资源限制

### 🛡️ 安全性
- 不要在代码中硬编码数据库密码
- 使用加密连接传输敏感数据
- 定期更新数据库访问凭据
- 实施适当的访问控制和审计

## 相关文档

- [异步决策智能体概述](./async_decision_overview.md) - 模块整体介绍
- [多输出异步决策智能体](./async_multi_output_decision_agent.md) - AsyncMultiOutputDecisionMaking 详细文档
- [使用示例](./usage_examples.md) - 更多使用示例和最佳实践
- [API参考](./api_reference.md) - 完整的API参考文档

---

> **提示**:在使用 `AsyncDecisionMaking` 时,建议先在本地测试模式下验证配置和逻辑,然后再部署到生产环境。确保数据库连接稳定,模型文件完整,优化参数合理。