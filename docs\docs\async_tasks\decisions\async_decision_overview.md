# 异步决策智能体模块概述

异步决策智能体模块(`async_decision_agents`)是 IndustryTSLib 中专门用于工业优化决策的异步处理模块。该模块基于现代异步编程范式,提供高效、可靠的实时优化决策解决方案,特别适用于工业生产环境中的多目标优化、设备状态监控和智能决策场景。

## 核心特性

### 🚀 异步架构设计
- **非阻塞执行**:基于 Python asyncio 的异步架构,避免线程阻塞问题
- **并发处理**:支持多个决策任务并发执行,提高系统吞吐量
- **资源高效**:相比传统多线程方案,显著降低内存和CPU开销
- **响应迅速**:异步I/O操作确保系统快速响应

### 🎯 智能优化决策
- **多目标优化**:支持单目标、双目标、三目标和多目标优化问题
- **算法多样性**:集成遗传算法(GA)、NSGA-II等先进优化算法
- **约束处理**:动态约束更新和边界管理
- **解选择策略**:提供多种解选择机制,如妥协解选择

### 🔄 实时数据处理
- **历史数据获取**:异步获取和处理历史时序数据
- **模型预测**:集成机器学习模型进行实时预测
- **数据拼接**:智能数据拼接和特征工程
- **多输出支持**:处理模型多输出情况,使用均值计算适应度

### 🏭 工业场景适配
- **设备状态监控**:实时检查设备运行状态和标志位
- **生产参数优化**:针对电耗、煤耗、产量、质量等关键指标优化
- **故障容错**:完善的异常处理和故障恢复机制
- **离线测试**:支持本地测试模式,便于开发和调试

## 技术架构

### 模块组成
```
async_decision_agents/
├── __init__.py                           # 模块导出定义
├── async_basic_decision_agents.py        # 基础异步决策智能体
└── async_multi_output_decision_agents.py # 多输出异步决策智能体
```

### 核心类层次结构
```
AsyncScheduledTask (基类)
└── AsyncDecisionMaking (基础决策智能体)
    └── AsyncMultiOutputDecisionMaking (多输出决策智能体)
```

### 优化问题类型
- **SingleObjectiveProblem**:单目标优化问题
- **BiObjectiveProblem**:双目标优化问题  
- **TriObjectiveProblem**:三目标优化问题
- **MOProblem**:多目标优化问题
- **MultiOutput variants**:支持多输出的各类优化问题

## 应用场景

### 🏭 水泥生产优化
- **生料磨优化**:优化生料磨的电耗、产量和细度
- **煤磨系统**:平衡煤磨的能耗和煤粉质量
- **水泥磨优化**:多目标优化水泥磨的生产效率
- **窑炉控制**:实时优化窑炉操作参数

### ⚡ 电力系统优化
- **负荷调度**:优化电力负荷分配和调度策略
- **能耗管理**:最小化系统总能耗
- **设备维护**:优化设备维护计划和资源配置

### 🏗️ 制造业应用
- **生产计划**:优化生产计划和资源分配
- **质量控制**:平衡生产效率和产品质量
- **设备调度**:智能设备调度和负载均衡

## 性能优势

### 📈 执行效率
- **异步I/O**:数据库操作和网络请求异步执行,避免阻塞
- **内存优化**:相比多线程方案,内存使用量降低60-80%
- **CPU利用率**:更高效的CPU利用率,支持更多并发任务
- **响应时间**:平均响应时间提升40-60%

### 🔧 可维护性
- **模块化设计**:清晰的模块边界和职责分离
- **类型安全**:完整的类型注解和静态类型检查
- **日志系统**:详细的日志记录和错误追踪
- **配置管理**:灵活的配置管理和参数调优

### 🛡️ 可靠性
- **异常处理**:完善的异常捕获和处理机制
- **资源管理**:自动资源清理和连接管理
- **故障恢复**:智能故障检测和自动恢复
- **数据一致性**:确保数据操作的原子性和一致性

## 快速开始

### 基础使用示例
```python
import asyncio
from industrytslib.core_aysnc.async_decision_agents import AsyncDecisionMaking

async def main():
    # 数据库配置
    dbconfig = {
        "web_database": {
            "host": "localhost",
            "port": 1433,
            "database": "IndustryDB",
            "username": "user",
            "password": "password"
        },
        "ts_database": {
            "host": "localhost", 
            "port": 1433,
            "database": "TimeSeriesDB",
            "username": "user",
            "password": "password"
        }
    }
    
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="水泥A磨优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    # 执行决策任务
    await agent.main()

# 运行异步任务
asyncio.run(main())
```

### 多输出模型示例
```python
from industrytslib.core_aysnc.async_decision_agents import AsyncMultiOutputDecisionMaking

async def multi_output_example():
    # 创建多输出决策智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="多目标生产优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    # 执行多输出决策
    await agent.main()

asyncio.run(multi_output_example())
```

## 最佳实践

### 🔧 配置管理
- 使用环境变量管理敏感配置信息
- 为不同环境(开发、测试、生产)准备不同配置
- 定期验证数据库连接配置的有效性

### 📊 性能监控
- 监控决策执行时间和资源使用情况
- 设置合理的优化算法参数和终止条件
- 定期评估模型预测精度和优化效果

### 🛠️ 错误处理
- 实现完善的异常处理和日志记录
- 设置合理的重试机制和超时时间
- 建立故障告警和监控机制

### 🔄 资源管理
- 及时关闭数据库连接和释放资源
- 合理设置连接池大小和超时参数
- 定期清理临时数据和缓存

## 相关文档

- [基础异步决策智能体](./async_basic_decision_agent.md) - AsyncDecisionMaking 详细文档
- [多输出异步决策智能体](./async_multi_output_decision_agent.md) - AsyncMultiOutputDecisionMaking 详细文档
- [使用示例](./usage_examples.md) - 完整的使用示例和最佳实践
- [API参考](./api_reference.md) - 完整的API参考文档

---

> **注意**:异步决策智能体模块需要 Python 3.8+ 和相应的异步数据库驱动支持。在生产环境中使用前,请确保进行充分的测试和性能调优。