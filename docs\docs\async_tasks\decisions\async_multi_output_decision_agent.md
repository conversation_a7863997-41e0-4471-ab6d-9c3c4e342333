# AsyncMultiOutputDecisionMaking - 多输出异步决策智能体

`AsyncMultiOutputDecisionMaking` 是基于 `AsyncDecisionMaking` 的增强版异步决策智能体,专门设计用于处理模型多输出场景。当机器学习模型产生多个输出时,该类能够智能地使用均值计算适应度函数,确保优化过程的稳定性和有效性。

## 概述

在工业生产环境中,许多机器学习模型(如深度神经网络、集成模型)会产生多个输出值。传统的优化方法往往难以有效处理这种多输出情况。`AsyncMultiOutputDecisionMaking` 通过以下方式解决这个问题:

- **多输出检测**:自动识别模型输出的维度和结构
- **均值计算**:对多输出结果计算均值,提供稳定的适应度值
- **兼容性保持**:完全兼容单输出模型,无需修改现有代码
- **异步处理**:继承所有异步处理优势,确保高性能

### 主要特性
- **继承完整功能**:继承 `AsyncDecisionMaking` 的所有功能和特性
- **多输出支持**:智能处理模型多输出情况
- **均值计算**:使用均值策略计算适应度函数
- **自动适配**:自动适配不同的输出维度和结构
- **稳定优化**:提供更稳定的优化过程和结果

## 初始化参数

### 构造函数
```python
def __init__(
    self,
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False,
    **kwargs,
) -> None
```

### 参数说明

参数配置与 `AsyncDecisionMaking` 完全相同:

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_name` | `str` | ✅ | - | 项目名称,用于标识决策任务和数据库表 |
| `dbconfig` | `Dict[str, Any]` | ✅ | - | 数据库配置字典,包含Web和时序数据库连接信息 |
| `local_test_mode` | `bool` | ❌ | `False` | 本地测试模式,True时使用离线模式 |
| `**kwargs` | `Any` | ❌ | - | 其他初始化参数 |

## 核心功能

### 多输出优化问题类

`AsyncMultiOutputDecisionMaking` 重写了 `_set_optimization_problem` 方法,创建支持多输出的优化问题类:

#### 支持的优化问题类型

1. **AsyncMultiOutputSingleObjectiveProblem** - 多输出单目标优化
2. **AsyncMultiOutputBiObjectiveProblem** - 多输出双目标优化
3. **AsyncMultiOutputTriObjectiveProblem** - 多输出三目标优化
4. **AsyncMultiOutputMOProblem** - 多输出多目标优化

### 多输出处理机制

#### 输出维度检测
```python
# 检查是否为多输出
if pred_numpy.ndim > 2 or (pred_numpy.ndim == 2 and pred_numpy.shape[1] > 1):
    # 多输出情况:使用均值
    pred_mean = np.mean(pred_numpy)
    processed_predictions.append(pred_mean)
else:
    # 单输出情况:保持原有逻辑
    # ... 单输出处理逻辑
```

#### 均值计算策略
- **多维输出**:对所有输出维度计算均值
- **批次输出**:对批次维度计算均值
- **时序输出**:对时间序列维度计算均值
- **混合输出**:对复合维度结构计算全局均值

## 优化问题类详解

### AsyncMultiOutputSingleObjectiveProblem

单目标多输出优化问题,适用于只有一个优化目标的场景。

```python
class AsyncMultiOutputSingleObjectiveProblem(SingleObjectiveIndustryProblem):
    """支持多输出的异步单目标优化问题"""
    
    def _evaluate(self, x, out, *args, **kwargs):
        # 多输出模型预测和均值计算
        # 构建目标字典
        # 计算单目标函数值
```

**适用场景**:
- 最小化能耗
- 最大化产量
- 最小化成本
- 最大化效率

### AsyncMultiOutputBiObjectiveProblem

双目标多输出优化问题,适用于需要平衡两个目标的场景。

```python
class AsyncMultiOutputBiObjectiveProblem(BiObjectiveIndustryProblem):
    """支持多输出的异步双目标优化问题"""
    
    def _evaluate(self, x, out, *args, **kwargs):
        # 多输出模型预测和均值计算
        # 构建目标字典
        # 计算双目标函数值
```

**适用场景**:
- 能耗与产量平衡
- 成本与质量平衡
- 效率与稳定性平衡
- 速度与精度平衡

### AsyncMultiOutputTriObjectiveProblem

三目标多输出优化问题,专门用于水泥生产等复杂工业场景。

```python
class AsyncMultiOutputTriObjectiveProblem(TriObjectiveIndustryProblem):
    """支持多输出的异步三目标水泥优化问题"""
    
    def _evaluate(self, x, out, *args, **kwargs):
        # 多输出模型预测和均值计算
        # 构建目标字典(包含水泥细度)
        # 计算三目标函数值
```

**适用场景**:
- 水泥生产优化(电耗、产量、细度)
- 钢铁生产优化(能耗、产量、质量)
- 化工过程优化(成本、产量、安全)

### AsyncMultiOutputMOProblem

多目标多输出优化问题,支持任意数量的优化目标。

```python
class AsyncMultiOutputMOProblem(MOIndustryProblem):
    """支持多输出的异步多目标优化问题"""
    
    def _evaluate(self, x, out, *args, **kwargs):
        # 多输出模型预测和均值计算
        # 构建目标字典
        # 计算多目标函数值
```

**适用场景**:
- 复杂制造系统优化
- 供应链优化
- 资源配置优化
- 多约束生产计划

## 模型输出处理流程

### 1. 数据预处理
```python
# 将决策变量转换为DataFrame格式
x_df = pl.DataFrame(x[np.newaxis, :])
x_df.columns = self.name_list

# 数据拼接(如果有历史数据)
if len(self.history_data) > i and self.history_data[i] is not None:
    data_joint = data_joint_decision(
        self.history_data[i], x_df, self.decision_length
    )
```

### 2. 模型预测
```python
# 模型预测处理
for i, model in enumerate(self.model_list):
    try:
        if hasattr(model, '__call__'):
            model_input = torch.tensor(prediction_data[i]).float()
            if model_input.dim() == 2 and model_input.size(0) == 1:
                model_input = model_input.unsqueeze(0)  # 添加batch维度
            
            model_output = model(model_input)
            predictions.append(model_output)
    except Exception as e:
        # 异常处理:使用默认值
        predictions.append(torch.tensor([[1.0]]))
```

### 3. 多输出处理
```python
# 多输出检测和处理
for i, prediction in enumerate(predictions):
    try:
        pred_numpy = prediction.detach().numpy()
        
        # 检查是否为多输出
        if pred_numpy.ndim > 2 or (pred_numpy.ndim == 2 and pred_numpy.shape[1] > 1):
            # 多输出情况:使用均值
            pred_mean = np.mean(pred_numpy)
            processed_predictions.append(pred_mean)
        else:
            # 单输出情况:保持原有逻辑
            if pred_numpy.ndim == 3:
                processed_predictions.append(pred_numpy[0][0][0])
            elif pred_numpy.ndim == 2:
                processed_predictions.append(pred_numpy[0][0])
            else:
                processed_predictions.append(pred_numpy[0])
    except Exception as e:
        # 降级处理:使用默认值
        processed_predictions.append(1.0)
```

### 4. 目标值映射
```python
# 构建目标字典
obj_dict = {"电耗": 1.0, "煤耗": 1.0, "质量": 1.0, "产量": 1.0}

# 自动识别机制
for i, prediction_value in enumerate(processed_predictions):
    model_name = self.model_name_list[i] if i < len(self.model_name_list) else f"model_{i}"
    
    # 关键词匹配
    for keyword in ["电耗", "煤耗", "产量"]:
        if keyword in model_name:
            obj_dict[keyword] = prediction_value
    
    # 质量相关指标
    if any(keyword in model_name for keyword in ["fcao", "比表面积", "生料细度"]):
        obj_dict["质量"] = prediction_value
```

## 使用示例

### 基础多输出决策
```python
import asyncio
import torch
import numpy as np
from industrytslib.core_aysnc.async_decision_agents import AsyncMultiOutputDecisionMaking

# 模拟多输出模型
class MultiOutputModel(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.linear1 = torch.nn.Linear(10, 20)
        self.linear2 = torch.nn.Linear(20, 5)  # 5个输出
    
    def forward(self, x):
        x = torch.relu(self.linear1(x))
        return self.linear2(x)  # 返回5维输出

async def multi_output_decision_example():
    """多输出决策示例"""
    
    # 数据库配置
    dbconfig = {
        "web_database": {
            "host": "localhost",
            "port": 1433,
            "database": "IndustryDB",
            "username": "admin",
            "password": "password123"
        },
        "ts_database": {
            "host": "localhost",
            "port": 1433,
            "database": "TimeSeriesDB",
            "username": "admin",
            "password": "password123"
        }
    }
    
    # 创建多输出决策智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="多输出生产优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行多输出决策任务
        await agent.main()
        print("多输出决策任务执行完成")
    except Exception as e:
        print(f"多输出决策任务执行失败: {e}")
    finally:
        await agent.clean_up()

# 运行示例
asyncio.run(multi_output_decision_example())
```

### 本地测试多输出模型
```python
async def local_multi_output_test():
    """本地多输出模型测试"""
    
    # 创建测试智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="多输出测试",
        dbconfig={},
        local_test_mode=True
    )
    
    # 模拟多输出模型
    def mock_multi_output_model(x):
        """模拟多输出模型"""
        # 返回3x2的输出矩阵
        return torch.randn(1, 3, 2)
    
    # 设置模拟模型
    agent.prediction_models = [mock_multi_output_model]
    agent.model_name_list = ["电耗预测模型"]
    
    try:
        await agent.main()
        print("本地多输出测试完成")
    except Exception as e:
        print(f"本地多输出测试失败: {e}")

asyncio.run(local_multi_output_test())
```

### 复杂多输出场景
```python
async def complex_multi_output_example():
    """复杂多输出场景示例"""
    
    agent = AsyncMultiOutputDecisionMaking(
        project_name="复杂多输出优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    # 建立连接
    await agent.async_database_connection()
    
    # 获取优化参数
    opt_params = await agent.get_optimization_parameter()
    
    # 设置为多目标优化
    opt_params.update({
        "optimization_function_type": "multi_objective",
        "n_objectives": 4,
        "optimization_algorithm": "NSGA2",
        "population_size": 150
    })
    
    # 应用参数
    agent._set_optimization_interval_time(opt_params)
    agent._set_termination(opt_params)
    
    # 构建模型
    await agent.get_model_data_and_build_models()
    
    # 执行决策
    await agent.decision_no_flag()
    
    # 清理
    await agent.clean_up()
    
    print("复杂多输出优化完成")

asyncio.run(complex_multi_output_example())
```

### 批量多输出决策
```python
async def batch_multi_output_decisions():
    """批量多输出决策示例"""
    
    projects = [
        ("水泥A磨多输出优化", "triple_objective"),
        ("水泥B磨多输出优化", "dual_objective"),
        ("煤磨多输出优化", "single_objective"),
        ("综合多输出优化", "multi_objective")
    ]
    
    async def run_multi_output_decision(project_name, obj_type):
        """运行单个多输出决策任务"""
        agent = AsyncMultiOutputDecisionMaking(
            project_name=project_name,
            dbconfig=dbconfig,
            local_test_mode=False
        )
        
        try:
            # 建立连接
            await agent.async_database_connection()
            
            # 获取并设置参数
            opt_params = await agent.get_optimization_parameter()
            opt_params["optimization_function_type"] = obj_type
            
            agent._set_optimization_interval_time(opt_params)
            agent._set_termination(opt_params)
            
            # 构建模型和执行决策
            await agent.get_model_data_and_build_models()
            await agent.decision_no_flag()
            
            print(f"{project_name} ({obj_type}) 完成")
            
        except Exception as e:
            print(f"{project_name} 失败: {e}")
        finally:
            await agent.clean_up()
    
    # 并发执行
    tasks = [
        run_multi_output_decision(project, obj_type) 
        for project, obj_type in projects
    ]
    await asyncio.gather(*tasks)
    
    print("所有多输出决策任务完成")

asyncio.run(batch_multi_output_decisions())
```

## 多输出模型适配指南

### 支持的输出格式

#### 1. 二维多输出
```python
# 模型输出: (batch_size, num_outputs)
output = torch.tensor([[1.2, 2.3, 3.4, 4.5]])  # 1x4输出
# 处理结果: mean([1.2, 2.3, 3.4, 4.5]) = 2.85
```

#### 2. 三维多输出
```python
# 模型输出: (batch_size, sequence_length, num_features)
output = torch.tensor([[[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]])  # 1x3x2输出
# 处理结果: mean(all_values) = 3.5
```

#### 3. 复杂多维输出
```python
# 模型输出: (batch_size, height, width, channels)
output = torch.randn(1, 10, 10, 3)  # 图像类输出
# 处理结果: mean(all_values)
```

### 模型集成最佳实践

#### 1. 模型包装
```python
class MultiOutputModelWrapper:
    """多输出模型包装器"""
    
    def __init__(self, model):
        self.model = model
        self.output_processor = self._setup_output_processor()
    
    def __call__(self, x):
        raw_output = self.model(x)
        return self.output_processor(raw_output)
    
    def _setup_output_processor(self):
        """设置输出处理器"""
        def processor(output):
            if output.dim() > 2:
                # 多维输出:展平后计算均值
                return output.view(output.size(0), -1).mean(dim=1, keepdim=True)
            else:
                # 二维输出:直接返回
                return output
        return processor
```

#### 2. 输出标准化
```python
class OutputNormalizer:
    """输出标准化器"""
    
    def __init__(self, target_range=(0, 1)):
        self.target_range = target_range
        self.stats = {}
    
    def fit(self, outputs):
        """拟合统计信息"""
        self.stats['mean'] = outputs.mean()
        self.stats['std'] = outputs.std()
    
    def transform(self, outputs):
        """标准化输出"""
        normalized = (outputs - self.stats['mean']) / self.stats['std']
        # 缩放到目标范围
        min_val, max_val = self.target_range
        return normalized * (max_val - min_val) + min_val
```

#### 3. 异常输出处理
```python
def robust_output_processing(model_output):
    """健壮的输出处理"""
    try:
        # 检查输出有效性
        if torch.isnan(model_output).any():
            print("警告:模型输出包含NaN值")
            model_output = torch.nan_to_num(model_output, nan=0.0)
        
        if torch.isinf(model_output).any():
            print("警告:模型输出包含无穷值")
            model_output = torch.clamp(model_output, -1e6, 1e6)
        
        # 多输出处理
        if model_output.dim() > 2:
            processed = torch.mean(model_output)
        else:
            processed = model_output.mean()
        
        return processed.item()
        
    except Exception as e:
        print(f"输出处理异常: {e}")
        return 1.0  # 返回默认值
```

## 性能优化建议

### 🚀 计算优化
```python
# 使用向量化操作
def vectorized_mean_calculation(outputs):
    """向量化均值计算"""
    if isinstance(outputs, list):
        outputs = torch.stack(outputs)
    return torch.mean(outputs, dim=tuple(range(1, outputs.dim())))

# 内存优化
def memory_efficient_processing(model_outputs):
    """内存高效的处理"""
    processed_outputs = []
    for output in model_outputs:
        # 立即处理并释放原始输出
        processed = torch.mean(output).item()
        processed_outputs.append(processed)
        del output  # 显式删除
    return processed_outputs
```

### 📊 缓存策略
```python
class OutputCache:
    """输出缓存"""
    
    def __init__(self, max_size=1000):
        self.cache = {}
        self.max_size = max_size
    
    def get_cached_output(self, input_hash):
        """获取缓存的输出"""
        return self.cache.get(input_hash)
    
    def cache_output(self, input_hash, output):
        """缓存输出"""
        if len(self.cache) >= self.max_size:
            # 删除最旧的缓存
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[input_hash] = output
```

## 故障排查

### 常见问题和解决方案

#### 1. 输出维度不匹配
```python
# 问题:模型输出维度与预期不符
# 解决:添加维度检查和自适应处理
def adaptive_output_processing(output):
    if output.dim() == 1:
        # 一维输出:添加batch维度
        output = output.unsqueeze(0)
    elif output.dim() > 3:
        # 高维输出:展平处理
        output = output.view(output.size(0), -1)
    return output
```

#### 2. 数值不稳定
```python
# 问题:输出值过大或过小导致优化不稳定
# 解决:添加数值稳定性处理
def stabilize_output(output, clip_range=(-100, 100)):
    # 裁剪极值
    output = torch.clamp(output, clip_range[0], clip_range[1])
    # 检查数值稳定性
    if torch.isnan(output).any() or torch.isinf(output).any():
        output = torch.zeros_like(output)
    return output
```

#### 3. 内存泄露
```python
# 问题:长时间运行导致内存泄露
# 解决:及时清理和垃圾回收
import gc

def memory_cleanup():
    """内存清理"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
```

## 注意事项

### ⚠️ 模型兼容性
- 确保模型支持批处理输入
- 验证模型输出格式的一致性
- 处理不同模型的输出尺度差异
- 考虑模型推理时间对优化效率的影响

### 🔧 参数调优
- 根据多输出特性调整优化算法参数
- 考虑增加种群大小以应对复杂适应度景观
- 适当延长迭代次数确保收敛
- 监控优化过程的稳定性

### 📈 性能监控
- 监控多输出处理的计算开销
- 跟踪内存使用情况
- 评估均值计算对优化效果的影响
- 定期验证模型输出的合理性

## 相关文档

- [异步决策智能体概述](./async_decision_overview.md) - 模块整体介绍
- [基础异步决策智能体](./async_basic_decision_agent.md) - AsyncDecisionMaking 详细文档
- [使用示例](./usage_examples.md) - 更多使用示例和最佳实践
- [API参考](./api_reference.md) - 完整的API参考文档

---

> **提示**:`AsyncMultiOutputDecisionMaking` 特别适用于深度学习模型、集成模型等产生多输出的场景。在使用时,建议先验证模型输出格式,确保均值计算策略符合业务需求。