# 最佳实践指南

本指南提供了使用异步决策智能体模块的最佳实践,帮助您构建高效、可靠、可维护的工业智能决策系统。

## 📋 目录




## 🏗️ 架构设计最佳实践

### 1. 模块化设计

```python
# ✅ 推荐:模块化的决策系统架构
class DecisionSystemArchitecture:
    """决策系统架构"""
    
    def __init__(self):
        # 分离关注点
        self.data_manager = DataManager()          # 数据管理
        self.model_manager = ModelManager()        # 模型管理
        self.optimizer = OptimizationEngine()      # 优化引擎
        self.decision_agent = DecisionAgent()      # 决策智能体
        self.monitor = SystemMonitor()             # 系统监控
        self.logger = StructuredLogger()           # 日志记录
    
    async def initialize(self):
        """初始化系统组件"""
        await self.data_manager.initialize()
        await self.model_manager.load_models()
        await self.optimizer.setup()
        await self.decision_agent.initialize()
        await self.monitor.start()
        
        self.logger.info("决策系统初始化完成")
    
    async def execute_decision(self, project_name: str):
        """执行决策流程"""
        try:
            # 获取数据
            data = await self.data_manager.get_latest_data(project_name)
            
            # 加载模型
            models = await self.model_manager.get_models(project_name)
            
            # 执行优化
            result = await self.optimizer.optimize(data, models)
            
            # 执行决策
            decision = await self.decision_agent.make_decision(result)
            
            # 记录结果
            await self.data_manager.save_decision(decision)
            
            # 监控记录
            self.monitor.record_decision(project_name, decision)
            
            return decision
            
        except Exception as e:
            self.logger.error(f"决策执行失败: {e}", project_name=project_name)
            raise
    
    async def cleanup(self):
        """清理资源"""
        await self.data_manager.close()
        await self.model_manager.cleanup()
        await self.optimizer.cleanup()
        await self.decision_agent.cleanup()
        await self.monitor.stop()

# ❌ 避免:单体式设计
class MonolithicDecisionSystem:
    """单体式决策系统(不推荐)"""
    
    def __init__(self):
        # 所有功能混合在一起
        self.db_connection = None
        self.models = {}
        self.optimization_params = {}
        # ... 大量混合的属性和方法
    
    def do_everything(self, project_name):
        """一个方法处理所有事情(不推荐)"""
        # 数据获取、模型加载、优化、决策、存储等
        # 所有逻辑混合在一个方法中
        pass
```

### 2. 异步编程模式

```python
# ✅ 推荐:正确的异步模式
class AsyncDecisionManager:
    """异步决策管理器"""
    
    def __init__(self):
        self.semaphore = asyncio.Semaphore(10)  # 限制并发数
        self.session_pool = aiohttp.ClientSession()  # 复用连接
        self.db_pool = None  # 数据库连接池
    
    async def initialize(self):
        """初始化异步资源"""
        # 创建数据库连接池
        self.db_pool = await asyncpg.create_pool(
            host='localhost',
            port=5432,
            database='industry_db',
            user='username',
            password='password',
            min_size=5,
            max_size=20
        )
    
    async def process_decisions_batch(self, projects: List[str]):
        """批量处理决策"""
        # 使用信号量控制并发
        tasks = []
        for project in projects:
            task = self._process_single_decision(project)
            tasks.append(task)
        
        # 并发执行,但限制并发数
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful = []
        failed = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed.append((projects[i], result))
            else:
                successful.append((projects[i], result))
        
        return successful, failed
    
    async def _process_single_decision(self, project_name: str):
        """处理单个决策"""
        async with self.semaphore:  # 控制并发
            try:
                # 并行获取数据和模型
                data_task = self._get_data(project_name)
                model_task = self._get_model(project_name)
                
                data, model = await asyncio.gather(data_task, model_task)
                
                # 执行优化
                result = await self._optimize(data, model)
                
                # 保存结果
                await self._save_result(project_name, result)
                
                return result
                
            except Exception as e:
                logger.error(f"处理决策失败: {e}", project_name=project_name)
                raise
    
    async def _get_data(self, project_name: str):
        """异步获取数据"""
        async with self.db_pool.acquire() as conn:
            query = "SELECT * FROM project_data WHERE project_name = $1"
            return await conn.fetch(query, project_name)
    
    async def _get_model(self, project_name: str):
        """异步加载模型"""
        # 使用线程池执行CPU密集型任务
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._load_model_sync, project_name)
    
    def _load_model_sync(self, project_name: str):
        """同步加载模型(在线程池中执行)"""
        import joblib
        model_path = f"/models/{project_name}_model.pkl"
        return joblib.load(model_path)
    
    async def cleanup(self):
        """清理异步资源"""
        if self.session_pool:
            await self.session_pool.close()
        if self.db_pool:
            await self.db_pool.close()

# ❌ 避免:阻塞式操作
class BlockingDecisionManager:
    """阻塞式决策管理器(不推荐)"""
    
    def process_decisions(self, projects):
        """同步处理决策(不推荐)"""
        results = []
        for project in projects:
            # 阻塞式操作
            data = self.get_data_sync(project)  # 阻塞
            model = self.load_model_sync(project)  # 阻塞
            result = self.optimize_sync(data, model)  # 阻塞
            results.append(result)
        return results
```

### 3. 配置管理

```python
# ✅ 推荐:分层配置管理
class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_layers = [
            DefaultConfig(),      # 默认配置
            FileConfig(),         # 文件配置
            EnvironmentConfig(),  # 环境变量配置
            RuntimeConfig()       # 运行时配置
        ]
        self.merged_config = {}
        self._merge_configs()
    
    def _merge_configs(self):
        """合并配置层"""
        for layer in self.config_layers:
            self.merged_config.update(layer.get_config())
    
    def get(self, key: str, default=None):
        """获取配置值"""
        return self.merged_config.get(key, default)
    
    def update_runtime_config(self, key: str, value):
        """更新运行时配置"""
        self.config_layers[-1].set(key, value)
        self._merge_configs()
    
    def validate(self):
        """验证配置"""
        validator = ConfigValidator()
        return validator.validate(self.merged_config)

class DefaultConfig:
    """默认配置"""
    
    def get_config(self):
        return {
            "database": {
                "host": "localhost",
                "port": 5432,
                "timeout": 30
            },
            "optimization": {
                "population_size": 100,
                "max_generations": 50
            },
            "logging": {
                "level": "INFO"
            }
        }

class FileConfig:
    """文件配置"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
    
    def get_config(self):
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

class EnvironmentConfig:
    """环境变量配置"""
    
    def get_config(self):
        config = {}
        
        # 数据库配置
        if os.getenv('DB_HOST'):
            config.setdefault('database', {})['host'] = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            config.setdefault('database', {})['port'] = int(os.getenv('DB_PORT'))
        
        # 优化配置
        if os.getenv('OPT_POPULATION_SIZE'):
            config.setdefault('optimization', {})['population_size'] = int(os.getenv('OPT_POPULATION_SIZE'))
        
        return config

class RuntimeConfig:
    """运行时配置"""
    
    def __init__(self):
        self.runtime_config = {}
    
    def get_config(self):
        return self.runtime_config
    
    def set(self, key: str, value):
        self.runtime_config[key] = value
```

## ⚡ 性能优化最佳实践

### 1. 数据库优化

```python
# ✅ 推荐:优化的数据库操作
class OptimizedDataManager:
    """优化的数据管理器"""
    
    def __init__(self):
        self.connection_pool = None
        self.query_cache = TTLCache(maxsize=1000, ttl=300)  # 5分钟缓存
        self.prepared_statements = {}
    
    async def initialize(self):
        """初始化连接池"""
        self.connection_pool = await asyncpg.create_pool(
            host='localhost',
            port=5432,
            database='industry_db',
            user='username',
            password='password',
            min_size=5,
            max_size=20,
            max_queries=50000,
            max_inactive_connection_lifetime=300
        )
        
        # 预编译常用查询
        await self._prepare_statements()
    
    async def _prepare_statements(self):
        """预编译SQL语句"""
        async with self.connection_pool.acquire() as conn:
            # 预编译常用查询
            self.prepared_statements['get_latest_data'] = await conn.prepare(
                "SELECT * FROM sensor_data WHERE project_name = $1 AND timestamp > $2 ORDER BY timestamp DESC LIMIT $3"
            )
            
            self.prepared_statements['insert_decision'] = await conn.prepare(
                "INSERT INTO decisions (project_name, decision_data, timestamp) VALUES ($1, $2, $3)"
            )
    
    async def get_latest_data(self, project_name: str, hours: int = 24, limit: int = 1000):
        """获取最新数据(使用缓存和预编译语句)"""
        cache_key = f"{project_name}_{hours}_{limit}"
        
        # 检查缓存
        if cache_key in self.query_cache:
            return self.query_cache[cache_key]
        
        # 执行查询
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        async with self.connection_pool.acquire() as conn:
            result = await self.prepared_statements['get_latest_data'].fetch(
                project_name, cutoff_time, limit
            )
        
        # 缓存结果
        self.query_cache[cache_key] = result
        
        return result
    
    async def batch_insert_decisions(self, decisions: List[Dict]):
        """批量插入决策结果"""
        if not decisions:
            return
        
        async with self.connection_pool.acquire() as conn:
            # 使用事务和批量插入
            async with conn.transaction():
                await conn.executemany(
                    "INSERT INTO decisions (project_name, decision_data, timestamp) VALUES ($1, $2, $3)",
                    [(d['project_name'], json.dumps(d['data']), d['timestamp']) for d in decisions]
                )
    
    async def get_aggregated_data(self, project_name: str, interval: str = '1h'):
        """获取聚合数据"""
        cache_key = f"agg_{project_name}_{interval}"
        
        if cache_key in self.query_cache:
            return self.query_cache[cache_key]
        
        # 使用数据库聚合函数
        query = f"""
        SELECT 
            date_trunc('{interval}', timestamp) as time_bucket,
            AVG(value) as avg_value,
            MAX(value) as max_value,
            MIN(value) as min_value,
            COUNT(*) as count
        FROM sensor_data 
        WHERE project_name = $1 
            AND timestamp > NOW() - INTERVAL '24 hours'
        GROUP BY time_bucket
        ORDER BY time_bucket
        """
        
        async with self.connection_pool.acquire() as conn:
            result = await conn.fetch(query, project_name)
        
        self.query_cache[cache_key] = result
        return result
```

### 2. 模型缓存和预加载

```python
# ✅ 推荐:智能模型管理
class IntelligentModelManager:
    """智能模型管理器"""
    
    def __init__(self, cache_size: int = 100):
        self.model_cache = LRUCache(maxsize=cache_size)
        self.model_metadata = {}  # 模型元数据
        self.preload_queue = asyncio.Queue()
        self.model_usage_stats = defaultdict(int)
        self.last_access_time = {}
        
        # 启动预加载任务
        asyncio.create_task(self._preload_worker())
        
        # 启动缓存清理任务
        asyncio.create_task(self._cache_cleanup_worker())
    
    async def load_model(self, model_name: str, force_reload: bool = False):
        """加载模型(带缓存)"""
        # 更新使用统计
        self.model_usage_stats[model_name] += 1
        self.last_access_time[model_name] = time.time()
        
        # 检查缓存
        if not force_reload and model_name in self.model_cache:
            return self.model_cache[model_name]
        
        # 异步加载模型
        model = await self._load_model_async(model_name)
        
        # 缓存模型
        self.model_cache[model_name] = model
        
        # 预加载相关模型
        await self._schedule_preload(model_name)
        
        return model
    
    async def _load_model_async(self, model_name: str):
        """异步加载模型"""
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行CPU密集型的模型加载
        model = await loop.run_in_executor(
            None, 
            self._load_model_sync, 
            model_name
        )
        
        return model
    
    def _load_model_sync(self, model_name: str):
        """同步加载模型"""
        model_path = f"/models/{model_name}.pkl"
        
        # 记录加载时间
        start_time = time.time()
        
        try:
            import joblib
            model = joblib.load(model_path)
            
            load_time = time.time() - start_time
            
            # 更新元数据
            self.model_metadata[model_name] = {
                'path': model_path,
                'load_time': load_time,
                'size': os.path.getsize(model_path),
                'last_modified': os.path.getmtime(model_path)
            }
            
            logger.info(f"模型加载成功: {model_name}, 耗时: {load_time:.2f}s")
            
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {model_name}, 错误: {e}")
            raise
    
    async def _schedule_preload(self, model_name: str):
        """调度预加载相关模型"""
        # 根据使用模式预加载相关模型
        related_models = self._get_related_models(model_name)
        
        for related_model in related_models:
            if related_model not in self.model_cache:
                await self.preload_queue.put(related_model)
    
    def _get_related_models(self, model_name: str) -> List[str]:
        """获取相关模型"""
        # 基于项目名称或模型类型的关联规则
        related = []
        
        if "电耗" in model_name:
            related.extend(["煤耗模型", "质量模型"])
        elif "煤耗" in model_name:
            related.extend(["电耗模型", "产量模型"])
        
        return related
    
    async def _preload_worker(self):
        """预加载工作器"""
        while True:
            try:
                model_name = await self.preload_queue.get()
                
                if model_name not in self.model_cache:
                    await self._load_model_async(model_name)
                    logger.info(f"预加载模型完成: {model_name}")
                
                self.preload_queue.task_done()
                
            except Exception as e:
                logger.error(f"预加载模型失败: {e}")
                await asyncio.sleep(1)
    
    async def _cache_cleanup_worker(self):
        """缓存清理工作器"""
        while True:
            await asyncio.sleep(300)  # 每5分钟清理一次
            
            current_time = time.time()
            models_to_remove = []
            
            # 清理长时间未使用的模型
            for model_name, last_access in self.last_access_time.items():
                if current_time - last_access > 3600:  # 1小时未使用
                    if model_name in self.model_cache:
                        models_to_remove.append(model_name)
            
            for model_name in models_to_remove:
                del self.model_cache[model_name]
                logger.info(f"清理缓存模型: {model_name}")
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.model_cache),
            'cache_hits': self.model_cache.hits,
            'cache_misses': self.model_cache.misses,
            'hit_rate': self.model_cache.hits / (self.model_cache.hits + self.model_cache.misses) if (self.model_cache.hits + self.model_cache.misses) > 0 else 0,
            'usage_stats': dict(self.model_usage_stats),
            'model_metadata': self.model_metadata
        }
```

### 3. 优化算法调优

```python
# ✅ 推荐:自适应优化算法
class AdaptiveOptimizer:
    """自适应优化器"""
    
    def __init__(self):
        self.performance_history = defaultdict(list)
        self.algorithm_stats = defaultdict(dict)
        self.current_algorithm = "GA"  # 默认算法
        self.adaptation_threshold = 10  # 适应阈值
    
    async def optimize(self, problem, project_name: str):
        """自适应优化"""
        # 选择最佳算法
        algorithm = self._select_algorithm(project_name)
        
        # 获取优化参数
        params = self._get_optimized_params(algorithm, project_name)
        
        # 执行优化
        start_time = time.time()
        
        try:
            if algorithm == "GA":
                result = await self._run_genetic_algorithm(problem, params)
            elif algorithm == "PSO":
                result = await self._run_particle_swarm(problem, params)
            elif algorithm == "NSGA2":
                result = await self._run_nsga2(problem, params)
            else:
                raise ValueError(f"未知算法: {algorithm}")
            
            # 记录性能
            execution_time = time.time() - start_time
            self._record_performance(project_name, algorithm, result, execution_time)
            
            return result
            
        except Exception as e:
            logger.error(f"优化失败: {e}", algorithm=algorithm, project_name=project_name)
            
            # 尝试备用算法
            if algorithm != "GA":
                logger.info(f"尝试备用算法: GA")
                return await self._run_genetic_algorithm(problem, self._get_default_params())
            
            raise
    
    def _select_algorithm(self, project_name: str) -> str:
        """选择最佳算法"""
        # 如果没有历史数据,使用默认算法
        if project_name not in self.performance_history:
            return self.current_algorithm
        
        # 分析历史性能
        history = self.performance_history[project_name]
        
        if len(history) < self.adaptation_threshold:
            return self.current_algorithm
        
        # 计算各算法的平均性能
        algorithm_performance = defaultdict(list)
        
        for record in history[-50:]:  # 最近50次记录
            algorithm_performance[record['algorithm']].append(record['fitness'])
        
        # 选择性能最好的算法
        best_algorithm = self.current_algorithm
        best_performance = float('-inf')
        
        for algorithm, performances in algorithm_performance.items():
            if performances:
                avg_performance = np.mean(performances)
                if avg_performance > best_performance:
                    best_performance = avg_performance
                    best_algorithm = algorithm
        
        return best_algorithm
    
    def _get_optimized_params(self, algorithm: str, project_name: str) -> Dict:
        """获取优化的参数"""
        base_params = self._get_default_params()
        
        # 根据历史性能调整参数
        if project_name in self.algorithm_stats:
            stats = self.algorithm_stats[project_name].get(algorithm, {})
            
            if 'avg_convergence_generation' in stats:
                # 根据收敛速度调整迭代次数
                avg_convergence = stats['avg_convergence_generation']
                base_params['max_generations'] = max(50, int(avg_convergence * 1.5))
            
            if 'avg_execution_time' in stats:
                # 根据执行时间调整种群大小
                avg_time = stats['avg_execution_time']
                if avg_time > 60:  # 如果执行时间超过1分钟
                    base_params['population_size'] = max(50, int(base_params['population_size'] * 0.8))
        
        return base_params
    
    def _get_default_params(self) -> Dict:
        """获取默认参数"""
        return {
            'population_size': 100,
            'max_generations': 50,
            'crossover_prob': 0.9,
            'mutation_prob': 0.1
        }
    
    async def _run_genetic_algorithm(self, problem, params):
        """运行遗传算法"""
        # 实现遗传算法
        # 这里是简化的示例
        population = self._initialize_population(params['population_size'], problem)
        
        best_fitness = float('-inf')
        convergence_generation = params['max_generations']
        
        for generation in range(params['max_generations']):
            # 评估适应度
            fitness_values = await self._evaluate_population(population, problem)
            
            # 检查收敛
            current_best = max(fitness_values)
            if current_best > best_fitness:
                best_fitness = current_best
                convergence_generation = generation
            
            # 选择、交叉、变异
            population = self._evolve_population(population, fitness_values, params)
            
            # 早停条件
            if generation - convergence_generation > 20:
                break
        
        best_individual = population[np.argmax(fitness_values)]
        
        return {
            'best_solution': best_individual,
            'best_fitness': best_fitness,
            'convergence_generation': convergence_generation,
            'total_generations': generation + 1
        }
    
    def _record_performance(self, project_name: str, algorithm: str, result: Dict, execution_time: float):
        """记录性能数据"""
        record = {
            'timestamp': time.time(),
            'algorithm': algorithm,
            'fitness': result['best_fitness'],
            'convergence_generation': result.get('convergence_generation', 0),
            'execution_time': execution_time
        }
        
        self.performance_history[project_name].append(record)
        
        # 更新算法统计
        if project_name not in self.algorithm_stats:
            self.algorithm_stats[project_name] = {}
        
        if algorithm not in self.algorithm_stats[project_name]:
            self.algorithm_stats[project_name][algorithm] = {
                'total_runs': 0,
                'total_fitness': 0,
                'total_convergence_generation': 0,
                'total_execution_time': 0
            }
        
        stats = self.algorithm_stats[project_name][algorithm]
        stats['total_runs'] += 1
        stats['total_fitness'] += result['best_fitness']
        stats['total_convergence_generation'] += result.get('convergence_generation', 0)
        stats['total_execution_time'] += execution_time
        
        # 计算平均值
        stats['avg_fitness'] = stats['total_fitness'] / stats['total_runs']
        stats['avg_convergence_generation'] = stats['total_convergence_generation'] / stats['total_runs']
        stats['avg_execution_time'] = stats['total_execution_time'] / stats['total_runs']
        
        # 保持历史记录在合理范围内
        if len(self.performance_history[project_name]) > 1000:
            self.performance_history[project_name] = self.performance_history[project_name][-500:]
```

## 📊 数据管理最佳实践

### 1. 数据验证和清洗

```python
# ✅ 推荐:完整的数据验证流水线
class DataValidationPipeline:
    """数据验证流水线"""
    
    def __init__(self):
        self.validators = [
            NullValueValidator(),
            RangeValidator(),
            TypeValidator(),
            ConsistencyValidator(),
            OutlierValidator()
        ]
        self.cleaners = [
            NullValueCleaner(),
            OutlierCleaner(),
            DuplicateCleaner(),
            FormatCleaner()
        ]
    
    async def validate_and_clean(self, data: pd.DataFrame, project_name: str) -> pd.DataFrame:
        """验证和清洗数据"""
        validation_results = []
        
        # 数据验证
        for validator in self.validators:
            result = await validator.validate(data, project_name)
            validation_results.append(result)
            
            if not result.is_valid:
                logger.warning(f"数据验证失败: {result.message}", 
                             project_name=project_name,
                             validator=validator.__class__.__name__)
        
        # 数据清洗
        cleaned_data = data.copy()
        
        for cleaner in self.cleaners:
            try:
                cleaned_data = await cleaner.clean(cleaned_data, project_name)
                logger.debug(f"数据清洗完成: {cleaner.__class__.__name__}",
                           project_name=project_name)
            except Exception as e:
                logger.error(f"数据清洗失败: {e}",
                           project_name=project_name,
                           cleaner=cleaner.__class__.__name__)
        
        # 记录清洗统计
        self._log_cleaning_stats(data, cleaned_data, project_name)
        
        return cleaned_data
    
    def _log_cleaning_stats(self, original: pd.DataFrame, cleaned: pd.DataFrame, project_name: str):
        """记录清洗统计信息"""
        stats = {
            'original_rows': len(original),
            'cleaned_rows': len(cleaned),
            'removed_rows': len(original) - len(cleaned),
            'removal_rate': (len(original) - len(cleaned)) / len(original) if len(original) > 0 else 0
        }
        
        logger.info("数据清洗统计", project_name=project_name, **stats)

class NullValueValidator:
    """空值验证器"""
    
    async def validate(self, data: pd.DataFrame, project_name: str) -> ValidationResult:
        null_counts = data.isnull().sum()
        null_percentage = (null_counts / len(data)) * 100
        
        # 检查是否有列的空值比例过高
        high_null_columns = null_percentage[null_percentage > 50].index.tolist()
        
        if high_null_columns:
            return ValidationResult(
                is_valid=False,
                message=f"以下列空值比例过高: {high_null_columns}",
                details={'null_percentages': null_percentage.to_dict()}
            )
        
        return ValidationResult(
            is_valid=True,
            message="空值检查通过",
            details={'null_percentages': null_percentage.to_dict()}
        )

class RangeValidator:
    """范围验证器"""
    
    def __init__(self):
        # 定义各种传感器的合理范围
        self.ranges = {
            'temperature': (-50, 200),
            'pressure': (0, 1000),
            'flow_rate': (0, 10000),
            'power_consumption': (0, 50000)
        }
    
    async def validate(self, data: pd.DataFrame, project_name: str) -> ValidationResult:
        out_of_range_issues = []
        
        for column in data.columns:
            if column in self.ranges:
                min_val, max_val = self.ranges[column]
                out_of_range = data[(data[column] < min_val) | (data[column] > max_val)]
                
                if len(out_of_range) > 0:
                    out_of_range_issues.append({
                        'column': column,
                        'count': len(out_of_range),
                        'percentage': (len(out_of_range) / len(data)) * 100
                    })
        
        if out_of_range_issues:
            return ValidationResult(
                is_valid=False,
                message="发现超出范围的数据",
                details={'out_of_range_issues': out_of_range_issues}
            )
        
        return ValidationResult(
            is_valid=True,
            message="范围检查通过"
        )

class OutlierValidator:
    """异常值验证器"""
    
    async def validate(self, data: pd.DataFrame, project_name: str) -> ValidationResult:
        outlier_info = []
        
        for column in data.select_dtypes(include=[np.number]).columns:
            # 使用IQR方法检测异常值
            Q1 = data[column].quantile(0.25)
            Q3 = data[column].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
            
            if len(outliers) > 0:
                outlier_percentage = (len(outliers) / len(data)) * 100
                outlier_info.append({
                    'column': column,
                    'count': len(outliers),
                    'percentage': outlier_percentage,
                    'bounds': (lower_bound, upper_bound)
                })
        
        # 如果异常值比例过高,标记为无效
        high_outlier_columns = [info for info in outlier_info if info['percentage'] > 10]
        
        if high_outlier_columns:
            return ValidationResult(
                is_valid=False,
                message="异常值比例过高",
                details={'outlier_info': outlier_info}
            )
        
        return ValidationResult(
            is_valid=True,
            message="异常值检查通过",
            details={'outlier_info': outlier_info}
        )

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    message: str
    details: Dict = None
```

### 2. 数据缓存策略

```python
# ✅ 推荐:多层数据缓存
class MultiLevelDataCache:
    """多层数据缓存"""
    
    def __init__(self):
        # L1缓存:内存缓存(最快)
        self.memory_cache = TTLCache(maxsize=100, ttl=300)  # 5分钟
        
        # L2缓存:Redis缓存(中等速度)
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        
        # L3缓存:本地文件缓存(较慢但持久)
        self.file_cache_dir = "/tmp/data_cache"
        os.makedirs(self.file_cache_dir, exist_ok=True)
        
        self.cache_stats = {
            'l1_hits': 0,
            'l2_hits': 0,
            'l3_hits': 0,
            'misses': 0
        }
    
    async def get(self, key: str) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        # L1缓存检查
        if key in self.memory_cache:
            self.cache_stats['l1_hits'] += 1
            return self.memory_cache[key]
        
        # L2缓存检查
        try:
            redis_data = self.redis_client.get(key)
            if redis_data:
                data = pickle.loads(redis_data)
                # 回填L1缓存
                self.memory_cache[key] = data
                self.cache_stats['l2_hits'] += 1
                return data
        except Exception as e:
            logger.warning(f"Redis缓存读取失败: {e}")
        
        # L3缓存检查
        file_path = os.path.join(self.file_cache_dir, f"{key}.pkl")
        if os.path.exists(file_path):
            try:
                # 检查文件是否过期(1小时)
                if time.time() - os.path.getmtime(file_path) < 3600:
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)
                    
                    # 回填上层缓存
                    self.memory_cache[key] = data
                    try:
                        self.redis_client.setex(key, 1800, pickle.dumps(data))  # 30分钟
                    except Exception as e:
                        logger.warning(f"Redis缓存写入失败: {e}")
                    
                    self.cache_stats['l3_hits'] += 1
                    return data
            except Exception as e:
                logger.warning(f"文件缓存读取失败: {e}")
        
        self.cache_stats['misses'] += 1
        return None
    
    async def set(self, key: str, data: pd.DataFrame):
        """设置缓存数据"""
        # L1缓存
        self.memory_cache[key] = data
        
        # L2缓存
        try:
            self.redis_client.setex(key, 1800, pickle.dumps(data))  # 30分钟
        except Exception as e:
            logger.warning(f"Redis缓存写入失败: {e}")
        
        # L3缓存(异步写入)
        asyncio.create_task(self._write_file_cache(key, data))
    
    async def _write_file_cache(self, key: str, data: pd.DataFrame):
        """异步写入文件缓存"""
        try:
            file_path = os.path.join(self.file_cache_dir, f"{key}.pkl")
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.warning(f"文件缓存写入失败: {e}")
    
    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = sum(self.cache_stats.values())
        if total_requests == 0:
            return self.cache_stats
        
        return {
            **self.cache_stats,
            'l1_hit_rate': self.cache_stats['l1_hits'] / total_requests,
            'l2_hit_rate': self.cache_stats['l2_hits'] / total_requests,
            'l3_hit_rate': self.cache_stats['l3_hits'] / total_requests,
            'overall_hit_rate': (total_requests - self.cache_stats['misses']) / total_requests
        }
    
    async def clear(self, pattern: str = None):
        """清理缓存"""
        if pattern:
            # 清理匹配模式的缓存
            keys_to_remove = [key for key in self.memory_cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self.memory_cache[key]
        else:
            # 清理所有缓存
            self.memory_cache.clear()
        
        # 清理Redis缓存
        try:
            if pattern:
                keys = self.redis_client.keys(f"*{pattern}*")
                if keys:
                    self.redis_client.delete(*keys)
            else:
                self.redis_client.flushdb()
        except Exception as e:
            logger.warning(f"Redis缓存清理失败: {e}")
```

## 🤖 模型管理最佳实践

### 1. 模型版本控制

```python
# ✅ 推荐:模型版本管理系统
class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, model_registry_path: str = "/models/registry"):
        self.registry_path = model_registry_path
        self.metadata_file = os.path.join(registry_path, "model_metadata.json")
        self.models_dir = os.path.join(registry_path, "models")
        
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 加载模型元数据
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict:
        """加载模型元数据"""
        if os.path.exists(self.metadata_file):
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_metadata(self):
        """保存模型元数据"""
        with open(self.metadata_file, 'w') as f:
            json.dump(self.metadata, f, indent=2, default=str)
    
    async def register_model(self, 
                           model_name: str, 
                           model_path: str, 
                           version: str = None,
                           metadata: Dict = None) -> str:
        """注册模型"""
        if version is None:
            version = self._generate_version(model_name)
        
        # 创建模型目录
        model_dir = os.path.join(self.models_dir, model_name)
        version_dir = os.path.join(model_dir, version)
        os.makedirs(version_dir, exist_ok=True)
        
        # 复制模型文件
        model_filename = os.path.basename(model_path)
        target_path = os.path.join(version_dir, model_filename)
        shutil.copy2(model_path, target_path)
        
        # 计算模型哈希
        model_hash = self._calculate_file_hash(target_path)
        
        # 更新元数据
        if model_name not in self.metadata:
            self.metadata[model_name] = {
                'versions': {},
                'latest_version': version,
                'created_at': datetime.utcnow().isoformat()
            }
        
        self.metadata[model_name]['versions'][version] = {
            'path': target_path,
            'hash': model_hash,
            'size': os.path.getsize(target_path),
            'created_at': datetime.utcnow().isoformat(),
            'metadata': metadata or {},
            'status': 'active'
        }
        
        self.metadata[model_name]['latest_version'] = version
        self.metadata[model_name]['updated_at'] = datetime.utcnow().isoformat()
        
        self._save_metadata()
        
        logger.info(f"模型注册成功: {model_name} v{version}")
        
        return version
    
    def _generate_version(self, model_name: str) -> str:
        """生成版本号"""
        if model_name not in self.metadata:
            return "1.0.0"
        
        versions = list(self.metadata[model_name]['versions'].keys())
        if not versions:
            return "1.0.0"
        
        # 简单的版本递增逻辑
        latest = max(versions, key=lambda v: [int(x) for x in v.split('.')])
        major, minor, patch = map(int, latest.split('.'))
        
        return f"{major}.{minor}.{patch + 1}"
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        import hashlib
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def get_model_path(self, model_name: str, version: str = None) -> str:
        """获取模型路径"""
        if model_name not in self.metadata:
            raise ValueError(f"模型不存在: {model_name}")
        
        if version is None:
            version = self.metadata[model_name]['latest_version']
        
        if version not in self.metadata[model_name]['versions']:
            raise ValueError(f"模型版本不存在: {model_name} v{version}")
        
        version_info = self.metadata[model_name]['versions'][version]
        
        if version_info['status'] != 'active':
            raise ValueError(f"模型版本已停用: {model_name} v{version}")
        
        return version_info['path']
    
    async def validate_model(self, model_name: str, version: str = None) -> bool:
        """验证模型完整性"""
        try:
            model_path = await self.get_model_path(model_name, version)
            
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return False
            
            # 验证哈希
            if version is None:
                version = self.metadata[model_name]['latest_version']
            
            expected_hash = self.metadata[model_name]['versions'][version]['hash']
            actual_hash = self._calculate_file_hash(model_path)
            
            if expected_hash != actual_hash:
                logger.error(f"模型文件哈希不匹配: {model_name} v{version}")
                return False
            
            # 尝试加载模型
            try:
                import joblib
                model = joblib.load(model_path)
                logger.info(f"模型验证成功: {model_name} v{version}")
                return True
            except Exception as e:
                logger.error(f"模型加载失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"模型验证失败: {e}")
            return False
    
    async def rollback_model(self, model_name: str, target_version: str):
        """回滚模型版本"""
        if model_name not in self.metadata:
            raise ValueError(f"模型不存在: {model_name}")
        
        if target_version not in self.metadata[model_name]['versions']:
            raise ValueError(f"目标版本不存在: {model_name} v{target_version}")
        
        # 验证目标版本
        if not await self.validate_model(model_name, target_version):
            raise ValueError(f"目标版本验证失败: {model_name} v{target_version}")
        
        # 更新最新版本
        old_version = self.metadata[model_name]['latest_version']
        self.metadata[model_name]['latest_version'] = target_version
        self.metadata[model_name]['updated_at'] = datetime.utcnow().isoformat()
        
        self._save_metadata()
        
        logger.info(f"模型回滚成功: {model_name} {old_version} -> {target_version}")
    
    def list_models(self) -> Dict:
        """列出所有模型"""
        result = {}
        
        for model_name, model_info in self.metadata.items():
            result[model_name] = {
                'latest_version': model_info['latest_version'],
                'versions': list(model_info['versions'].keys()),
                'created_at': model_info['created_at'],
                'updated_at': model_info.get('updated_at', model_info['created_at'])
            }
        
        return result
    
    def get_model_info(self, model_name: str) -> Dict:
        """获取模型详细信息"""
        if model_name not in self.metadata:
            raise ValueError(f"模型不存在: {model_name}")
        
        return self.metadata[model_name]
```

### 2. 模型性能监控

```python
# ✅ 推荐:模型性能监控系统
class ModelPerformanceMonitor:
    """模型性能监控器"""
    
    def __init__(self):
        self.performance_data = defaultdict(list)
        self.drift_detectors = {}
        self.alert_thresholds = {
            'accuracy_drop': 0.1,      # 准确率下降阈值
            'latency_increase': 2.0,   # 延迟增加倍数阈值
            'drift_score': 0.5         # 数据漂移分数阈值
        }
        self.baseline_metrics = {}
    
    async def record_prediction(self, 
                              model_name: str, 
                              input_data: np.ndarray, 
                              prediction: np.ndarray,
                              actual: np.ndarray = None,
                              latency: float = None):
        """记录预测结果"""
        timestamp = time.time()
        
        record = {
            'timestamp': timestamp,
            'model_name': model_name,
            'input_shape': input_data.shape,
            'prediction': prediction,
            'actual': actual,
            'latency': latency
        }
        
        self.performance_data[model_name].append(record)
        
        # 检测数据漂移
        await self._detect_drift(model_name, input_data)
        
        # 如果有真实值,计算性能指标
        if actual is not None:
            await self._calculate_metrics(model_name, prediction, actual)
        
        # 检查延迟
        if latency is not None:
            await self._check_latency(model_name, latency)
        
        # 清理旧数据(保留最近1000条记录)
        if len(self.performance_data[model_name]) > 1000:
            self.performance_data[model_name] = self.performance_data[model_name][-1000:]
    
    async def _detect_drift(self, model_name: str, input_data: np.ndarray):
        """检测数据漂移"""
        if model_name not in self.drift_detectors:
            # 初始化漂移检测器
            from alibi_detect import KSDrift
            
            # 使用历史数据作为参考
            reference_data = self._get_reference_data(model_name)
            if reference_data is not None:
                self.drift_detectors[model_name] = KSDrift(reference_data)
        
        if model_name in self.drift_detectors:
            try:
                drift_result = self.drift_detectors[model_name].predict(input_data)
                
                if drift_result['data']['is_drift']:
                    drift_score = drift_result['data']['p_val']
                    
                    if drift_score > self.alert_thresholds['drift_score']:
                        await self._send_alert(
                            model_name, 
                            'data_drift', 
                            f"检测到数据漂移,分数: {drift_score:.3f}"
                        )
                        
                        logger.warning(f"数据漂移警告: {model_name}, 分数: {drift_score:.3f}")
            
            except Exception as e:
                logger.error(f"数据漂移检测失败: {e}")
    
    def _get_reference_data(self, model_name: str) -> Optional[np.ndarray]:
        """获取参考数据"""
        # 从历史记录中获取参考数据
        if model_name in self.performance_data:
            records = self.performance_data[model_name][-100:]  # 最近100条记录
            if records:
                # 这里需要根据实际情况提取输入数据
                # 简化示例
                return np.random.random((100, 10))  # 示例数据
        return None
    
    async def _calculate_metrics(self, model_name: str, prediction: np.ndarray, actual: np.ndarray):
        """计算性能指标"""
        try:
            from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
            
            # 计算回归指标
            mse = mean_squared_error(actual, prediction)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(actual, prediction)
            r2 = r2_score(actual, prediction)
            
            metrics = {
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,
                'timestamp': time.time()
            }
            
            # 检查性能下降
            await self._check_performance_degradation(model_name, metrics)
            
            # 记录指标
            if model_name not in self.baseline_metrics:
                self.baseline_metrics[model_name] = []
            
            self.baseline_metrics[model_name].append(metrics)
            
            # 保留最近100个指标
            if len(self.baseline_metrics[model_name]) > 100:
                self.baseline_metrics[model_name] = self.baseline_metrics[model_name][-100:]
        
        except Exception as e:
            logger.error(f"性能指标计算失败: {e}")
    
    async def _check_performance_degradation(self, model_name: str, current_metrics: Dict):
        """检查性能下降"""
        if model_name not in self.baseline_metrics or len(self.baseline_metrics[model_name]) < 10:
            return  # 需要足够的历史数据
        
        # 计算基线性能(最近10次的平均值)
        recent_metrics = self.baseline_metrics[model_name][-10:]
        baseline_r2 = np.mean([m['r2'] for m in recent_metrics])
        
        # 检查R²分数下降
        r2_drop = baseline_r2 - current_metrics['r2']
        
        if r2_drop > self.alert_thresholds['accuracy_drop']:
            await self._send_alert(
                model_name,
                'performance_degradation',
                f"模型性能下降,R²分数下降: {r2_drop:.3f}"
            )
    
    async def _check_latency(self, model_name: str, latency: float):
        """检查延迟"""
        # 获取历史延迟数据
        recent_records = self.performance_data[model_name][-50:]  # 最近50次
        recent_latencies = [r['latency'] for r in recent_records if r['latency'] is not None]
        
        if len(recent_latencies) < 10:
            return  # 需要足够的历史数据
        
        baseline_latency = np.mean(recent_latencies)
        
        if latency > baseline_latency * self.alert_thresholds['latency_increase']:
            await self._send_alert(
                model_name,
                'high_latency',
                f"模型延迟过高: {latency:.3f}s (基线: {baseline_latency:.3f}s)"
            )
    
    async def _send_alert(self, model_name: str, alert_type: str, message: str):
        """发送告警"""
        alert = {
            'timestamp': time.time(),
            'model_name': model_name,
            'alert_type': alert_type,
            'message': message,
            'severity': 'warning'
        }
        
        # 这里可以集成实际的告警系统
        logger.warning(f"模型告警: {message}", **alert)
        
        # 可以发送到监控系统、邮件、Slack等
        # await self.notification_service.send_alert(alert)
    
    def get_model_summary(self, model_name: str) -> Dict:
        """获取模型性能摘要"""
        if model_name not in self.performance_data:
            return {}
        
        records = self.performance_data[model_name]
        
        if not records:
            return {}
        
        # 计算统计信息
        latencies = [r['latency'] for r in records if r['latency'] is not None]
        
        summary = {
            'total_predictions': len(records),
            'avg_latency': np.mean(latencies) if latencies else None,
            'max_latency': np.max(latencies) if latencies else None,
            'min_latency': np.min(latencies) if latencies else None,
            'last_prediction': records[-1]['timestamp'] if records else None
        }
        
        # 添加性能指标
        if model_name in self.baseline_metrics and self.baseline_metrics[model_name]:
            recent_metrics = self.baseline_metrics[model_name][-10:]
            summary.update({
                'avg_r2': np.mean([m['r2'] for m in recent_metrics]),
                'avg_rmse': np.mean([m['rmse'] for m in recent_metrics]),
                'avg_mae': np.mean([m['mae'] for m in recent_metrics])
            })
        
        return summary


## 🚨 错误处理最佳实践

### 1. 分层错误处理

```python
# ✅ 推荐:分层错误处理策略
class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_strategies = {
            ConnectionError: self._handle_connection_error,
            TimeoutError: self._handle_timeout_error,
            ValidationError: self._handle_validation_error,
            ModelError: self._handle_model_error,
            OptimizationError: self._handle_optimization_error
        }
        self.retry_config = {
            'max_retries': 3,
            'base_delay': 1,
            'max_delay': 60,
            'exponential_base': 2
        }
    
    async def handle_error(self, error: Exception, context: Dict) -> bool:
        """处理错误"""
        error_type = type(error)
        
        # 记录错误
        await self._log_error(error, context)
        
        # 查找处理策略
        for error_class, handler in self.error_strategies.items():
            if isinstance(error, error_class):
                return await handler(error, context)
        
        # 默认处理
        return await self._handle_unknown_error(error, context)
    
    async def _handle_connection_error(self, error: ConnectionError, context: Dict) -> bool:
        """处理连接错误"""
        logger.warning(f"连接错误: {error}", **context)
        
        # 尝试重新连接
        if context.get('retry_count', 0) < self.retry_config['max_retries']:
            delay = self._calculate_retry_delay(context.get('retry_count', 0))
            await asyncio.sleep(delay)
            return True  # 可以重试
        
        return False  # 不可重试
    
    async def _handle_timeout_error(self, error: TimeoutError, context: Dict) -> bool:
        """处理超时错误"""
        logger.warning(f"超时错误: {error}", **context)
        
        # 增加超时时间重试
        if context.get('retry_count', 0) < 2:
            context['timeout'] = context.get('timeout', 30) * 2
            return True
        
        return False
    
    async def _handle_validation_error(self, error: Exception, context: Dict) -> bool:
        """处理验证错误"""
        logger.error(f"数据验证错误: {error}", **context)
        
        # 验证错误通常不可重试
        return False
    
    async def _handle_model_error(self, error: Exception, context: Dict) -> bool:
        """处理模型错误"""
        logger.error(f"模型错误: {error}", **context)
        
        # 尝试重新加载模型
        if context.get('model_reload_count', 0) < 1:
            context['model_reload_count'] = context.get('model_reload_count', 0) + 1
            return True
        
        return False
    
    async def _handle_optimization_error(self, error: Exception, context: Dict) -> bool:
        """处理优化错误"""
        logger.error(f"优化错误: {error}", **context)
        
        # 尝试降低优化参数
        if context.get('param_reduction_count', 0) < 2:
            context['param_reduction_count'] = context.get('param_reduction_count', 0) + 1
            context['population_size'] = max(20, context.get('population_size', 100) // 2)
            context['max_generations'] = max(10, context.get('max_generations', 50) // 2)
            return True
        
        return False
    
    async def _handle_unknown_error(self, error: Exception, context: Dict) -> bool:
        """处理未知错误"""
        logger.error(f"未知错误: {error}", **context)
        return False
    
    def _calculate_retry_delay(self, retry_count: int) -> float:
        """计算重试延迟"""
        delay = self.retry_config['base_delay'] * (
            self.retry_config['exponential_base'] ** retry_count
        )
        return min(delay, self.retry_config['max_delay'])
    
    async def _log_error(self, error: Exception, context: Dict):
        """记录错误日志"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context,
            'timestamp': time.time()
        }
        
        # 发送到错误监控系统
        # await self.error_monitoring_service.report_error(error_info)
        
        logger.error("错误详情", **error_info)


class ResilientDecisionAgent:
    """具有弹性的决策智能体"""
    
    def __init__(self, base_agent, error_handler: ErrorHandler):
        self.base_agent = base_agent
        self.error_handler = error_handler
        self.circuit_breaker = CircuitBreaker()
    
    async def make_decision(self, project_name: str, **kwargs):
        """执行决策(带错误处理)"""
        context = {
            'project_name': project_name,
            'operation': 'make_decision',
            **kwargs
        }
        
        retry_count = 0
        max_retries = 3
        
        while retry_count <= max_retries:
            try:
                # 检查熔断器状态
                if not self.circuit_breaker.can_execute():
                    raise Exception("服务熔断中")
                
                # 执行决策
                result = await self.base_agent.make_decision(project_name, **kwargs)
                
                # 成功时重置熔断器
                self.circuit_breaker.record_success()
                
                return result
                
            except Exception as e:
                # 记录失败
                self.circuit_breaker.record_failure()
                
                # 更新上下文
                context['retry_count'] = retry_count
                context['error'] = str(e)
                
                # 处理错误
                can_retry = await self.error_handler.handle_error(e, context)
                
                if not can_retry or retry_count >= max_retries:
                    logger.error(f"决策失败,已达到最大重试次数: {retry_count}")
                    raise
                
                retry_count += 1
                logger.info(f"准备重试决策,第 {retry_count} 次")


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        if self.state == 'CLOSED':
            return True
        elif self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """记录成功"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'
```

### 2. 优雅降级策略

```python
# ✅ 推荐:优雅降级机制
class GracefulDegradationManager:
    """优雅降级管理器"""
    
    def __init__(self):
        self.degradation_levels = {
            'NORMAL': 0,
            'LIGHT_DEGRADATION': 1,
            'MODERATE_DEGRADATION': 2,
            'HEAVY_DEGRADATION': 3,
            'EMERGENCY_MODE': 4
        }
        self.current_level = 'NORMAL'
        self.system_health = SystemHealthMonitor()
    
    async def execute_with_degradation(self, operation_func, *args, **kwargs):
        """执行操作(带降级)"""
        # 评估系统健康状况
        health_score = await self.system_health.get_health_score()
        
        # 确定降级级别
        degradation_level = self._determine_degradation_level(health_score)
        
        if degradation_level != self.current_level:
            logger.info(f"降级级别变更: {self.current_level} -> {degradation_level}")
            self.current_level = degradation_level
        
        # 根据降级级别调整操作
        if degradation_level == 'NORMAL':
            return await operation_func(*args, **kwargs)
        elif degradation_level == 'LIGHT_DEGRADATION':
            return await self._light_degradation(operation_func, *args, **kwargs)
        elif degradation_level == 'MODERATE_DEGRADATION':
            return await self._moderate_degradation(operation_func, *args, **kwargs)
        elif degradation_level == 'HEAVY_DEGRADATION':
            return await self._heavy_degradation(operation_func, *args, **kwargs)
        else:  # EMERGENCY_MODE
            return await self._emergency_mode(operation_func, *args, **kwargs)
    
    def _determine_degradation_level(self, health_score: float) -> str:
        """确定降级级别"""
        if health_score >= 0.9:
            return 'NORMAL'
        elif health_score >= 0.7:
            return 'LIGHT_DEGRADATION'
        elif health_score >= 0.5:
            return 'MODERATE_DEGRADATION'
        elif health_score >= 0.3:
            return 'HEAVY_DEGRADATION'
        else:
            return 'EMERGENCY_MODE'
    
    async def _light_degradation(self, operation_func, *args, **kwargs):
        """轻度降级:减少并发数"""
        # 限制并发数
        semaphore = asyncio.Semaphore(5)  # 正常情况下可能是10
        
        async with semaphore:
            return await operation_func(*args, **kwargs)
    
    async def _moderate_degradation(self, operation_func, *args, **kwargs):
        """中度降级:使用缓存,减少计算复杂度"""
        # 优先使用缓存
        cache_key = self._generate_cache_key(args, kwargs)
        cached_result = await self._get_from_cache(cache_key)
        
        if cached_result is not None:
            logger.info("使用缓存结果(中度降级模式)")
            return cached_result
        
        # 减少优化参数
        if 'optimization_params' in kwargs:
            kwargs['optimization_params'] = self._reduce_optimization_params(
                kwargs['optimization_params']
            )
        
        result = await operation_func(*args, **kwargs)
        await self._save_to_cache(cache_key, result)
        
        return result
    
    async def _heavy_degradation(self, operation_func, *args, **kwargs):
        """重度降级:使用简化算法"""
        # 使用简化的决策算法
        logger.warning("系统重度降级,使用简化决策算法")
        
        # 这里可以实现一个简化的决策逻辑
        return await self._simplified_decision(*args, **kwargs)
    
    async def _emergency_mode(self, operation_func, *args, **kwargs):
        """紧急模式:返回安全的默认值"""
        logger.critical("系统进入紧急模式,返回安全默认值")
        
        # 返回安全的默认决策
        return self._get_safe_default_decision(kwargs.get('project_name'))
    
    def _reduce_optimization_params(self, params: Dict) -> Dict:
        """减少优化参数"""
        reduced_params = params.copy()
        reduced_params['population_size'] = max(20, params.get('population_size', 100) // 2)
        reduced_params['max_generations'] = max(10, params.get('max_generations', 50) // 2)
        return reduced_params
    
    async def _simplified_decision(self, *args, **kwargs):
        """简化决策算法"""
        # 实现一个简单的基于规则的决策
        project_name = kwargs.get('project_name')
        
        # 获取最近的历史决策
        recent_decision = await self._get_recent_decision(project_name)
        
        if recent_decision:
            # 基于历史决策进行微调
            return self._adjust_recent_decision(recent_decision)
        
        # 返回默认决策
        return self._get_safe_default_decision(project_name)
    
    def _get_safe_default_decision(self, project_name: str) -> Dict:
        """获取安全的默认决策"""
        # 返回保守的决策值
        return {
            'decision_values': [0.5] * 10,  # 中等值
            'confidence': 0.1,  # 低置信度
            'mode': 'emergency',
            'timestamp': time.time()
        }


class SystemHealthMonitor:
    """系统健康监控器"""
    
    def __init__(self):
        self.health_indicators = [
            CPUHealthIndicator(),
            MemoryHealthIndicator(),
            DatabaseHealthIndicator(),
            ModelHealthIndicator()
        ]
    
    async def get_health_score(self) -> float:
        """获取系统健康分数"""
        scores = []
        
        for indicator in self.health_indicators:
            try:
                score = await indicator.get_score()
                scores.append(score)
            except Exception as e:
                logger.error(f"健康指标检查失败: {e}")
                scores.append(0.0)  # 失败时给最低分
        
        # 计算加权平均分
        return np.mean(scores) if scores else 0.0


class CPUHealthIndicator:
    """CPU健康指标"""
    
    async def get_score(self) -> float:
        import psutil
        cpu_percent = psutil.cpu_percent(interval=1)
        
        if cpu_percent < 50:
            return 1.0
        elif cpu_percent < 70:
            return 0.8
        elif cpu_percent < 85:
            return 0.5
        else:
            return 0.2


class MemoryHealthIndicator:
    """内存健康指标"""
    
    async def get_score(self) -> float:
        import psutil
        memory = psutil.virtual_memory()
        
        if memory.percent < 60:
            return 1.0
        elif memory.percent < 75:
            return 0.8
        elif memory.percent < 90:
            return 0.5
        else:
            return 0.2


class DatabaseHealthIndicator:
    """数据库健康指标"""
    
    async def get_score(self) -> float:
        try:
            # 测试数据库连接
            start_time = time.time()
            # await self.db_client.execute("SELECT 1")
            response_time = time.time() - start_time
            
            if response_time < 0.1:
                return 1.0
            elif response_time < 0.5:
                return 0.8
            elif response_time < 1.0:
                return 0.5
            else:
                return 0.2
        except Exception:
            return 0.0


class ModelHealthIndicator:
    """模型健康指标"""
    
    async def get_score(self) -> float:
        try:
            # 检查模型响应时间和准确性
            # 这里可以实现模型健康检查逻辑
            return 1.0  # 简化实现
        except Exception:
            return 0.0
```


## 📊 监控告警最佳实践

### 1. 全面监控体系

```python
# ✅ 推荐:完整的监控告警系统
class ComprehensiveMonitoringSystem:
    """综合监控系统"""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        self.dashboard = MonitoringDashboard()
        self.health_checker = HealthChecker()
        
        # 监控指标定义
        self.metrics_config = {
            'performance': {
                'decision_latency': {'threshold': 5.0, 'unit': 'seconds'},
                'throughput': {'threshold': 100, 'unit': 'decisions/hour'},
                'success_rate': {'threshold': 0.95, 'unit': 'ratio'}
            },
            'system': {
                'cpu_usage': {'threshold': 80, 'unit': 'percent'},
                'memory_usage': {'threshold': 85, 'unit': 'percent'},
                'disk_usage': {'threshold': 90, 'unit': 'percent'}
            },
            'business': {
                'model_accuracy': {'threshold': 0.85, 'unit': 'ratio'},
                'optimization_convergence': {'threshold': 0.8, 'unit': 'ratio'},
                'data_quality_score': {'threshold': 0.9, 'unit': 'ratio'}
            }
        }
    
    async def start_monitoring(self):
        """启动监控"""
        # 启动各个监控组件
        await asyncio.gather(
            self.metrics_collector.start(),
            self.alert_manager.start(),
            self.dashboard.start(),
            self.health_checker.start()
        )
        
        logger.info("监控系统已启动")
    
    async def collect_metrics(self, decision_result: Dict):
        """收集指标"""
        metrics = {
            'timestamp': time.time(),
            'decision_id': decision_result.get('decision_id'),
            'project_name': decision_result.get('project_name'),
            'latency': decision_result.get('execution_time'),
            'success': decision_result.get('success', False),
            'error_type': decision_result.get('error_type'),
            'model_accuracy': decision_result.get('model_accuracy'),
            'optimization_score': decision_result.get('optimization_score')
        }
        
        await self.metrics_collector.collect(metrics)
        
        # 检查告警条件
        await self._check_alerts(metrics)
    
    async def _check_alerts(self, metrics: Dict):
        """检查告警条件"""
        alerts = []
        
        # 性能告警
        if metrics.get('latency', 0) > self.metrics_config['performance']['decision_latency']['threshold']:
            alerts.append({
                'type': 'performance',
                'severity': 'warning',
                'message': f"决策延迟过高: {metrics['latency']:.2f}s",
                'metrics': metrics
            })
        
        # 准确性告警
        if metrics.get('model_accuracy', 1.0) < self.metrics_config['business']['model_accuracy']['threshold']:
            alerts.append({
                'type': 'business',
                'severity': 'critical',
                'message': f"模型准确性下降: {metrics['model_accuracy']:.3f}",
                'metrics': metrics
            })
        
        # 发送告警
        for alert in alerts:
            await self.alert_manager.send_alert(alert)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics_buffer = []
        self.buffer_size = 1000
        self.flush_interval = 60  # 秒
        self.storage = MetricsStorage()
    
    async def start(self):
        """启动收集器"""
        # 定期刷新缓冲区
        asyncio.create_task(self._flush_periodically())
    
    async def collect(self, metrics: Dict):
        """收集指标"""
        self.metrics_buffer.append(metrics)
        
        # 缓冲区满时立即刷新
        if len(self.metrics_buffer) >= self.buffer_size:
            await self._flush_buffer()
    
    async def _flush_buffer(self):
        """刷新缓冲区"""
        if not self.metrics_buffer:
            return
        
        try:
            await self.storage.store_batch(self.metrics_buffer)
            logger.debug(f"已存储 {len(self.metrics_buffer)} 条指标")
            self.metrics_buffer.clear()
        except Exception as e:
            logger.error(f"指标存储失败: {e}")
    
    async def _flush_periodically(self):
        """定期刷新"""
        while True:
            await asyncio.sleep(self.flush_interval)
            await self._flush_buffer()


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.alert_channels = [
            EmailAlertChannel(),
            SlackAlertChannel(),
            WebhookAlertChannel()
        ]
        self.alert_rules = AlertRules()
        self.alert_history = []
    
    async def start(self):
        """启动告警管理器"""
        for channel in self.alert_channels:
            await channel.initialize()
    
    async def send_alert(self, alert: Dict):
        """发送告警"""
        # 检查告警规则
        if not await self.alert_rules.should_alert(alert):
            return
        
        # 记录告警历史
        alert['alert_id'] = str(uuid.uuid4())
        alert['timestamp'] = time.time()
        self.alert_history.append(alert)
        
        # 发送到各个渠道
        for channel in self.alert_channels:
            try:
                await channel.send(alert)
            except Exception as e:
                logger.error(f"告警发送失败 ({channel.__class__.__name__}): {e}")
    
    async def get_alert_summary(self, hours: int = 24) -> Dict:
        """获取告警摘要"""
        cutoff_time = time.time() - (hours * 3600)
        recent_alerts = [
            alert for alert in self.alert_history 
            if alert['timestamp'] > cutoff_time
        ]
        
        summary = {
            'total_alerts': len(recent_alerts),
            'by_severity': {},
            'by_type': {},
            'recent_alerts': recent_alerts[-10:]  # 最近10条
        }
        
        for alert in recent_alerts:
            severity = alert.get('severity', 'unknown')
            alert_type = alert.get('type', 'unknown')
            
            summary['by_severity'][severity] = summary['by_severity'].get(severity, 0) + 1
            summary['by_type'][alert_type] = summary['by_type'].get(alert_type, 0) + 1
        
        return summary


class AlertRules:
    """告警规则"""
    
    def __init__(self):
        self.rules = {
            'rate_limit': {
                'max_alerts_per_hour': 10,
                'max_alerts_per_type_per_hour': 3
            },
            'suppression': {
                'duplicate_window': 300,  # 5分钟内相同告警去重
                'escalation_window': 1800  # 30分钟内升级告警
            }
        }
        self.alert_counts = {}
        self.recent_alerts = []
    
    async def should_alert(self, alert: Dict) -> bool:
        """判断是否应该发送告警"""
        current_time = time.time()
        
        # 检查频率限制
        if not self._check_rate_limit(alert, current_time):
            return False
        
        # 检查重复告警
        if self._is_duplicate_alert(alert, current_time):
            return False
        
        # 更新计数和历史
        self._update_alert_tracking(alert, current_time)
        
        return True
    
    def _check_rate_limit(self, alert: Dict, current_time: float) -> bool:
        """检查频率限制"""
        hour_key = int(current_time // 3600)
        
        # 检查总体频率
        total_key = f"total_{hour_key}"
        if self.alert_counts.get(total_key, 0) >= self.rules['rate_limit']['max_alerts_per_hour']:
            return False
        
        # 检查类型频率
        type_key = f"{alert.get('type')}_{hour_key}"
        if self.alert_counts.get(type_key, 0) >= self.rules['rate_limit']['max_alerts_per_type_per_hour']:
            return False
        
        return True
    
    def _is_duplicate_alert(self, alert: Dict, current_time: float) -> bool:
        """检查是否为重复告警"""
        cutoff_time = current_time - self.rules['suppression']['duplicate_window']
        
        for recent_alert in self.recent_alerts:
            if recent_alert['timestamp'] < cutoff_time:
                continue
            
            if (recent_alert.get('type') == alert.get('type') and 
                recent_alert.get('message') == alert.get('message')):
                return True
        
        return False
    
    def _update_alert_tracking(self, alert: Dict, current_time: float):
        """更新告警跟踪"""
        hour_key = int(current_time // 3600)
        
        # 更新计数
        total_key = f"total_{hour_key}"
        type_key = f"{alert.get('type')}_{hour_key}"
        
        self.alert_counts[total_key] = self.alert_counts.get(total_key, 0) + 1
        self.alert_counts[type_key] = self.alert_counts.get(type_key, 0) + 1
        
        # 更新历史
        alert_copy = alert.copy()
        alert_copy['timestamp'] = current_time
        self.recent_alerts.append(alert_copy)
        
        # 清理过期数据
        cutoff_time = current_time - self.rules['suppression']['duplicate_window']
        self.recent_alerts = [
            a for a in self.recent_alerts 
            if a['timestamp'] > cutoff_time
        ]
```

### 2. 智能告警策略

```python
# ✅ 推荐:智能告警策略
class IntelligentAlertStrategy:
    """智能告警策略"""
    
    def __init__(self):
        self.anomaly_detector = AnomalyDetector()
        self.trend_analyzer = TrendAnalyzer()
        self.correlation_analyzer = CorrelationAnalyzer()
    
    async def analyze_and_alert(self, metrics: Dict) -> List[Dict]:
        """分析并生成智能告警"""
        alerts = []
        
        # 异常检测
        anomalies = await self.anomaly_detector.detect(metrics)
        for anomaly in anomalies:
            alerts.append({
                'type': 'anomaly',
                'severity': self._calculate_severity(anomaly),
                'message': f"检测到异常: {anomaly['description']}",
                'details': anomaly,
                'recommended_actions': self._get_recommended_actions(anomaly)
            })
        
        # 趋势分析
        trends = await self.trend_analyzer.analyze(metrics)
        for trend in trends:
            if trend['is_concerning']:
                alerts.append({
                    'type': 'trend',
                    'severity': 'warning',
                    'message': f"趋势告警: {trend['description']}",
                    'details': trend,
                    'prediction': trend.get('prediction')
                })
        
        # 关联分析
        correlations = await self.correlation_analyzer.analyze(metrics)
        for correlation in correlations:
            if correlation['strength'] > 0.8:
                alerts.append({
                    'type': 'correlation',
                    'severity': 'info',
                    'message': f"发现强关联: {correlation['description']}",
                    'details': correlation
                })
        
        return alerts
    
    def _calculate_severity(self, anomaly: Dict) -> str:
        """计算告警严重程度"""
        score = anomaly.get('anomaly_score', 0)
        
        if score > 0.9:
            return 'critical'
        elif score > 0.7:
            return 'warning'
        else:
            return 'info'
    
    def _get_recommended_actions(self, anomaly: Dict) -> List[str]:
        """获取推荐操作"""
        actions = []
        
        anomaly_type = anomaly.get('type')
        
        if anomaly_type == 'performance_degradation':
            actions.extend([
                "检查系统资源使用情况",
                "分析最近的配置变更",
                "检查数据库连接状态",
                "考虑扩容或优化"
            ])
        elif anomaly_type == 'accuracy_drop':
            actions.extend([
                "检查输入数据质量",
                "验证模型版本",
                "分析数据分布变化",
                "考虑重新训练模型"
            ])
        elif anomaly_type == 'high_error_rate':
            actions.extend([
                "检查错误日志",
                "验证外部依赖状态",
                "检查网络连接",
                "考虑启用降级模式"
            ])
        
        return actions


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self):
        self.models = {
            'isolation_forest': IsolationForest(contamination=0.1),
            'statistical': StatisticalAnomalyDetector(),
            'time_series': TimeSeriesAnomalyDetector()
        }
        self.history_window = 1000  # 保留最近1000个数据点
        self.metrics_history = defaultdict(list)
    
    async def detect(self, metrics: Dict) -> List[Dict]:
        """检测异常"""
        anomalies = []
        
        # 更新历史数据
        self._update_history(metrics)
        
        # 对每个指标进行异常检测
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)):
                metric_anomalies = await self._detect_metric_anomalies(
                    metric_name, value
                )
                anomalies.extend(metric_anomalies)
        
        return anomalies
    
    def _update_history(self, metrics: Dict):
        """更新历史数据"""
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)):
                history = self.metrics_history[metric_name]
                history.append(value)
                
                # 保持窗口大小
                if len(history) > self.history_window:
                    history.pop(0)
    
    async def _detect_metric_anomalies(self, metric_name: str, value: float) -> List[Dict]:
        """检测单个指标的异常"""
        anomalies = []
        history = self.metrics_history[metric_name]
        
        if len(history) < 10:  # 数据不足
            return anomalies
        
        # 统计异常检测
        if self._is_statistical_anomaly(history, value):
            anomalies.append({
                'type': 'statistical_anomaly',
                'metric': metric_name,
                'value': value,
                'description': f"{metric_name} 出现统计异常",
                'anomaly_score': self._calculate_statistical_score(history, value)
            })
        
        # 趋势异常检测
        if self._is_trend_anomaly(history, value):
            anomalies.append({
                'type': 'trend_anomaly',
                'metric': metric_name,
                'value': value,
                'description': f"{metric_name} 趋势异常",
                'anomaly_score': self._calculate_trend_score(history, value)
            })
        
        return anomalies
    
    def _is_statistical_anomaly(self, history: List[float], value: float) -> bool:
        """统计异常检测"""
        if len(history) < 10:
            return False
        
        mean = np.mean(history)
        std = np.std(history)
        
        # 3-sigma 规则
        return abs(value - mean) > 3 * std
    
    def _is_trend_anomaly(self, history: List[float], value: float) -> bool:
        """趋势异常检测"""
        if len(history) < 20:
            return False
        
        # 计算最近趋势
        recent_data = history[-10:]
        trend = np.polyfit(range(len(recent_data)), recent_data, 1)[0]
        
        # 预测值
        predicted = recent_data[-1] + trend
        
        # 检查偏差
        deviation = abs(value - predicted) / (abs(predicted) + 1e-6)
        
        return deviation > 0.5  # 偏差超过50%
    
    def _calculate_statistical_score(self, history: List[float], value: float) -> float:
        """计算统计异常分数"""
        mean = np.mean(history)
        std = np.std(history)
        
        if std == 0:
            return 0.0
        
        z_score = abs(value - mean) / std
        return min(z_score / 3.0, 1.0)  # 归一化到[0,1]
    
    def _calculate_trend_score(self, history: List[float], value: float) -> float:
        """计算趋势异常分数"""
        recent_data = history[-10:]
        trend = np.polyfit(range(len(recent_data)), recent_data, 1)[0]
        predicted = recent_data[-1] + trend
        
        deviation = abs(value - predicted) / (abs(predicted) + 1e-6)
        return min(deviation, 1.0)
```


## 🚀 部署策略最佳实践

### 1. 容器化部署

```python
# ✅ 推荐:容器化部署配置
class ContainerizedDeployment:
    """容器化部署管理"""
    
    def __init__(self):
        self.docker_config = self._get_docker_config()
        self.k8s_config = self._get_kubernetes_config()
    
    def _get_docker_config(self) -> Dict:
        """获取Docker配置"""
        return {
            'base_image': 'python:3.11-slim',
            'working_dir': '/app',
            'environment': {
                'PYTHONPATH': '/app',
                'PYTHONUNBUFFERED': '1',
                'TZ': 'Asia/Shanghai'
            },
            'health_check': {
                'test': ['CMD', 'python', '-c', 'import requests; requests.get("http://localhost:8000/health")'],
                'interval': '30s',
                'timeout': '10s',
                'retries': 3,
                'start_period': '60s'
            },
            'resource_limits': {
                'memory': '2g',
                'cpus': '1.0'
            }
        }
    
    def _get_kubernetes_config(self) -> Dict:
        """获取Kubernetes配置"""
        return {
            'deployment': {
                'replicas': 3,
                'strategy': {
                    'type': 'RollingUpdate',
                    'rollingUpdate': {
                        'maxSurge': 1,
                        'maxUnavailable': 0
                    }
                },
                'resources': {
                    'requests': {
                        'memory': '1Gi',
                        'cpu': '500m'
                    },
                    'limits': {
                        'memory': '2Gi',
                        'cpu': '1000m'
                    }
                },
                'probes': {
                    'liveness': {
                        'httpGet': {
                            'path': '/health',
                            'port': 8000
                        },
                        'initialDelaySeconds': 60,
                        'periodSeconds': 30
                    },
                    'readiness': {
                        'httpGet': {
                            'path': '/ready',
                            'port': 8000
                        },
                        'initialDelaySeconds': 10,
                        'periodSeconds': 5
                    }
                }
            },
            'service': {
                'type': 'ClusterIP',
                'ports': [{
                    'port': 80,
                    'targetPort': 8000,
                    'protocol': 'TCP'
                }]
            },
            'hpa': {
                'minReplicas': 2,
                'maxReplicas': 10,
                'targetCPUUtilizationPercentage': 70,
                'targetMemoryUtilizationPercentage': 80
            }
        }
    
    def generate_dockerfile(self) -> str:
        """生成Dockerfile"""
        return f'''
FROM {self.docker_config['base_image']}

WORKDIR {self.docker_config['working_dir']}

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
{self._format_env_vars()}

# 健康检查
HEALTHCHECK --interval={self.docker_config['health_check']['interval']} \
            --timeout={self.docker_config['health_check']['timeout']} \
            --start-period={self.docker_config['health_check']['start_period']} \
            --retries={self.docker_config['health_check']['retries']} \
            {' '.join(self.docker_config['health_check']['test'])}

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
'''
    
    def _format_env_vars(self) -> str:
        """格式化环境变量"""
        env_lines = []
        for key, value in self.docker_config['environment'].items():
            env_lines.append(f'ENV {key}={value}')
        return '\n'.join(env_lines)
    
    def generate_k8s_manifests(self) -> Dict[str, str]:
        """生成Kubernetes清单"""
        manifests = {}
        
        # Deployment
        manifests['deployment.yaml'] = self._generate_deployment_yaml()
        
        # Service
        manifests['service.yaml'] = self._generate_service_yaml()
        
        # HPA
        manifests['hpa.yaml'] = self._generate_hpa_yaml()
        
        # ConfigMap
        manifests['configmap.yaml'] = self._generate_configmap_yaml()
        
        # Secret
        manifests['secret.yaml'] = self._generate_secret_yaml()
        
        return manifests
```

### 2. 蓝绿部署策略

```python
# ✅ 推荐:蓝绿部署实现
class BlueGreenDeployment:
    """蓝绿部署管理器"""
    
    def __init__(self, k8s_client):
        self.k8s_client = k8s_client
        self.current_color = 'blue'
        self.deployment_config = DeploymentConfig()
    
    async def deploy(self, new_version: str, validation_tests: List[callable]) -> bool:
        """执行蓝绿部署"""
        target_color = 'green' if self.current_color == 'blue' else 'blue'
        
        try:
            # 1. 部署新版本到目标环境
            logger.info(f"开始部署版本 {new_version} 到 {target_color} 环境")
            await self._deploy_to_environment(target_color, new_version)
            
            # 2. 等待部署完成
            await self._wait_for_deployment_ready(target_color)
            
            # 3. 执行健康检查
            health_ok = await self._health_check(target_color)
            if not health_ok:
                raise Exception("健康检查失败")
            
            # 4. 执行验证测试
            validation_ok = await self._run_validation_tests(target_color, validation_tests)
            if not validation_ok:
                raise Exception("验证测试失败")
            
            # 5. 切换流量
            await self._switch_traffic(target_color)
            
            # 6. 验证切换后状态
            post_switch_ok = await self._post_switch_validation(target_color)
            if not post_switch_ok:
                # 回滚
                await self._switch_traffic(self.current_color)
                raise Exception("切换后验证失败,已回滚")
            
            # 7. 更新当前颜色
            old_color = self.current_color
            self.current_color = target_color
            
            # 8. 清理旧环境(可选)
            await self._cleanup_old_environment(old_color)
            
            logger.info(f"部署成功,当前环境: {self.current_color}")
            return True
            
        except Exception as e:
            logger.error(f"部署失败: {e}")
            # 清理失败的部署
            await self._cleanup_failed_deployment(target_color)
            return False
    
    async def _deploy_to_environment(self, color: str, version: str):
        """部署到指定环境"""
        deployment_name = f"decision-agent-{color}"
        
        # 更新部署配置
        deployment_spec = self.deployment_config.get_deployment_spec(
            name=deployment_name,
            version=version,
            color=color
        )
        
        # 应用部署
        await self.k8s_client.apply_deployment(deployment_spec)
    
    async def _wait_for_deployment_ready(self, color: str, timeout: int = 300):
        """等待部署就绪"""
        deployment_name = f"decision-agent-{color}"
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = await self.k8s_client.get_deployment_status(deployment_name)
            
            if status.get('ready_replicas', 0) == status.get('replicas', 0):
                logger.info(f"{color} 环境部署就绪")
                return
            
            await asyncio.sleep(10)
        
        raise Exception(f"{color} 环境部署超时")
    
    async def _health_check(self, color: str) -> bool:
        """健康检查"""
        service_url = f"http://decision-agent-{color}:8000"
        
        try:
            # 检查健康端点
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{service_url}/health") as response:
                    if response.status != 200:
                        return False
                    
                    health_data = await response.json()
                    return health_data.get('status') == 'healthy'
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    async def _run_validation_tests(self, color: str, tests: List[callable]) -> bool:
        """运行验证测试"""
        service_url = f"http://decision-agent-{color}:8000"
        
        for test in tests:
            try:
                result = await test(service_url)
                if not result:
                    logger.error(f"验证测试失败: {test.__name__}")
                    return False
            except Exception as e:
                logger.error(f"验证测试异常: {test.__name__}: {e}")
                return False
        
        logger.info("所有验证测试通过")
        return True
    
    async def _switch_traffic(self, target_color: str):
        """切换流量"""
        # 更新服务选择器
        service_spec = {
            'selector': {
                'app': 'decision-agent',
                'color': target_color
            }
        }
        
        await self.k8s_client.update_service('decision-agent', service_spec)
        logger.info(f"流量已切换到 {target_color} 环境")
    
    async def _post_switch_validation(self, color: str) -> bool:
        """切换后验证"""
        # 等待一段时间让流量稳定
        await asyncio.sleep(30)
        
        # 检查错误率
        error_rate = await self._get_error_rate()
        if error_rate > 0.05:  # 错误率超过5%
            logger.error(f"错误率过高: {error_rate:.2%}")
            return False
        
        # 检查响应时间
        avg_response_time = await self._get_avg_response_time()
        if avg_response_time > 5.0:  # 响应时间超过5秒
            logger.error(f"响应时间过长: {avg_response_time:.2f}s")
            return False
        
        return True
    
    async def rollback(self) -> bool:
        """回滚到上一个版本"""
        target_color = 'green' if self.current_color == 'blue' else 'blue'
        
        try:
            # 切换流量回上一个环境
            await self._switch_traffic(target_color)
            
            # 验证回滚
            rollback_ok = await self._post_switch_validation(target_color)
            if rollback_ok:
                self.current_color = target_color
                logger.info(f"回滚成功,当前环境: {self.current_color}")
                return True
            else:
                logger.error("回滚验证失败")
                return False
                
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            return False
```


## 📋 总结

### 核心最佳实践要点

1. **架构设计**
   - 采用模块化设计,确保组件解耦
   - 使用异步编程模式提高并发性能
   - 实施分层配置管理策略

2. **性能优化**
   - 实施多层缓存策略
   - 优化数据库连接和查询
   - 合理调优优化算法参数

3. **数据管理**
   - 严格的数据验证和清洗
   - 实时数据质量监控
   - 智能数据缓存机制

4. **模型管理**
   - 版本化模型管理
   - 实时性能监控
   - 自动化模型更新

5. **错误处理**
   - 分层错误处理策略
   - 智能重试机制
   - 优雅降级方案

6. **监控告警**
   - 全面的指标收集
   - 智能异常检测
   - 多渠道告警通知

7. **部署策略**
   - 容器化部署
   - 蓝绿部署策略
   - 自动化回滚机制

### 实施建议

1. **渐进式实施**:不要一次性实施所有最佳实践,而是根据项目需求逐步引入
2. **持续监控**:建立完善的监控体系,及时发现和解决问题
3. **定期评估**:定期评估系统性能和稳定性,持续优化
4. **团队培训**:确保团队成员了解和掌握这些最佳实践
5. **文档维护**:保持文档的及时更新,确保知识传承

### 相关文档

- [API参考文档](api_reference.md)
- [配置指南](configuration_guide.md)
- [故障排查指南](troubleshooting.md)
- [使用示例](usage_examples.md)
```