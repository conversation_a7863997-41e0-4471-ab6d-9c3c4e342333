# 配置指南

本指南详细介绍了异步决策智能体模块的配置方法,包括数据库配置、环境变量设置、优化参数调整等。

## 📋 目录



## 🗄️ 数据库配置

### PostgreSQL 配置

```python
# 基础数据库配置
dbconfig = {
    "host": "localhost",          # 数据库主机地址
    "port": 5432,                 # 数据库端口
    "database": "industry_db",    # 数据库名称
    "user": "username",           # 用户名
    "password": "password",       # 密码
    "sslmode": "prefer",          # SSL模式
    "connect_timeout": 30,        # 连接超时时间(秒)
    "command_timeout": 60,        # 命令超时时间(秒)
    "server_settings": {          # 服务器设置
        "application_name": "async_decision_agent",
        "timezone": "UTC"
    }
}
```

### 连接池配置

```python
# 高级数据库配置(连接池)
advanced_dbconfig = {
    "host": "localhost",
    "port": 5432,
    "database": "industry_db",
    "user": "username",
    "password": "password",
    
    # 连接池设置
    "min_size": 5,               # 最小连接数
    "max_size": 20,              # 最大连接数
    "max_queries": 50000,        # 每个连接最大查询数
    "max_inactive_connection_lifetime": 300,  # 非活跃连接生命周期
    
    # 重试设置
    "retry_attempts": 3,          # 重试次数
    "retry_delay": 1,             # 重试延迟(秒)
    
    # 健康检查
    "health_check_interval": 60,  # 健康检查间隔(秒)
}
```

### 时序数据库配置

```python
# InfluxDB 配置
influxdb_config = {
    "url": "http://localhost:8086",
    "token": "your-influxdb-token",
    "org": "your-organization",
    "bucket": "industry-data",
    "timeout": 30000,            # 超时时间(毫秒)
    "verify_ssl": True,          # SSL验证
    "enable_gzip": True,         # 启用压缩
    "retries": 3,                # 重试次数
}
```

## 🌍 环境变量配置

### .env 文件示例

```bash
# .env 文件
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=industry_db
DB_USER=username
DB_PASSWORD=password
DB_SSL_MODE=prefer

# 时序数据库配置
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=your-organization
INFLUXDB_BUCKET=industry-data

# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_LOG_LEVEL=INFO

# 模型配置
MODEL_PATH=/path/to/models
MODEL_CACHE_SIZE=100
MODEL_TIMEOUT=30

# 优化配置
OPT_POPULATION_SIZE=100
OPT_MAX_GENERATIONS=50
OPT_ALGORITHM=GA
OPT_CROSSOVER_PROB=0.9
OPT_MUTATION_PROB=0.1

# 监控配置
MONITOR_ENABLED=true
MONITOR_INTERVAL=300
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60

# 安全配置
API_KEY=your-api-key
SECRET_KEY=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

### 环境变量加载

```python
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # 数据库配置
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 5432))
    DB_NAME = os.getenv('DB_NAME', 'industry_db')
    DB_USER = os.getenv('DB_USER', 'username')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'password')
    DB_SSL_MODE = os.getenv('DB_SSL_MODE', 'prefer')
    
    # 时序数据库配置
    INFLUXDB_URL = os.getenv('INFLUXDB_URL', 'http://localhost:8086')
    INFLUXDB_TOKEN = os.getenv('INFLUXDB_TOKEN')
    INFLUXDB_ORG = os.getenv('INFLUXDB_ORG')
    INFLUXDB_BUCKET = os.getenv('INFLUXDB_BUCKET', 'industry-data')
    
    # 应用配置
    APP_ENV = os.getenv('APP_ENV', 'development')
    APP_DEBUG = os.getenv('APP_DEBUG', 'false').lower() == 'true'
    APP_LOG_LEVEL = os.getenv('APP_LOG_LEVEL', 'INFO')
    
    # 模型配置
    MODEL_PATH = os.getenv('MODEL_PATH', '/path/to/models')
    MODEL_CACHE_SIZE = int(os.getenv('MODEL_CACHE_SIZE', 100))
    MODEL_TIMEOUT = int(os.getenv('MODEL_TIMEOUT', 30))
    
    # 优化配置
    OPT_POPULATION_SIZE = int(os.getenv('OPT_POPULATION_SIZE', 100))
    OPT_MAX_GENERATIONS = int(os.getenv('OPT_MAX_GENERATIONS', 50))
    OPT_ALGORITHM = os.getenv('OPT_ALGORITHM', 'GA')
    OPT_CROSSOVER_PROB = float(os.getenv('OPT_CROSSOVER_PROB', 0.9))
    OPT_MUTATION_PROB = float(os.getenv('OPT_MUTATION_PROB', 0.1))
    
    # 监控配置
    MONITOR_ENABLED = os.getenv('MONITOR_ENABLED', 'true').lower() == 'true'
    MONITOR_INTERVAL = int(os.getenv('MONITOR_INTERVAL', 300))
    HEALTH_CHECK_ENABLED = os.getenv('HEALTH_CHECK_ENABLED', 'true').lower() == 'true'
    HEALTH_CHECK_INTERVAL = int(os.getenv('HEALTH_CHECK_INTERVAL', 60))
    
    @classmethod
    def get_db_config(cls):
        """获取数据库配置"""
        return {
            "host": cls.DB_HOST,
            "port": cls.DB_PORT,
            "database": cls.DB_NAME,
            "user": cls.DB_USER,
            "password": cls.DB_PASSWORD,
            "sslmode": cls.DB_SSL_MODE
        }
    
    @classmethod
    def get_influxdb_config(cls):
        """获取InfluxDB配置"""
        return {
            "url": cls.INFLUXDB_URL,
            "token": cls.INFLUXDB_TOKEN,
            "org": cls.INFLUXDB_ORG,
            "bucket": cls.INFLUXDB_BUCKET
        }
    
    @classmethod
    def validate(cls):
        """验证配置"""
        required_vars = [
            'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
            'INFLUXDB_URL', 'INFLUXDB_TOKEN', 'INFLUXDB_ORG'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")
        
        return True

# 使用配置
config = Config()
config.validate()

dbconfig = config.get_db_config()
influxdb_config = config.get_influxdb_config()
```

## ⚙️ 优化参数配置

### 基础优化参数

```python
# 遗传算法参数
ga_config = {
    "population_size": 100,       # 种群大小
    "max_generations": 50,        # 最大迭代次数
    "crossover_prob": 0.9,        # 交叉概率
    "mutation_prob": 0.1,         # 变异概率
    "selection_method": "tournament",  # 选择方法
    "tournament_size": 3,          # 锦标赛大小
    "elitism": True,               # 精英保留
    "elite_size": 2,               # 精英个体数量
}

# NSGA-II参数
nsga2_config = {
    "population_size": 100,
    "max_generations": 50,
    "crossover_prob": 0.9,
    "mutation_prob": 0.1,
    "crowding_distance": True,    # 拥挤距离
    "diversity_preservation": True, # 多样性保持
}

# 粒子群优化参数
pso_config = {
    "swarm_size": 50,             # 粒子群大小
    "max_iterations": 100,        # 最大迭代次数
    "inertia_weight": 0.9,         # 惯性权重
    "cognitive_coeff": 2.0,        # 认知系数
    "social_coeff": 2.0,           # 社会系数
    "velocity_clamp": 0.5,         # 速度限制
}
```

### 动态参数调整

```python
class DynamicOptimizationConfig:
    """动态优化配置"""
    
    def __init__(self):
        self.base_config = {
            "population_size": 100,
            "max_generations": 50,
            "crossover_prob": 0.9,
            "mutation_prob": 0.1
        }
        self.performance_history = []
        self.adjustment_threshold = 0.1
    
    def adjust_parameters(self, current_performance):
        """根据性能动态调整参数"""
        self.performance_history.append(current_performance)
        
        if len(self.performance_history) < 5:
            return self.base_config
        
        # 计算性能趋势
        recent_performance = self.performance_history[-5:]
        performance_trend = (recent_performance[-1] - recent_performance[0]) / recent_performance[0]
        
        config = self.base_config.copy()
        
        # 如果性能提升缓慢,增加种群多样性
        if abs(performance_trend) < self.adjustment_threshold:
            config["mutation_prob"] = min(0.3, config["mutation_prob"] * 1.2)
            config["population_size"] = min(200, int(config["population_size"] * 1.1))
        
        # 如果性能下降,增加选择压力
        elif performance_trend < -self.adjustment_threshold:
            config["crossover_prob"] = min(0.95, config["crossover_prob"] * 1.05)
            config["mutation_prob"] = max(0.05, config["mutation_prob"] * 0.9)
        
        return config
    
    def reset(self):
        """重置配置"""
        self.performance_history.clear()
        return self.base_config
```

## 🤖 模型配置

### 模型路径配置

```python
# 模型配置
model_config = {
    "model_base_path": "/path/to/models",
    "model_formats": [".pkl", ".joblib", ".h5", ".onnx"],
    "model_cache_size": 100,       # 模型缓存大小
    "model_timeout": 30,           # 模型加载超时时间
    "model_validation": True,      # 启用模型验证
    "auto_reload": False,          # 自动重新加载模型
    "reload_interval": 3600,       # 重新加载间隔(秒)
}

# 模型特定配置
model_specific_config = {
    "电耗模型": {
        "path": "/path/to/power_consumption_model.pkl",
        "input_features": ["feature1", "feature2", "feature3"],
        "output_range": [0, 1000],
        "normalization": "minmax",
        "validation_threshold": 0.95
    },
    "煤耗模型": {
        "path": "/path/to/coal_consumption_model.pkl",
        "input_features": ["feature1", "feature2", "feature4"],
        "output_range": [0, 500],
        "normalization": "standard",
        "validation_threshold": 0.90
    },
    "质量模型": {
        "path": "/path/to/quality_model.pkl",
        "input_features": ["feature1", "feature3", "feature5"],
        "output_range": [0, 100],
        "normalization": "robust",
        "validation_threshold": 0.85
    }
}
```

### 模型验证配置

```python
class ModelValidationConfig:
    """模型验证配置"""
    
    def __init__(self):
        self.validation_rules = {
            "input_shape_check": True,     # 检查输入形状
            "output_range_check": True,    # 检查输出范围
            "nan_check": True,             # 检查NaN值
            "inf_check": True,             # 检查无穷值
            "performance_check": True,     # 检查性能指标
        }
        
        self.performance_thresholds = {
            "accuracy": 0.8,               # 准确率阈值
            "r2_score": 0.7,               # R²分数阈值
            "mae": 10.0,                   # 平均绝对误差阈值
            "rmse": 15.0,                  # 均方根误差阈值
        }
        
        self.validation_data = {
            "test_size": 0.2,              # 测试集比例
            "random_state": 42,            # 随机种子
            "stratify": False,             # 分层采样
        }
    
    def validate_model(self, model, X_test, y_test):
        """验证模型"""
        validation_results = {}
        
        try:
            # 预测
            y_pred = model.predict(X_test)
            
            # 检查NaN和无穷值
            if self.validation_rules["nan_check"]:
                validation_results["has_nan"] = np.isnan(y_pred).any()
            
            if self.validation_rules["inf_check"]:
                validation_results["has_inf"] = np.isinf(y_pred).any()
            
            # 性能检查
            if self.validation_rules["performance_check"]:
                from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
                
                validation_results["r2_score"] = r2_score(y_test, y_pred)
                validation_results["mae"] = mean_absolute_error(y_test, y_pred)
                validation_results["rmse"] = np.sqrt(mean_squared_error(y_test, y_pred))
                
                # 检查是否满足阈值
                validation_results["meets_r2_threshold"] = validation_results["r2_score"] >= self.performance_thresholds["r2_score"]
                validation_results["meets_mae_threshold"] = validation_results["mae"] <= self.performance_thresholds["mae"]
                validation_results["meets_rmse_threshold"] = validation_results["rmse"] <= self.performance_thresholds["rmse"]
            
            # 整体验证结果
            validation_results["is_valid"] = (
                not validation_results.get("has_nan", False) and
                not validation_results.get("has_inf", False) and
                validation_results.get("meets_r2_threshold", True) and
                validation_results.get("meets_mae_threshold", True) and
                validation_results.get("meets_rmse_threshold", True)
            )
            
        except Exception as e:
            validation_results["error"] = str(e)
            validation_results["is_valid"] = False
        
        return validation_results
```

## 📝 日志配置

### 基础日志配置

```python
import logging
import logging.handlers
from datetime import datetime

# 日志配置
logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s(): %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "json": {
            "format": '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}',
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "standard",
            "stream": "ext://sys.stdout"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "detailed",
            "filename": "logs/async_decision_agent.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8"
        },
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "ERROR",
            "formatter": "detailed",
            "filename": "logs/async_decision_agent_errors.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 3,
            "encoding": "utf8"
        }
    },
    "loggers": {
        "async_decision_agent": {
            "level": "DEBUG",
            "handlers": ["console", "file", "error_file"],
            "propagate": False
        },
        "asyncio": {
            "level": "WARNING",
            "handlers": ["console"],
            "propagate": False
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["console"]
    }
}

# 应用日志配置
logging.config.dictConfig(logging_config)

# 创建日志器
logger = logging.getLogger("async_decision_agent")
```

### 结构化日志配置

```python
import json
from datetime import datetime

class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name="async_decision_agent"):
        self.logger = logging.getLogger(name)
        self.base_context = {
            "service": "async_decision_agent",
            "version": "1.0.0",
            "environment": os.getenv("APP_ENV", "development")
        }
    
    def _log(self, level, message, **kwargs):
        """记录结构化日志"""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            **self.base_context,
            **kwargs
        }
        
        getattr(self.logger, level.lower())(json.dumps(log_data, ensure_ascii=False))
    
    def info(self, message, **kwargs):
        """记录信息日志"""
        self._log("INFO", message, **kwargs)
    
    def warning(self, message, **kwargs):
        """记录警告日志"""
        self._log("WARNING", message, **kwargs)
    
    def error(self, message, **kwargs):
        """记录错误日志"""
        self._log("ERROR", message, **kwargs)
    
    def debug(self, message, **kwargs):
        """记录调试日志"""
        self._log("DEBUG", message, **kwargs)
    
    def decision_start(self, project_name, **kwargs):
        """记录决策开始"""
        self.info("决策流程开始", 
                 event_type="decision_start",
                 project_name=project_name,
                 **kwargs)
    
    def decision_complete(self, project_name, duration, **kwargs):
        """记录决策完成"""
        self.info("决策流程完成",
                 event_type="decision_complete",
                 project_name=project_name,
                 duration_seconds=duration,
                 **kwargs)
    
    def model_loaded(self, model_name, model_path, **kwargs):
        """记录模型加载"""
        self.info("模型加载成功",
                 event_type="model_loaded",
                 model_name=model_name,
                 model_path=model_path,
                 **kwargs)
    
    def optimization_result(self, algorithm, generations, best_fitness, **kwargs):
        """记录优化结果"""
        self.info("优化完成",
                 event_type="optimization_result",
                 algorithm=algorithm,
                 generations=generations,
                 best_fitness=best_fitness,
                 **kwargs)

# 使用结构化日志器
struct_logger = StructuredLogger()
```

## 🏭 生产环境配置

### Docker 配置

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV APP_ENV=production
ENV APP_LOG_LEVEL=INFO

# 暴露端口(如果需要)
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from health_check import check_health; asyncio.run(check_health())" || exit 1

# 启动命令
CMD ["python", "-m", "async_decision_agent"]
```

### Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  async-decision-agent:
    build: .
    container_name: async-decision-agent
    restart: unless-stopped
    environment:
      - APP_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=industry_db
      - DB_USER=postgres
      - DB_PASSWORD=password
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
      - INFLUXDB_ORG=${INFLUXDB_ORG}
      - INFLUXDB_BUCKET=industry-data
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
      - ./config:/app/config
    depends_on:
      - postgres
      - influxdb
    networks:
      - industry-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  postgres:
    image: postgres:13
    container_name: postgres-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=industry_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - industry-network

  influxdb:
    image: influxdb:2.0
    container_name: influxdb
    restart: unless-stopped
    environment:
      - INFLUXDB_DB=industry_data
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=password
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    networks:
      - industry-network

  redis:
    image: redis:6-alpine
    container_name: redis-cache
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - industry-network

volumes:
  postgres_data:
  influxdb_data:
  redis_data:

networks:
  industry-network:
    driver: bridge
```

### Kubernetes 配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: async-decision-agent
  labels:
    app: async-decision-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: async-decision-agent
  template:
    metadata:
      labels:
        app: async-decision-agent
    spec:
      containers:
      - name: async-decision-agent
        image: async-decision-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: APP_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: model-volume
          mountPath: /app/models
        - name: log-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: agent-config
      - name: model-volume
        persistentVolumeClaim:
          claimName: model-pvc
      - name: log-volume
        persistentVolumeClaim:
          claimName: log-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: async-decision-agent-service
spec:
  selector:
    app: async-decision-agent
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## ✅ 配置验证

### 配置验证器

```python
import json
import jsonschema
from typing import Dict, Any, List

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.schema = {
            "type": "object",
            "properties": {
                "database": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string"},
                        "port": {"type": "integer", "minimum": 1, "maximum": 65535},
                        "database": {"type": "string"},
                        "user": {"type": "string"},
                        "password": {"type": "string"}
                    },
                    "required": ["host", "port", "database", "user", "password"]
                },
                "optimization": {
                    "type": "object",
                    "properties": {
                        "population_size": {"type": "integer", "minimum": 10, "maximum": 1000},
                        "max_generations": {"type": "integer", "minimum": 1, "maximum": 1000},
                        "crossover_prob": {"type": "number", "minimum": 0, "maximum": 1},
                        "mutation_prob": {"type": "number", "minimum": 0, "maximum": 1}
                    },
                    "required": ["population_size", "max_generations"]
                },
                "models": {
                    "type": "object",
                    "properties": {
                        "model_path": {"type": "string"},
                        "cache_size": {"type": "integer", "minimum": 1},
                        "timeout": {"type": "integer", "minimum": 1}
                    },
                    "required": ["model_path"]
                },
                "logging": {
                    "type": "object",
                    "properties": {
                        "level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR"]},
                        "file_path": {"type": "string"},
                        "max_size": {"type": "integer", "minimum": 1}
                    }
                }
            },
            "required": ["database", "optimization", "models"]
        }
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """验证配置"""
        errors = []
        
        try:
            jsonschema.validate(config, self.schema)
        except jsonschema.ValidationError as e:
            errors.append(f"配置验证失败: {e.message}")
        except jsonschema.SchemaError as e:
            errors.append(f"配置模式错误: {e.message}")
        
        # 自定义验证规则
        errors.extend(self._custom_validation(config))
        
        return errors
    
    def _custom_validation(self, config: Dict[str, Any]) -> List[str]:
        """自定义验证规则"""
        errors = []
        
        # 检查数据库连接
        if "database" in config:
            db_config = config["database"]
            
            # 检查主机名
            if "host" in db_config and not db_config["host"].strip():
                errors.append("数据库主机名不能为空")
            
            # 检查端口范围
            if "port" in db_config:
                port = db_config["port"]
                if not (1 <= port <= 65535):
                    errors.append(f"数据库端口 {port} 超出有效范围 (1-65535)")
        
        # 检查优化参数
        if "optimization" in config:
            opt_config = config["optimization"]
            
            # 检查概率值
            for prob_key in ["crossover_prob", "mutation_prob"]:
                if prob_key in opt_config:
                    prob_value = opt_config[prob_key]
                    if not (0 <= prob_value <= 1):
                        errors.append(f"{prob_key} 必须在 0-1 之间")
            
            # 检查种群大小和迭代次数的合理性
            pop_size = opt_config.get("population_size", 0)
            max_gen = opt_config.get("max_generations", 0)
            
            if pop_size * max_gen > 100000:
                errors.append("种群大小 × 最大迭代次数过大,可能导致计算时间过长")
        
        # 检查模型路径
        if "models" in config:
            model_config = config["models"]
            
            if "model_path" in model_config:
                model_path = model_config["model_path"]
                if not os.path.exists(model_path):
                    errors.append(f"模型路径不存在: {model_path}")
        
        return errors
    
    def validate_file(self, config_file: str) -> List[str]:
        """验证配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return self.validate_config(config)
        except FileNotFoundError:
            return [f"配置文件不存在: {config_file}"]
        except json.JSONDecodeError as e:
            return [f"配置文件格式错误: {e}"]
        except Exception as e:
            return [f"读取配置文件失败: {e}"]

# 使用配置验证器
validator = ConfigValidator()

# 验证配置
config = {
    "database": {
        "host": "localhost",
        "port": 5432,
        "database": "industry_db",
        "user": "username",
        "password": "password"
    },
    "optimization": {
        "population_size": 100,
        "max_generations": 50,
        "crossover_prob": 0.9,
        "mutation_prob": 0.1
    },
    "models": {
        "model_path": "/path/to/models",
        "cache_size": 100,
        "timeout": 30
    }
}

errors = validator.validate_config(config)
if errors:
    print("配置验证失败:")
    for error in errors:
        print(f"  - {error}")
else:
    print("配置验证通过")
```

### 配置模板生成器

```python
class ConfigTemplateGenerator:
    """配置模板生成器"""
    
    def generate_basic_config(self) -> Dict[str, Any]:
        """生成基础配置模板"""
        return {
            "database": {
                "host": "localhost",
                "port": 5432,
                "database": "industry_db",
                "user": "username",
                "password": "password",
                "sslmode": "prefer",
                "connect_timeout": 30,
                "command_timeout": 60
            },
            "influxdb": {
                "url": "http://localhost:8086",
                "token": "your-influxdb-token",
                "org": "your-organization",
                "bucket": "industry-data",
                "timeout": 30000
            },
            "optimization": {
                "population_size": 100,
                "max_generations": 50,
                "crossover_prob": 0.9,
                "mutation_prob": 0.1,
                "algorithm": "GA"
            },
            "models": {
                "model_path": "/path/to/models",
                "cache_size": 100,
                "timeout": 30,
                "validation": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/async_decision_agent.log",
                "max_size": 10485760,
                "backup_count": 5
            },
            "monitoring": {
                "enabled": True,
                "interval": 300,
                "health_check_enabled": True,
                "health_check_interval": 60
            }
        }
    
    def generate_production_config(self) -> Dict[str, Any]:
        """生成生产环境配置模板"""
        config = self.generate_basic_config()
        
        # 生产环境优化
        config["database"].update({
            "min_size": 5,
            "max_size": 20,
            "max_queries": 50000,
            "max_inactive_connection_lifetime": 300
        })
        
        config["optimization"].update({
            "population_size": 200,
            "max_generations": 100
        })
        
        config["models"].update({
            "cache_size": 200,
            "auto_reload": True,
            "reload_interval": 3600
        })
        
        config["logging"].update({
            "level": "WARNING",
            "structured": True,
            "json_format": True
        })
        
        return config
    
    def save_template(self, config: Dict[str, Any], filename: str):
        """保存配置模板"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"配置模板已保存: {filename}")
        except Exception as e:
            print(f"保存配置模板失败: {e}")

# 生成配置模板
generator = ConfigTemplateGenerator()

# 生成基础配置
basic_config = generator.generate_basic_config()
generator.save_template(basic_config, "config_basic.json")

# 生成生产环境配置
production_config = generator.generate_production_config()
generator.save_template(production_config, "config_production.json")
```

## 📋 配置检查清单

### 部署前检查

- [ ] **数据库配置**
  - [ ] 数据库连接参数正确
  - [ ] 数据库用户权限充足
  - [ ] 网络连接可达
  - [ ] SSL配置正确

- [ ] **环境变量**
  - [ ] 所有必需的环境变量已设置
  - [ ] 敏感信息未硬编码
  - [ ] 环境变量值格式正确

- [ ] **模型文件**
  - [ ] 模型文件存在且可读
  - [ ] 模型版本兼容
  - [ ] 模型性能满足要求

- [ ] **优化参数**
  - [ ] 参数值在合理范围内
  - [ ] 计算资源充足
  - [ ] 算法选择合适

- [ ] **日志配置**
  - [ ] 日志级别适当
  - [ ] 日志文件路径可写
  - [ ] 日志轮转配置正确

- [ ] **监控配置**
  - [ ] 健康检查端点可访问
  - [ ] 监控指标收集正常
  - [ ] 告警规则配置正确

### 运行时检查

- [ ] **系统资源**
  - [ ] CPU使用率正常
  - [ ] 内存使用率正常
  - [ ] 磁盘空间充足
  - [ ] 网络连接稳定

- [ ] **应用状态**
  - [ ] 服务启动成功
  - [ ] 数据库连接正常
  - [ ] 模型加载成功
  - [ ] 决策流程运行正常

- [ ] **数据质量**
  - [ ] 输入数据完整
  - [ ] 数据格式正确
  - [ ] 数据范围合理
  - [ ] 无异常值

---

通过遵循本配置指南,您可以确保异步决策智能体模块在各种环境中稳定、高效地运行。建议定期检查和更新配置,以适应不断变化的业务需求和技术环境。