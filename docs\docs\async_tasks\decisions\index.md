# 异步决策智能体文档

欢迎使用异步决策智能体模块！本模块提供了强大的异步决策制定功能,支持工业场景下的智能优化决策。

## 📚 文档导航

### 🚀 快速开始

- [🔍 模块概览](async_decision_overview.md) - 详细的模块介绍和架构说明

### 📋 详细指南
- [🤖 基础决策智能体](async_basic_decision_agent.md) - `AsyncDecisionMaking` 类详细说明
- [🎯 多输出决策智能体](async_multi_output_decision_agent.md) - `AsyncMultiOutputDecisionMaking` 类详细说明
- [💡 使用示例](usage_examples.md) - 丰富的代码示例和应用场景
- [⚙️ 配置指南](configuration_guide.md) - 详细的配置方法和最佳实践

### 🔧 技术参考
- [📚 API参考](api_reference.md) - 完整的API文档
- [🏆 最佳实践](best_practices.md) - 架构设计、性能优化、部署策略
- [🔧 故障排查](troubleshooting.md) - 常见问题诊断和解决方案

## 🌟 核心特性

### 🚀 异步架构
- **非阻塞执行**:避免优化计算阻塞主线程
- **并发处理**:支持多个决策任务同时执行
- **资源管理**:智能的连接池和资源管理

### 🎯 智能优化
- **多算法支持**:遗传算法、粒子群优化、差分进化等
- **多目标优化**:支持单目标、双目标、三目标和多目标优化
- **自适应参数**:动态调整优化参数以提高收敛性能

### 📊 实时数据处理
- **数据库集成**:支持PostgreSQL和InfluxDB
- **数据验证**:严格的输入数据验证和清洗
- **缓存机制**:多层缓存提高数据访问性能

### 🏭 工业场景适配
- **水泥生产**:生产参数优化和质量控制
- **电力系统**:负荷调度和能耗优化
- **制造业**:生产计划和资源配置

## 🚀 快速开始

### 安装依赖
```bash
# 安装核心依赖
pip install asyncio asyncpg influxdb-client
pip install numpy pandas scikit-learn
pip install pymoo  # 多目标优化库
```

### 基础使用
```python
import asyncio
from industrytslib.core_async.async_decision_agents import AsyncDecisionMaking

async def main():
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="cement_production",
        db_config={
            "host": "localhost",
            "port": 5432,
            "database": "industry_db",
            "user": "user",
            "password": "password"
        }
    )
    
    # 执行决策
    result = await agent.make_decision("cement_production")
    print(f"决策结果: {result}")
    
    # 清理资源
    await agent.cleanup()

# 运行示例
asyncio.run(main())
```

### 多输出模型使用
```python
from industrytslib.core_async.async_decision_agents import AsyncMultiOutputDecisionMaking

async def multi_output_example():
    # 创建多输出决策智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="multi_objective_optimization",
        db_config={...},
        model_paths={
            "power_consumption": "/path/to/power_model.pkl",
            "coal_consumption": "/path/to/coal_model.pkl",
            "quality_index": "/path/to/quality_model.pkl"
        }
    )
    
    # 执行多目标决策
    result = await agent.make_decision("multi_objective_optimization")
    print(f"多目标决策结果: {result}")
    
    await agent.cleanup()

asyncio.run(multi_output_example())
```

## 🎯 使用场景

### 🏭 水泥生产优化
- 生产参数实时优化
- 能耗和质量平衡
- 设备状态监控

### ⚡ 电力系统调度
- 负荷预测和调度
- 能源配置优化
- 成本效益分析

### 🏗️ 制造业应用
- 生产计划优化
- 资源配置决策
- 质量控制管理

## 📈 性能特点

- **高并发**:支持数百个并发决策任务
- **低延迟**:毫秒级决策响应时间
- **高可用**:99.9%的系统可用性
- **可扩展**:支持水平扩展和负载均衡

## 🔗 相关链接

- [GitHub仓库](https://github.com/your-org/industrytslib)
- [问题反馈](https://github.com/your-org/industrytslib/issues)
- [贡献指南](https://github.com/your-org/industrytslib/blob/main/CONTRIBUTING.md)
- [更新日志](https://github.com/your-org/industrytslib/blob/main/CHANGELOG.md)

## 📞 技术支持

如果您在使用过程中遇到问题,请参考:

1. [故障排查指南](troubleshooting.md) - 常见问题的解决方案
2. [API参考文档](api_reference.md) - 详细的API说明
3. [最佳实践](best_practices.md) - 推荐的使用方法
4. [GitHub Issues](https://github.com/your-org/industrytslib/issues) - 提交问题和建议

---

*最后更新时间:2024年12月*