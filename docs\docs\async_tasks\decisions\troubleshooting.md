# 异步决策智能体故障排查指南

本文档提供了异步决策智能体模块常见问题的诊断和解决方案,帮助您快速定位和解决在使用过程中遇到的各种问题。

## 目录



## 常见问题分类

### 🔴 严重问题
- 数据库连接完全失败
- 模型文件损坏或丢失
- 内存溢出导致程序崩溃
- 优化算法无法收敛

### 🟡 警告问题
- 数据库连接不稳定
- 模型预测精度下降
- 优化执行时间过长
- 内存使用率过高

### 🟢 信息问题
- 配置参数不合理
- 日志记录不完整
- 性能监控缺失
- 文档版本不匹配

## 数据库连接问题

### 问题1:数据库连接失败

**症状:**
```
DatabaseConnectionError: 无法连接到数据库
Connection failed: [Microsoft][ODBC Driver 17 for SQL Server][TCP/IP Sockets]No connection could be made
```

**可能原因:**
1. 数据库服务器未启动
2. 网络连接问题
3. 防火墙阻止连接
4. 数据库配置错误
5. ODBC驱动未安装或版本不匹配

**解决方案:**

```python
# 1. 检查数据库配置
async def check_database_config():
    """检查数据库配置"""
    dbconfig = {
        "web_database": {
            "host": "*************",  # 确认IP地址正确
            "port": 1433,             # 确认端口正确
            "database": "IndustryDB", # 确认数据库名称
            "username": "admin",      # 确认用户名
            "password": "password123", # 确认密码
            "driver": "ODBC Driver 17 for SQL Server"  # 确认驱动版本
        }
    }
    
    # 测试连接
    try:
        import pyodbc
        conn_str = (
            f"DRIVER={{{dbconfig['web_database']['driver']}}};"
            f"SERVER={dbconfig['web_database']['host']},{dbconfig['web_database']['port']};"
            f"DATABASE={dbconfig['web_database']['database']};"
            f"UID={dbconfig['web_database']['username']};"
            f"PWD={dbconfig['web_database']['password']}"
        )
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功")
        conn.close()
    except Exception as e:
        print(f"数据库连接失败: {e}")

# 2. 安装正确的ODBC驱动
# Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install unixodbc unixodbc-dev
# curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
# curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
# sudo apt-get update
# sudo ACCEPT_EULA=Y apt-get install msodbcsql17

# 3. 检查网络连接
import socket

def check_network_connection(host, port):
    """检查网络连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        if result == 0:
            print(f"网络连接正常: {host}:{port}")
            return True
        else:
            print(f"网络连接失败: {host}:{port}")
            return False
    except Exception as e:
        print(f"网络检查异常: {e}")
        return False

# 使用示例
check_network_connection("*************", 1433)
```

### 问题2:数据库连接超时

**症状:**
```
Timeout expired. The timeout period elapsed prior to completion of the operation
```

**解决方案:**

```python
# 增加连接超时时间
class ImprovedAsyncDecisionMaking(AsyncDecisionMaking):
    """改进的异步决策智能体,增加超时处理"""
    
    async def async_database_connection(self, timeout=30):
        """带超时的数据库连接"""
        try:
            # 设置连接超时
            conn_str = self._build_connection_string()
            conn_str += f";Connection Timeout={timeout}"
            
            # 使用asyncio.wait_for设置总超时
            await asyncio.wait_for(
                super().async_database_connection(),
                timeout=timeout
            )
            
        except asyncio.TimeoutError:
            self.logger.error(f"数据库连接超时 ({timeout}秒)")
            raise DatabaseConnectionError("数据库连接超时")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    async def async_reconnect_with_backoff(self, max_retries=3):
        """带退避策略的重连"""
        for attempt in range(max_retries):
            try:
                wait_time = 2 ** attempt  # 指数退避
                self.logger.info(f"尝试重连数据库 (第 {attempt + 1} 次,等待 {wait_time} 秒)")
                
                await asyncio.sleep(wait_time)
                await self.async_database_connection(timeout=10 + attempt * 5)
                
                self.logger.info("数据库重连成功")
                return True
                
            except Exception as e:
                self.logger.warning(f"重连失败 (第 {attempt + 1} 次): {e}")
                if attempt == max_retries - 1:
                    raise DatabaseConnectionError("数据库重连失败,已达到最大重试次数")
        
        return False
```

### 问题3:数据库权限不足

**症状:**
```
Login failed for user 'username'. The user is not associated with a trusted SQL Server connection.
```

**解决方案:**

```sql
-- 1. 检查用户权限
SELECT 
    p.principal_id,
    p.name,
    p.type_desc,
    r.permission_name,
    r.state_desc
FROM sys.database_principals p
LEFT JOIN sys.database_permissions r ON p.principal_id = r.grantee_principal_id
WHERE p.name = 'your_username';

-- 2. 授予必要权限
GRANT SELECT, INSERT, UPDATE ON optimization_parameters TO your_username;
GRANT SELECT, INSERT, UPDATE ON model_information TO your_username;
GRANT SELECT, INSERT, UPDATE ON optimization_constraints TO your_username;
GRANT SELECT, INSERT, UPDATE ON time_series_data TO your_username;
GRANT SELECT, INSERT, UPDATE ON optimization_results TO your_username;

-- 3. 创建专用用户(推荐)
CREATE LOGIN decision_agent_user WITH PASSWORD = 'StrongPassword123!';
CREATE USER decision_agent_user FOR LOGIN decision_agent_user;
ALTER ROLE db_datareader ADD MEMBER decision_agent_user;
ALTER ROLE db_datawriter ADD MEMBER decision_agent_user;
```

## 模型加载问题

### 问题1:模型文件不存在

**症状:**
```
ModelLoadingError: 模型文件不存在: /path/to/model.pkl
FileNotFoundError: [Errno 2] No such file or directory
```

**解决方案:**

```python
import os
from pathlib import Path

class ModelValidator:
    """模型验证器"""
    
    @staticmethod
    def validate_model_files(model_paths):
        """验证模型文件"""
        missing_files = []
        corrupted_files = []
        
        for model_name, model_path in model_paths.items():
            path = Path(model_path)
            
            # 检查文件是否存在
            if not path.exists():
                missing_files.append((model_name, model_path))
                continue
            
            # 检查文件大小
            if path.stat().st_size == 0:
                corrupted_files.append((model_name, model_path))
                continue
            
            # 尝试加载模型
            try:
                if model_path.endswith('.pkl'):
                    import pickle
                    with open(model_path, 'rb') as f:
                        pickle.load(f)
                elif model_path.endswith('.pth'):
                    import torch
                    torch.load(model_path, map_location='cpu')
                elif model_path.endswith('.h5'):
                    import tensorflow as tf
                    tf.keras.models.load_model(model_path)
            except Exception as e:
                corrupted_files.append((model_name, f"{model_path}: {e}"))
        
        return missing_files, corrupted_files
    
    @staticmethod
    def create_backup_models(model_paths, backup_dir):
        """创建模型备份"""
        import shutil
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        for model_name, model_path in model_paths.items():
            if Path(model_path).exists():
                backup_file = backup_path / f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
                shutil.copy2(model_path, backup_file)
                print(f"模型备份完成: {backup_file}")

# 使用示例
model_paths = {
    "电耗模型": "/path/to/power_model.pkl",
    "煤耗模型": "/path/to/coal_model.pkl",
    "质量模型": "/path/to/quality_model.pkl"
}

validator = ModelValidator()
missing, corrupted = validator.validate_model_files(model_paths)

if missing:
    print("缺失的模型文件:")
    for name, path in missing:
        print(f"  {name}: {path}")

if corrupted:
    print("损坏的模型文件:")
    for name, error in corrupted:
        print(f"  {name}: {error}")
```

### 问题2:模型版本不兼容

**症状:**
```
RuntimeError: Attempting to deserialize object on a CUDA device but torch.cuda.is_available() is False
ValueError: cannot reshape array of size 100 into shape (10,20)
```

**解决方案:**

```python
class ModelCompatibilityChecker:
    """模型兼容性检查器"""
    
    @staticmethod
    def check_pytorch_model(model_path):
        """检查PyTorch模型兼容性"""
        try:
            import torch
            
            # 强制使用CPU加载
            model = torch.load(model_path, map_location='cpu')
            
            # 检查模型结构
            if hasattr(model, 'state_dict'):
                state_dict = model.state_dict()
                print(f"模型参数数量: {len(state_dict)}")
                
                # 检查参数形状
                for name, param in state_dict.items():
                    print(f"  {name}: {param.shape}")
            
            # 测试前向传播
            if hasattr(model, 'eval'):
                model.eval()
                # 创建测试输入
                test_input = torch.randn(1, 10)  # 根据实际输入维度调整
                with torch.no_grad():
                    output = model(test_input)
                    print(f"模型输出形状: {output.shape}")
            
            return True
            
        except Exception as e:
            print(f"PyTorch模型检查失败: {e}")
            return False
    
    @staticmethod
    def fix_model_device_issue(model_path, output_path):
        """修复模型设备问题"""
        try:
            import torch
            
            # 加载模型到CPU
            model = torch.load(model_path, map_location='cpu')
            
            # 确保所有参数都在CPU上
            if hasattr(model, 'cpu'):
                model = model.cpu()
            
            # 保存修复后的模型
            torch.save(model, output_path)
            print(f"模型设备问题已修复: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"模型设备问题修复失败: {e}")
            return False

# 使用示例
checker = ModelCompatibilityChecker()

# 检查模型兼容性
if not checker.check_pytorch_model("/path/to/model.pth"):
    # 尝试修复
    checker.fix_model_device_issue("/path/to/model.pth", "/path/to/model_fixed.pth")
```

### 问题3:模型输入输出维度不匹配

**症状:**
```
RuntimeError: mat1 and mat2 shapes cannot be multiplied (32x10 and 20x5)
ValueError: Input 0 of layer dense is incompatible with the layer
```

**解决方案:**

```python
class ModelDimensionValidator:
    """模型维度验证器"""
    
    def __init__(self, expected_input_dim, expected_output_dim):
        self.expected_input_dim = expected_input_dim
        self.expected_output_dim = expected_output_dim
    
    def validate_model_dimensions(self, model, model_type="pytorch"):
        """验证模型输入输出维度"""
        try:
            if model_type == "pytorch":
                return self._validate_pytorch_model(model)
            elif model_type == "tensorflow":
                return self._validate_tensorflow_model(model)
            elif model_type == "sklearn":
                return self._validate_sklearn_model(model)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
        
        except Exception as e:
            print(f"模型维度验证失败: {e}")
            return False
    
    def _validate_pytorch_model(self, model):
        """验证PyTorch模型维度"""
        import torch
        
        model.eval()
        test_input = torch.randn(1, self.expected_input_dim)
        
        with torch.no_grad():
            output = model(test_input)
            
            if output.shape[-1] != self.expected_output_dim:
                print(f"输出维度不匹配: 期望 {self.expected_output_dim}, 实际 {output.shape[-1]}")
                return False
            
            print(f"模型维度验证通过: 输入 {self.expected_input_dim}, 输出 {self.expected_output_dim}")
            return True
    
    def _validate_sklearn_model(self, model):
        """验证sklearn模型维度"""
        import numpy as np
        
        test_input = np.random.randn(1, self.expected_input_dim)
        output = model.predict(test_input)
        
        if output.shape[-1] != self.expected_output_dim:
            print(f"输出维度不匹配: 期望 {self.expected_output_dim}, 实际 {output.shape[-1]}")
            return False
        
        print(f"模型维度验证通过: 输入 {self.expected_input_dim}, 输出 {self.expected_output_dim}")
        return True
    
    def create_dimension_adapter(self, model, model_type="pytorch"):
        """创建维度适配器"""
        if model_type == "pytorch":
            return self._create_pytorch_adapter(model)
        else:
            raise NotImplementedError(f"暂不支持 {model_type} 的维度适配")
    
    def _create_pytorch_adapter(self, model):
        """创建PyTorch维度适配器"""
        import torch
        import torch.nn as nn
        
        class DimensionAdapter(nn.Module):
            def __init__(self, original_model, input_dim, output_dim):
                super().__init__()
                self.original_model = original_model
                
                # 添加输入适配层
                self.input_adapter = nn.Linear(input_dim, self._get_model_input_dim(original_model))
                
                # 添加输出适配层
                self.output_adapter = nn.Linear(self._get_model_output_dim(original_model), output_dim)
            
            def forward(self, x):
                x = self.input_adapter(x)
                x = self.original_model(x)
                x = self.output_adapter(x)
                return x
            
            def _get_model_input_dim(self, model):
                # 获取模型第一层的输入维度
                for module in model.modules():
                    if isinstance(module, nn.Linear):
                        return module.in_features
                return None
            
            def _get_model_output_dim(self, model):
                # 获取模型最后一层的输出维度
                for module in reversed(list(model.modules())):
                    if isinstance(module, nn.Linear):
                        return module.out_features
                return None
        
        return DimensionAdapter(model, self.expected_input_dim, self.expected_output_dim)

# 使用示例
validator = ModelDimensionValidator(expected_input_dim=10, expected_output_dim=3)

# 加载模型
import torch
model = torch.load("/path/to/model.pth", map_location='cpu')

# 验证维度
if not validator.validate_model_dimensions(model, "pytorch"):
    # 创建适配器
    adapted_model = validator.create_dimension_adapter(model, "pytorch")
    
    # 保存适配后的模型
    torch.save(adapted_model, "/path/to/adapted_model.pth")
    print("维度适配完成")
```

## 优化求解问题

### 问题1:优化算法不收敛

**症状:**
```
OptimizationError: 优化算法在最大迭代次数内未收敛
Warning: 目标函数值变化过小,可能陷入局部最优
```

**解决方案:**

```python
class OptimizationDiagnostics:
    """优化诊断工具"""
    
    def __init__(self):
        self.convergence_history = []
        self.objective_history = []
        self.population_diversity = []
    
    def diagnose_convergence_issue(self, problem, algorithm_params):
        """诊断收敛问题"""
        issues = []
        recommendations = []
        
        # 检查参数设置
        if algorithm_params.get("population_size", 0) < 50:
            issues.append("种群大小过小")
            recommendations.append("增加种群大小到100以上")
        
        if algorithm_params.get("max_generations", 0) < 100:
            issues.append("最大迭代次数过少")
            recommendations.append("增加最大迭代次数到200以上")
        
        if algorithm_params.get("mutation_prob", 0) < 0.01:
            issues.append("变异概率过低")
            recommendations.append("增加变异概率到0.1以上")
        
        # 检查目标函数
        try:
            test_solutions = self._generate_test_solutions(problem)
            objective_values = []
            
            for sol in test_solutions:
                try:
                    obj_val = problem._evaluate(sol)
                    objective_values.append(obj_val)
                except Exception as e:
                    issues.append(f"目标函数评估失败: {e}")
                    break
            
            if objective_values:
                obj_range = max(objective_values) - min(objective_values)
                if obj_range < 1e-6:
                    issues.append("目标函数值变化范围过小")
                    recommendations.append("检查目标函数定义和约束条件")
        
        except Exception as e:
            issues.append(f"目标函数测试失败: {e}")
        
        return issues, recommendations
    
    def _generate_test_solutions(self, problem, n_samples=10):
        """生成测试解"""
        import numpy as np
        
        solutions = []
        bounds = problem.bounds if hasattr(problem, 'bounds') else [(0, 1)] * 10
        
        for _ in range(n_samples):
            solution = []
            for lower, upper in bounds:
                solution.append(np.random.uniform(lower, upper))
            solutions.append(np.array(solution))
        
        return solutions
    
    def create_robust_optimization_config(self, problem_type="single_objective"):
        """创建鲁棒的优化配置"""
        if problem_type == "single_objective":
            return {
                "algorithm": "GA",
                "population_size": 200,
                "max_generations": 300,
                "crossover_prob": 0.8,
                "mutation_prob": 0.15,
                "selection_pressure": 2.0,
                "elitism": True,
                "diversity_preservation": True
            }
        elif problem_type == "multi_objective":
            return {
                "algorithm": "NSGA2",
                "population_size": 150,
                "max_generations": 250,
                "crossover_prob": 0.9,
                "mutation_prob": 0.1,
                "crowding_distance": True,
                "archive_size": 100
            }
        else:
            raise ValueError(f"不支持的问题类型: {problem_type}")

# 使用示例
diagnostics = OptimizationDiagnostics()

# 当前算法参数
current_params = {
    "population_size": 30,
    "max_generations": 50,
    "mutation_prob": 0.01
}

# 诊断问题
issues, recommendations = diagnostics.diagnose_convergence_issue(problem, current_params)

print("发现的问题:")
for issue in issues:
    print(f"  - {issue}")

print("\n建议的解决方案:")
for rec in recommendations:
    print(f"  - {rec}")

# 获取鲁棒配置
robust_config = diagnostics.create_robust_optimization_config("single_objective")
print(f"\n推荐的鲁棒配置: {robust_config}")
```

### 问题2:目标函数评估异常

**症状:**
```
RuntimeError: 模型预测过程中发生异常
ValueError: 输入数据包含NaN或无穷大值
```

**解决方案:**

```python
class ObjectiveFunctionValidator:
    """目标函数验证器"""
    
    def __init__(self, bounds, tolerance=1e-6):
        self.bounds = bounds
        self.tolerance = tolerance
        self.evaluation_cache = {}
    
    def validate_input(self, x):
        """验证输入数据"""
        import numpy as np
        
        # 检查数据类型
        if not isinstance(x, np.ndarray):
            x = np.array(x)
        
        # 检查NaN和无穷大
        if np.any(np.isnan(x)):
            raise ValueError("输入包含NaN值")
        
        if np.any(np.isinf(x)):
            raise ValueError("输入包含无穷大值")
        
        # 检查边界约束
        for i, (lower, upper) in enumerate(self.bounds):
            if x[i] < lower or x[i] > upper:
                raise ValueError(f"变量 {i} 超出边界: {x[i]} not in [{lower}, {upper}]")
        
        return x
    
    def safe_evaluate(self, objective_func, x, max_retries=3):
        """安全的目标函数评估"""
        import numpy as np
        
        # 验证输入
        x = self.validate_input(x)
        
        # 检查缓存
        x_key = tuple(x.round(6))  # 四舍五入避免浮点精度问题
        if x_key in self.evaluation_cache:
            return self.evaluation_cache[x_key]
        
        # 多次尝试评估
        for attempt in range(max_retries):
            try:
                result = objective_func(x)
                
                # 验证结果
                if isinstance(result, (list, tuple)):
                    result = np.array(result)
                
                if np.any(np.isnan(result)):
                    raise ValueError("目标函数返回NaN")
                
                if np.any(np.isinf(result)):
                    raise ValueError("目标函数返回无穷大")
                
                # 缓存结果
                self.evaluation_cache[x_key] = result
                return result
                
            except Exception as e:
                if attempt == max_retries - 1:
                    # 最后一次尝试失败,返回惩罚值
                    penalty_value = 1e6
                    print(f"目标函数评估失败,返回惩罚值: {e}")
                    return penalty_value
                else:
                    # 添加小的随机扰动重试
                    noise = np.random.normal(0, self.tolerance, x.shape)
                    x = x + noise
                    x = self.validate_input(x)  # 重新验证边界
    
    def create_robust_objective_wrapper(self, original_objective):
        """创建鲁棒的目标函数包装器"""
        def robust_objective(x):
            return self.safe_evaluate(original_objective, x)
        
        return robust_objective

# 使用示例
bounds = [(0, 100), (0, 100), (0, 100)]  # 变量边界
validator = ObjectiveFunctionValidator(bounds)

# 原始目标函数
def original_objective(x):
    # 可能出现异常的目标函数
    if x[0] < 0.1:  # 可能导致除零错误
        return 1.0 / x[0]
    return x[0]**2 + x[1]**2 + x[2]**2

# 创建鲁棒包装器
robust_objective = validator.create_robust_objective_wrapper(original_objective)

# 测试
test_inputs = [
    [0.05, 50, 50],  # 可能导致异常
    [50, 50, 50],    # 正常输入
    [float('nan'), 50, 50],  # NaN输入
]

for test_x in test_inputs:
    try:
        result = robust_objective(test_x)
        print(f"输入 {test_x} -> 输出 {result}")
    except Exception as e:
        print(f"输入 {test_x} -> 异常 {e}")
```

## 性能问题

### 问题1:执行时间过长

**症状:**
- 单次决策执行超过预期时间
- 系统响应缓慢
- 优化算法迭代速度慢

**解决方案:**

```python
import time
import asyncio
from functools import wraps

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.timing_data = {}
        self.call_counts = {}
    
    def profile_async(self, func_name=None):
        """异步函数性能分析装饰器"""
        def decorator(func):
            name = func_name or func.__name__
            
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    if name not in self.timing_data:
                        self.timing_data[name] = []
                        self.call_counts[name] = 0
                    
                    self.timing_data[name].append(execution_time)
                    self.call_counts[name] += 1
                    
                    if execution_time > 10:  # 超过10秒的调用
                        print(f"⚠️  {name} 执行时间过长: {execution_time:.2f}秒")
            
            return wrapper
        return decorator
    
    def get_performance_report(self):
        """获取性能报告"""
        import numpy as np
        
        report = {}
        for func_name, times in self.timing_data.items():
            report[func_name] = {
                "call_count": self.call_counts[func_name],
                "total_time": sum(times),
                "avg_time": np.mean(times),
                "min_time": min(times),
                "max_time": max(times),
                "std_time": np.std(times)
            }
        
        return report
    
    def identify_bottlenecks(self, threshold=5.0):
        """识别性能瓶颈"""
        bottlenecks = []
        report = self.get_performance_report()
        
        for func_name, stats in report.items():
            if stats["avg_time"] > threshold:
                bottlenecks.append({
                    "function": func_name,
                    "avg_time": stats["avg_time"],
                    "call_count": stats["call_count"],
                    "total_impact": stats["total_time"]
                })
        
        # 按总影响时间排序
        bottlenecks.sort(key=lambda x: x["total_impact"], reverse=True)
        return bottlenecks

# 优化的异步决策智能体
class OptimizedAsyncDecisionMaking(AsyncDecisionMaking):
    """性能优化的异步决策智能体"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.profiler = PerformanceProfiler()
        self.model_cache = {}  # 模型缓存
        self.data_cache = {}   # 数据缓存
        self.cache_ttl = 300   # 缓存生存时间(秒)
    
    @property
    def profiler_decorator(self):
        return self.profiler.profile_async
    
    @profiler_decorator("database_connection")
    async def async_database_connection(self):
        """带性能分析的数据库连接"""
        return await super().async_database_connection()
    
    @profiler_decorator("model_building")
    async def get_model_data_and_build_models(self):
        """带缓存的模型构建"""
        cache_key = f"models_{self.project_name}"
        current_time = time.time()
        
        # 检查缓存
        if cache_key in self.model_cache:
            cache_data, cache_time = self.model_cache[cache_key]
            if current_time - cache_time < self.cache_ttl:
                self.prediction_models = cache_data
                self.logger.info("使用缓存的模型数据")
                return
        
        # 构建模型
        await super().get_model_data_and_build_models()
        
        # 缓存模型
        self.model_cache[cache_key] = (self.prediction_models.copy(), current_time)
    
    @profiler_decorator("history_data_fetch")
    async def get_history_data(self):
        """带缓存的历史数据获取"""
        cache_key = f"history_{self.project_name}_{int(time.time() // 60)}"  # 按分钟缓存
        
        if cache_key in self.data_cache:
            self.logger.info("使用缓存的历史数据")
            return self.data_cache[cache_key]
        
        data = await super().get_history_data()
        self.data_cache[cache_key] = data
        
        # 清理过期缓存
        self._cleanup_cache()
        
        return data
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        
        # 清理模型缓存
        expired_keys = []
        for key, (_, cache_time) in self.model_cache.items():
            if current_time - cache_time > self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.model_cache[key]
        
        # 清理数据缓存(保留最近5个)
        if len(self.data_cache) > 5:
            sorted_keys = sorted(self.data_cache.keys())
            for key in sorted_keys[:-5]:
                del self.data_cache[key]
    
    @profiler_decorator("optimization_solving")
    def _solve_optimization_problem(self, problem):
        """带性能分析的优化求解"""
        return super()._solve_optimization_problem(problem)
    
    async def performance_summary(self):
        """获取性能摘要"""
        report = self.profiler.get_performance_report()
        bottlenecks = self.profiler.identify_bottlenecks()
        
        print("\n=== 性能分析报告 ===")
        for func_name, stats in report.items():
            print(f"{func_name}:")
            print(f"  调用次数: {stats['call_count']}")
            print(f"  平均耗时: {stats['avg_time']:.2f}秒")
            print(f"  总耗时: {stats['total_time']:.2f}秒")
            print(f"  最大耗时: {stats['max_time']:.2f}秒")
        
        if bottlenecks:
            print("\n=== 性能瓶颈 ===")
            for bottleneck in bottlenecks:
                print(f"{bottleneck['function']}: 平均 {bottleneck['avg_time']:.2f}秒")
        
        return report, bottlenecks

# 使用示例
async def performance_optimization_example():
    """性能优化示例"""
    
    # 创建优化的决策智能体
    agent = OptimizedAsyncDecisionMaking(
        project_name="性能优化测试",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行多次决策以收集性能数据
        for i in range(3):
            print(f"\n执行第 {i+1} 次决策...")
            await agent.main()
        
        # 获取性能报告
        await agent.performance_summary()
        
    finally:
        await agent.clean_up()

# 运行性能优化示例
# asyncio.run(performance_optimization_example())
```

### 问题2:内存使用过高

**症状:**
```
MemoryError: Unable to allocate array
OSError: [Errno 12] Cannot allocate memory
```

**解决方案:**

```python
import psutil
import gc
import sys
from typing import Optional

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_percent=80):
        self.max_memory_percent = max_memory_percent
        self.memory_alerts = []
    
    def get_memory_usage(self):
        """获取当前内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            "total": memory.total / (1024**3),  # GB
            "available": memory.available / (1024**3),  # GB
            "used": memory.used / (1024**3),  # GB
            "percent": memory.percent
        }
    
    def check_memory_threshold(self):
        """检查内存使用阈值"""
        usage = self.get_memory_usage()
        if usage["percent"] > self.max_memory_percent:
            alert = f"内存使用率过高: {usage['percent']:.1f}%"
            self.memory_alerts.append(alert)
            return False
        return True
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        before = self.get_memory_usage()
        
        # 执行垃圾回收
        collected = gc.collect()
        
        # 清理PyTorch缓存(如果可用)
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass
        
        after = self.get_memory_usage()
        freed = before["used"] - after["used"]
        
        print(f"垃圾回收完成: 回收对象 {collected} 个, 释放内存 {freed:.2f}GB")
        return freed
    
    def memory_efficient_data_processing(self, data, chunk_size=1000):
        """内存高效的数据处理"""
        import numpy as np
        
        if len(data) <= chunk_size:
            return data
        
        # 分块处理大数据
        results = []
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i+chunk_size]
            # 处理数据块
            processed_chunk = self._process_data_chunk(chunk)
            results.append(processed_chunk)
            
            # 检查内存使用
            if not self.check_memory_threshold():
                self.force_garbage_collection()
        
        return np.concatenate(results) if results else np.array([])
    
    def _process_data_chunk(self, chunk):
        """处理数据块"""
        # 这里实现具体的数据处理逻辑
        return chunk

class MemoryEfficientDecisionMaking(AsyncDecisionMaking):
    """内存高效的异步决策智能体"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.memory_manager = MemoryManager(max_memory_percent=75)
        self.batch_size = 100  # 批处理大小
    
    async def get_history_data(self):
        """内存高效的历史数据获取"""
        # 检查内存使用
        if not self.memory_manager.check_memory_threshold():
            self.memory_manager.force_garbage_collection()
        
        # 分批获取数据
        if self.local_test_mode:
            # 本地测试模式,生成较小的测试数据
            import numpy as np
            return np.random.randn(100, 10)  # 减少数据量
        else:
            # 从数据库分批获取数据
            return await self._get_history_data_in_batches()
    
    async def _get_history_data_in_batches(self):
        """分批获取历史数据"""
        import numpy as np
        
        all_data = []
        offset = 0
        
        while True:
            # 获取一批数据
            batch_data = await self._fetch_data_batch(offset, self.batch_size)
            
            if len(batch_data) == 0:
                break
            
            all_data.append(batch_data)
            offset += self.batch_size
            
            # 检查内存使用
            if not self.memory_manager.check_memory_threshold():
                self.logger.warning("内存使用率过高,停止数据获取")
                break
        
        if all_data:
            return np.vstack(all_data)
        else:
            return np.array([])
    
    async def _fetch_data_batch(self, offset, limit):
        """获取数据批次"""
        # 这里实现实际的数据库查询逻辑
        # 返回指定偏移量和限制的数据
        import numpy as np
        return np.random.randn(min(limit, 50), 10)  # 模拟数据
    
    def _solve_optimization_problem(self, problem):
        """内存高效的优化求解"""
        # 检查内存使用
        if not self.memory_manager.check_memory_threshold():
            self.memory_manager.force_garbage_collection()
            
            # 如果内存仍然不足,使用较小的种群大小
            if not self.memory_manager.check_memory_threshold():
                self.logger.warning("内存不足,使用较小的优化参数")
                self.termination["population_size"] = min(
                    self.termination.get("population_size", 100), 50
                )
                self.termination["max_generations"] = min(
                    self.termination.get("max_generations", 100), 50
                )
        
        return super()._solve_optimization_problem(problem)
    
    async def clean_up(self):
        """清理资源和内存"""
        # 清理模型缓存
        if hasattr(self, 'prediction_models'):
            self.prediction_models.clear()
        
        # 清理数据缓存
        if hasattr(self, 'constraint_table_data'):
            self.constraint_table_data.clear()
        
        # 强制垃圾回收
        self.memory_manager.force_garbage_collection()
        
        # 调用父类清理方法
        await super().clean_up()
        
        # 打印内存使用报告
        usage = self.memory_manager.get_memory_usage()
        self.logger.info(f"清理后内存使用: {usage['percent']:.1f}%")

# 内存监控装饰器
def monitor_memory(func):
    """内存监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        memory_manager = MemoryManager()
        
        before = memory_manager.get_memory_usage()
        print(f"执行前内存使用: {before['percent']:.1f}%")
        
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            after = memory_manager.get_memory_usage()
            print(f"执行后内存使用: {after['percent']:.1f}%")
            
            memory_diff = after["used"] - before["used"]
            if memory_diff > 0:
                print(f"内存增加: {memory_diff:.2f}GB")
            else:
                print(f"内存减少: {abs(memory_diff):.2f}GB")
    
    return wrapper

# 使用示例
@monitor_memory
async def memory_efficient_example():
    """内存高效示例"""
    
    agent = MemoryEfficientDecisionMaking(
        project_name="内存优化测试",
        dbconfig=dbconfig,
        local_test_mode=True  # 使用本地测试模式减少内存使用
    )
    
    try:
        await agent.main()
        
        # 检查内存告警
        if agent.memory_manager.memory_alerts:
            print("内存告警:")
            for alert in agent.memory_manager.memory_alerts:
                print(f"  - {alert}")
    
    finally:
        await agent.clean_up()

# 运行内存高效示例
# asyncio.run(memory_efficient_example())
```

## 配置问题

### 问题1:配置文件格式错误

**症状:**
```
JSONDecodeError: Expecting ',' delimiter
KeyError: 'database' key not found in configuration
```

**解决方案:**

```python
import json
import jsonschema
from typing import Dict, Any

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.schema = self._get_config_schema()
    
    def _get_config_schema(self):
        """获取配置文件JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "database": {
                    "type": "object",
                    "properties": {
                        "web_database": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string"},
                                "port": {"type": "integer", "minimum": 1, "maximum": 65535},
                                "database": {"type": "string"},
                                "username": {"type": "string"},
                                "password": {"type": "string"},
                                "driver": {"type": "string"}
                            },
                            "required": ["host", "port", "database", "username", "password", "driver"]
                        },
                        "ts_database": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string"},
                                "port": {"type": "integer", "minimum": 1, "maximum": 65535},
                                "database": {"type": "string"},
                                "username": {"type": "string"},
                                "password": {"type": "string"},
                                "driver": {"type": "string"}
                            },
                            "required": ["host", "port", "database", "username", "password", "driver"]
                        }
                    },
                    "required": ["web_database", "ts_database"]
                },
                "optimization": {
                    "type": "object",
                    "properties": {
                        "algorithm": {"type": "string", "enum": ["GA", "NSGA2", "NSGA3", "PSO", "DE"]},
                        "population_size": {"type": "integer", "minimum": 10, "maximum": 1000},
                        "max_generations": {"type": "integer", "minimum": 10, "maximum": 1000},
                        "crossover_prob": {"type": "number", "minimum": 0, "maximum": 1},
                        "mutation_prob": {"type": "number", "minimum": 0, "maximum": 1}
                    }
                },
                "agent": {
                    "type": "object",
                    "properties": {
                        "project_name": {"type": "string"},
                        "multi_output": {"type": "boolean"},
                        "local_test_mode": {"type": "boolean"},
                        "execution_interval": {"type": "integer", "minimum": 60}
                    },
                    "required": ["project_name"]
                }
            },
            "required": ["agent"]
        }
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, list]:
        """验证配置"""
        errors = []
        
        try:
            jsonschema.validate(config, self.schema)
            return True, []
        except jsonschema.ValidationError as e:
            errors.append(f"配置验证失败: {e.message}")
            errors.append(f"错误路径: {' -> '.join(str(p) for p in e.path)}")
            return False, errors
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
            return False, errors
    
    def load_and_validate_config(self, config_file: str) -> tuple[Dict[str, Any], list]:
        """加载并验证配置文件"""
        errors = []
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            errors.append(f"配置文件不存在: {config_file}")
            return {}, errors
        except json.JSONDecodeError as e:
            errors.append(f"JSON格式错误: {e}")
            return {}, errors
        except Exception as e:
            errors.append(f"配置文件读取失败: {e}")
            return {}, errors
        
        # 验证配置
        is_valid, validation_errors = self.validate_config(config)
        if not is_valid:
            errors.extend(validation_errors)
        
        return config, errors
    
    def create_default_config(self, output_file: str = "default_config.json"):
        """创建默认配置文件"""
        default_config = {
            "agent": {
                "project_name": "默认项目",
                "multi_output": False,
                "local_test_mode": True,
                "execution_interval": 300
            },
            "database": {
                "web_database": {
                    "host": "localhost",
                    "port": 1433,
                    "database": "IndustryDB",
                    "username": "admin",
                    "password": "password123",
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "ts_database": {
                    "host": "localhost",
                    "port": 1433,
                    "database": "TimeSeriesDB",
                    "username": "admin",
                    "password": "password123",
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            },
            "optimization": {
                "algorithm": "GA",
                "population_size": 100,
                "max_generations": 50,
                "crossover_prob": 0.8,
                "mutation_prob": 0.1
            },
            "monitoring": {
                "enable_prometheus": False,
                "metrics_port": 8000,
                "log_level": "INFO"
            }
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"默认配置文件已创建: {output_file}")
            return True
        except Exception as e:
            print(f"创建默认配置文件失败: {e}")
            return False
    
    def fix_common_config_issues(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """修复常见配置问题"""
        fixed_config = config.copy()
        
        # 确保必需的键存在
        if "agent" not in fixed_config:
            fixed_config["agent"] = {}
        
        if "project_name" not in fixed_config["agent"]:
            fixed_config["agent"]["project_name"] = "未命名项目"
        
        # 修复数据类型问题
        if "optimization" in fixed_config:
            opt_config = fixed_config["optimization"]
            
            # 确保整数类型
            for key in ["population_size", "max_generations"]:
                if key in opt_config and isinstance(opt_config[key], str):
                    try:
                        opt_config[key] = int(opt_config[key])
                    except ValueError:
                        pass
            
            # 确保浮点数类型
            for key in ["crossover_prob", "mutation_prob"]:
                if key in opt_config and isinstance(opt_config[key], str):
                    try:
                        opt_config[key] = float(opt_config[key])
                    except ValueError:
                        pass
        
        # 修复端口号问题
        if "database" in fixed_config:
            for db_key in ["web_database", "ts_database"]:
                if db_key in fixed_config["database"]:
                    db_config = fixed_config["database"][db_key]
                    if "port" in db_config and isinstance(db_config["port"], str):
                        try:
                            db_config["port"] = int(db_config["port"])
                        except ValueError:
                            db_config["port"] = 1433  # 默认端口
        
        return fixed_config

# 使用示例
validator = ConfigValidator()

# 验证配置文件
config, errors = validator.load_and_validate_config("config.json")

if errors:
    print("配置文件存在问题:")
    for error in errors:
        print(f"  - {error}")
    
    # 尝试修复
    if config:
        fixed_config = validator.fix_common_config_issues(config)
        is_valid, _ = validator.validate_config(fixed_config)
        
        if is_valid:
            print("配置问题已自动修复")
            # 保存修复后的配置
            with open("config_fixed.json", 'w', encoding='utf-8') as f:
                json.dump(fixed_config, f, indent=2, ensure_ascii=False)
else:
    print("配置文件验证通过")
```

### 问题2:环境变量配置错误

**症状:**
```
KeyError: 'DATABASE_HOST'
ValueError: invalid literal for int() with base 10: ''
```

**解决方案:**

```python
import os
from typing import Optional, Union

class EnvironmentManager:
    """环境变量管理器"""
    
    def __init__(self):
        self.required_vars = {
            "DATABASE_HOST": str,
            "DATABASE_PORT": int,
            "DATABASE_NAME": str,
            "DATABASE_USER": str,
            "DATABASE_PASSWORD": str
        }
        
        self.optional_vars = {
            "OPTIMIZATION_ALGORITHM": (str, "GA"),
            "POPULATION_SIZE": (int, 100),
            "MAX_GENERATIONS": (int, 50),
            "LOG_LEVEL": (str, "INFO"),
            "LOCAL_TEST_MODE": (bool, False)
        }
    
    def validate_environment(self) -> tuple[bool, list]:
        """验证环境变量"""
        errors = []
        
        # 检查必需的环境变量
        for var_name, var_type in self.required_vars.items():
            value = os.getenv(var_name)
            
            if value is None:
                errors.append(f"缺少必需的环境变量: {var_name}")
                continue
            
            # 验证数据类型
            try:
                if var_type == int:
                    int(value)
                elif var_type == float:
                    float(value)
                elif var_type == bool:
                    self._parse_bool(value)
            except ValueError:
                errors.append(f"环境变量 {var_name} 的值 '{value}' 不是有效的 {var_type.__name__} 类型")
        
        return len(errors) == 0, errors
    
    def get_env_var(self, name: str, var_type: type = str, default: Optional[Union[str, int, float, bool]] = None):
        """安全获取环境变量"""
        value = os.getenv(name, default)
        
        if value is None:
            return None
        
        try:
            if var_type == int:
                return int(value)
            elif var_type == float:
                return float(value)
            elif var_type == bool:
                return self._parse_bool(value)
            else:
                return str(value)
        except ValueError as e:
            raise ValueError(f"环境变量 {name} 的值 '{value}' 无法转换为 {var_type.__name__}: {e}")
    
    def _parse_bool(self, value: str) -> bool:
        """解析布尔值"""
        if isinstance(value, bool):
            return value
        
        value = str(value).lower()
        if value in ('true', '1', 'yes', 'on'):
            return True
        elif value in ('false', '0', 'no', 'off'):
            return False
        else:
            raise ValueError(f"无法解析布尔值: {value}")
    
    def create_env_template(self, output_file: str = ".env.template"):
        """创建环境变量模板文件"""
        template_content = [
            "# 异步决策智能体环境变量配置",
            "# 复制此文件为 .env 并填入实际值",
            "",
            "# 数据库配置 (必需)",
            "DATABASE_HOST=localhost",
            "DATABASE_PORT=1433",
            "DATABASE_NAME=IndustryDB",
            "DATABASE_USER=admin",
            "DATABASE_PASSWORD=your_password_here",
            "",
            "# 时序数据库配置 (可选,如果与主数据库不同)",
            "TS_DATABASE_HOST=localhost",
            "TS_DATABASE_PORT=1433",
            "TS_DATABASE_NAME=TimeSeriesDB",
            "TS_DATABASE_USER=admin",
            "TS_DATABASE_PASSWORD=your_password_here",
            "",
            "# 优化算法配置 (可选)",
            "OPTIMIZATION_ALGORITHM=GA",
            "POPULATION_SIZE=100",
            "MAX_GENERATIONS=50",
            "CROSSOVER_PROB=0.8",
            "MUTATION_PROB=0.1",
            "",
            "# 应用配置 (可选)",
            "PROJECT_NAME=我的项目",
            "LOCAL_TEST_MODE=false",
            "LOG_LEVEL=INFO",
            "EXECUTION_INTERVAL=300",
            "",
            "# 监控配置 (可选)",
            "ENABLE_PROMETHEUS=false",
            "METRICS_PORT=8000"
        ]
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(template_content))
            print(f"环境变量模板已创建: {output_file}")
            return True
        except Exception as e:
            print(f"创建环境变量模板失败: {e}")
            return False
    
    def load_env_file(self, env_file: str = ".env"):
        """加载 .env 文件"""
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print(f"环境变量文件已加载: {env_file}")
            return True
        except FileNotFoundError:
            print(f"环境变量文件不存在: {env_file}")
            return False
        except Exception as e:
            print(f"加载环境变量文件失败: {e}")
            return False

# 使用示例
env_manager = EnvironmentManager()

# 加载 .env 文件
env_manager.load_env_file()

# 验证环境变量
is_valid, errors = env_manager.validate_environment()

if not is_valid:
    print("环境变量配置有问题:")
    for error in errors:
        print(f"  - {error}")
    
    # 创建模板文件
    env_manager.create_env_template()
    print("请参考 .env.template 文件配置环境变量")
else:
    print("环境变量配置正确")

# 安全获取环境变量
try:
    db_host = env_manager.get_env_var("DATABASE_HOST", str)
    db_port = env_manager.get_env_var("DATABASE_PORT", int)
    local_mode = env_manager.get_env_var("LOCAL_TEST_MODE", bool, False)
    
    print(f"数据库配置: {db_host}:{db_port}")
    print(f"本地测试模式: {local_mode}")
    
except ValueError as e:
    print(f"环境变量配置错误: {e}")
```

## 日志分析

### 日志级别和格式配置

```python
import logging
import sys
from datetime import datetime
from pathlib import Path

class LoggingConfigurator:
    """日志配置器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
    
    def setup_logging(self, 
                     level: str = "INFO",
                     enable_file_logging: bool = True,
                     enable_console_logging: bool = True,
                     max_file_size: int = 10 * 1024 * 1024,  # 10MB
                     backup_count: int = 5):
        """设置日志配置"""
        
        # 创建根日志器
        logger = logging.getLogger()
        logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if enable_console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if enable_file_logging:
            from logging.handlers import RotatingFileHandler
            
            # 主日志文件
            main_log_file = self.log_dir / f"decision_agent_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = RotatingFileHandler(
                main_log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = self.log_dir / f"decision_agent_error_{datetime.now().strftime('%Y%m%d')}.log"
            error_handler = RotatingFileHandler(
                error_log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            logger.addHandler(error_handler)
        
        return logger
    
    def analyze_logs(self, log_file: str = None, days: int = 7):
        """分析日志文件"""
        if log_file is None:
            # 查找最新的日志文件
            log_files = list(self.log_dir.glob("decision_agent_*.log"))
            if not log_files:
                print("未找到日志文件")
                return
            log_file = max(log_files, key=lambda f: f.stat().st_mtime)
        
        analysis_result = {
            "total_lines": 0,
            "error_count": 0,
            "warning_count": 0,
            "info_count": 0,
            "debug_count": 0,
            "common_errors": {},
            "performance_issues": [],
            "database_issues": [],
            "optimization_issues": []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    analysis_result["total_lines"] += 1
                    
                    # 统计日志级别
                    if " - ERROR - " in line:
                        analysis_result["error_count"] += 1
                        self._analyze_error_line(line, analysis_result)
                    elif " - WARNING - " in line:
                        analysis_result["warning_count"] += 1
                        self._analyze_warning_line(line, analysis_result)
                    elif " - INFO - " in line:
                        analysis_result["info_count"] += 1
                    elif " - DEBUG - " in line:
                        analysis_result["debug_count"] += 1
        
        except Exception as e:
            print(f"日志分析失败: {e}")
            return None
        
        return analysis_result
    
    def _analyze_error_line(self, line: str, analysis_result: dict):
        """分析错误日志行"""
        # 提取错误类型
        if "DatabaseConnectionError" in line:
            analysis_result["database_issues"].append(line.strip())
        elif "OptimizationError" in line:
            analysis_result["optimization_issues"].append(line.strip())
        elif "MemoryError" in line or "OutOfMemoryError" in line:
            analysis_result["performance_issues"].append(line.strip())
        
        # 统计常见错误
        for error_type in ["ConnectionError", "TimeoutError", "MemoryError", "ValueError", "KeyError"]:
            if error_type in line:
                if error_type not in analysis_result["common_errors"]:
                    analysis_result["common_errors"][error_type] = 0
                analysis_result["common_errors"][error_type] += 1
    
    def _analyze_warning_line(self, line: str, analysis_result: dict):
        """分析警告日志行"""
        if "执行时间过长" in line or "timeout" in line.lower():
            analysis_result["performance_issues"].append(line.strip())
        elif "内存使用率过高" in line or "memory" in line.lower():
            analysis_result["performance_issues"].append(line.strip())
    
    def generate_log_report(self, analysis_result: dict):
        """生成日志分析报告"""
        if not analysis_result:
            return "无法生成日志报告"
        
        report = []
        report.append("=== 日志分析报告 ===")
        report.append(f"总日志行数: {analysis_result['total_lines']}")
        report.append(f"错误数量: {analysis_result['error_count']}")
        report.append(f"警告数量: {analysis_result['warning_count']}")
        report.append(f"信息数量: {analysis_result['info_count']}")
        report.append(f"调试数量: {analysis_result['debug_count']}")
        report.append("")
        
        # 常见错误统计
        if analysis_result["common_errors"]:
            report.append("=== 常见错误统计 ===")
            for error_type, count in sorted(analysis_result["common_errors"].items(), 
                                          key=lambda x: x[1], reverse=True):
                report.append(f"{error_type}: {count} 次")
            report.append("")
        
        # 数据库问题
        if analysis_result["database_issues"]:
            report.append("=== 数据库问题 ===")
            for issue in analysis_result["database_issues"][:5]:  # 显示前5个
                report.append(f"  - {issue}")
            if len(analysis_result["database_issues"]) > 5:
                report.append(f"  ... 还有 {len(analysis_result['database_issues']) - 5} 个问题")
            report.append("")
        
        # 性能问题
        if analysis_result["performance_issues"]:
            report.append("=== 性能问题 ===")
            for issue in analysis_result["performance_issues"][:5]:  # 显示前5个
                report.append(f"  - {issue}")
            if len(analysis_result["performance_issues"]) > 5:
                report.append(f"  ... 还有 {len(analysis_result['performance_issues']) - 5} 个问题")
            report.append("")
        
        # 优化问题
        if analysis_result["optimization_issues"]:
            report.append("=== 优化问题 ===")
            for issue in analysis_result["optimization_issues"][:5]:  # 显示前5个
                report.append(f"  - {issue}")
            if len(analysis_result["optimization_issues"]) > 5:
                report.append(f"  ... 还有 {len(analysis_result['optimization_issues']) - 5} 个问题")
        
        return "\n".join(report)

# 使用示例
log_config = LoggingConfigurator()

# 设置日志配置
logger = log_config.setup_logging(
    level="INFO",
    enable_file_logging=True,
    enable_console_logging=True
)

# 分析日志
analysis = log_config.analyze_logs()
if analysis:
    report = log_config.generate_log_report(analysis)
    print(report)
    
    # 保存报告
    with open("log_analysis_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)
```

## 调试工具

### 交互式调试器

```python
import asyncio
import pdb
from typing import Any, Dict

class AsyncDebugger:
    """异步调试器"""
    
    def __init__(self, agent):
        self.agent = agent
        self.breakpoints = set()
        self.step_mode = False
        self.debug_data = {}
    
    def set_breakpoint(self, method_name: str):
        """设置断点"""
        self.breakpoints.add(method_name)
        print(f"断点已设置: {method_name}")
    
    def remove_breakpoint(self, method_name: str):
        """移除断点"""
        if method_name in self.breakpoints:
            self.breakpoints.remove(method_name)
            print(f"断点已移除: {method_name}")
    
    def enable_step_mode(self):
        """启用单步模式"""
        self.step_mode = True
        print("单步调试模式已启用")
    
    def disable_step_mode(self):
        """禁用单步模式"""
        self.step_mode = False
        print("单步调试模式已禁用")
    
    async def debug_method(self, method_name: str, *args, **kwargs):
        """调试方法执行"""
        print(f"\n=== 调试方法: {method_name} ===")
        
        # 检查断点
        if method_name in self.breakpoints or self.step_mode:
            print(f"断点触发: {method_name}")
            print(f"参数: args={args}, kwargs={kwargs}")
            
            # 进入调试模式
            print("进入调试模式 (输入 'c' 继续, 'q' 退出, 'i' 查看信息)")
            while True:
                command = input("(debug) ").strip().lower()
                
                if command == 'c':
                    break
                elif command == 'q':
                    raise KeyboardInterrupt("用户中断调试")
                elif command == 'i':
                    self._show_debug_info()
                elif command.startswith('eval '):
                    try:
                        result = eval(command[5:])
                        print(f"结果: {result}")
                    except Exception as e:
                        print(f"评估错误: {e}")
                else:
                    print("可用命令: c(继续), q(退出), i(信息), eval <表达式>")
        
        # 执行方法
        try:
            method = getattr(self.agent, method_name)
            if asyncio.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                result = method(*args, **kwargs)
            
            # 保存调试数据
            self.debug_data[method_name] = {
                "args": args,
                "kwargs": kwargs,
                "result": result,
                "success": True
            }
            
            return result
            
        except Exception as e:
            print(f"方法执行异常: {e}")
            self.debug_data[method_name] = {
                "args": args,
                "kwargs": kwargs,
                "error": str(e),
                "success": False
            }
            raise
    
    def _show_debug_info(self):
        """显示调试信息"""
        print("\n=== 调试信息 ===")
        print(f"智能体项目: {self.agent.project_name}")
        print(f"本地测试模式: {getattr(self.agent, 'local_test_mode', 'Unknown')}")
        print(f"断点列表: {list(self.breakpoints)}")
        print(f"单步模式: {self.step_mode}")
        
        if hasattr(self.agent, 'prediction_models'):
            print(f"已加载模型数量: {len(self.agent.prediction_models)}")
        
        if self.debug_data:
            print("\n最近执行的方法:")
            for method, data in list(self.debug_data.items())[-3:]:
                status = "成功" if data["success"] else "失败"
                print(f"  {method}: {status}")
        print("=================\n")
    
    def get_debug_report(self):
        """获取调试报告"""
        return {
            "breakpoints": list(self.breakpoints),
            "step_mode": self.step_mode,
            "execution_history": self.debug_data.copy()
        }

class DebuggableAsyncDecisionMaking(AsyncDecisionMaking):
    """可调试的异步决策智能体"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.debugger = AsyncDebugger(self)
        self.debug_enabled = False
    
    def enable_debug(self):
        """启用调试模式"""
        self.debug_enabled = True
        print("调试模式已启用")
    
    def disable_debug(self):
        """禁用调试模式"""
        self.debug_enabled = False
        print("调试模式已禁用")
    
    async def async_database_connection(self):
        """可调试的数据库连接"""
        if self.debug_enabled:
            return await self.debugger.debug_method("async_database_connection")
        return await super().async_database_connection()
    
    async def get_model_data_and_build_models(self):
        """可调试的模型构建"""
        if self.debug_enabled:
            return await self.debugger.debug_method("get_model_data_and_build_models")
        return await super().get_model_data_and_build_models()
    
    async def get_history_data(self):
        """可调试的历史数据获取"""
        if self.debug_enabled:
            return await self.debugger.debug_method("get_history_data")
        return await super().get_history_data()
    
    def _solve_optimization_problem(self, problem):
        """可调试的优化求解"""
        if self.debug_enabled:
            # 注意:这是同步方法,需要特殊处理
            print(f"\n=== 调试方法: _solve_optimization_problem ===")
            if "_solve_optimization_problem" in self.debugger.breakpoints:
                print("断点触发: _solve_optimization_problem")
                pdb.set_trace()  # 使用标准调试器
        
        return super()._solve_optimization_problem(problem)

# 使用示例
async def debug_example():
    """调试示例"""
    
    # 创建可调试的智能体
    agent = DebuggableAsyncDecisionMaking(
        project_name="调试测试",
        dbconfig=dbconfig,
        local_test_mode=True
    )
    
    # 启用调试
    agent.enable_debug()
    
    # 设置断点
    agent.debugger.set_breakpoint("async_database_connection")
    agent.debugger.set_breakpoint("get_model_data_and_build_models")
    
    try:
        # 执行决策流程(会在断点处停止)
        await agent.main()
        
        # 获取调试报告
        debug_report = agent.debugger.get_debug_report()
        print(f"\n调试报告: {debug_report}")
        
    except KeyboardInterrupt:
        print("调试被用户中断")
    finally:
        await agent.clean_up()

# 运行调试示例
# asyncio.run(debug_example())
```

### 性能分析工具

```python
import cProfile
import pstats
import io
from functools import wraps

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.profiler = None
        self.profile_data = {}
    
    def start_profiling(self):
        """开始性能分析"""
        self.profiler = cProfile.Profile()
        self.profiler.enable()
        print("性能分析已开始")
    
    def stop_profiling(self):
        """停止性能分析"""
        if self.profiler:
            self.profiler.disable()
            print("性能分析已停止")
    
    def get_profile_stats(self, sort_by='cumulative', top_n=20):
        """获取性能统计"""
        if not self.profiler:
            return "未进行性能分析"
        
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s)
        ps.sort_stats(sort_by)
        ps.print_stats(top_n)
        
        return s.getvalue()
    
    def save_profile_report(self, filename="performance_report.txt"):
        """保存性能报告"""
        report = self.get_profile_stats()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"性能报告已保存: {filename}")
            return True
        except Exception as e:
            print(f"保存性能报告失败: {e}")
            return False
    
    def profile_function(self, func):
        """函数性能分析装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            profiler = cProfile.Profile()
            profiler.enable()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                profiler.disable()
                
                # 保存函数性能数据
                s = io.StringIO()
                ps = pstats.Stats(profiler, stream=s)
                ps.sort_stats('cumulative')
                ps.print_stats(10)
                
                self.profile_data[func.__name__] = s.getvalue()
        
        return wrapper
    
    def profile_async_function(self, func):
        """异步函数性能分析装饰器"""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            profiler = cProfile.Profile()
            profiler.enable()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                profiler.disable()
                
                # 保存函数性能数据
                s = io.StringIO()
                ps = pstats.Stats(profiler, stream=s)
                ps.sort_stats('cumulative')
                ps.print_stats(10)
                
                self.profile_data[func.__name__] = s.getvalue()
        
        return wrapper

# 使用示例
analyzer = PerformanceAnalyzer()

# 分析整个决策流程
async def profile_decision_process():
    """分析决策流程性能"""
    
    agent = AsyncDecisionMaking(
        project_name="性能分析测试",
        dbconfig=dbconfig,
        local_test_mode=True
    )
    
    # 开始性能分析
    analyzer.start_profiling()
    
    try:
        await agent.main()
    finally:
        # 停止性能分析
        analyzer.stop_profiling()
        
        # 获取并保存报告
        stats = analyzer.get_profile_stats()
        print("\n=== 性能分析报告 ===")
        print(stats)
        
        analyzer.save_profile_report("decision_performance.txt")
        
        await agent.clean_up()

# 运行性能分析
# asyncio.run(profile_decision_process())
```

## 预防措施

### 健康检查系统

```python
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any

class HealthChecker:
    """健康检查器"""
    
    def __init__(self, agent):
        self.agent = agent
        self.health_status = {
            "overall": "unknown",
            "database": "unknown",
            "models": "unknown",
            "optimization": "unknown",
            "memory": "unknown"
        }
        self.last_check_time = None
        self.check_interval = 300  # 5分钟
    
    async def perform_health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        print("开始健康检查...")
        
        checks = {
            "database": self._check_database_health,
            "models": self._check_models_health,
            "optimization": self._check_optimization_health,
            "memory": self._check_memory_health
        }
        
        results = {}
        overall_healthy = True
        
        for check_name, check_func in checks.items():
            try:
                result = await check_func()
                results[check_name] = result
                
                if not result["healthy"]:
                    overall_healthy = False
                    
            except Exception as e:
                results[check_name] = {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                overall_healthy = False
        
        # 更新整体状态
        results["overall"] = {
            "healthy": overall_healthy,
            "timestamp": datetime.now().isoformat()
        }
        
        self.health_status = results
        self.last_check_time = datetime.now()
        
        return results
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            # 检查数据库连接
            if not hasattr(self.agent, 'web_database_client') or self.agent.web_database_client is None:
                await self.agent.async_database_connection()
            
            # 执行简单查询测试
            test_query = "SELECT 1 as test"
            result = await self.agent.web_database_client.fetch_all(test_query)
            
            return {
                "healthy": True,
                "message": "数据库连接正常",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": "数据库连接失败",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _check_models_health(self) -> Dict[str, Any]:
        """检查模型健康状态"""
        try:
            if not hasattr(self.agent, 'prediction_models') or not self.agent.prediction_models:
                await self.agent.get_model_data_and_build_models()
            
            model_count = len(self.agent.prediction_models)
            
            if model_count == 0:
                return {
                    "healthy": False,
                    "message": "未加载任何模型",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 测试模型预测
            import numpy as np
            test_input = np.random.randn(1, 10)  # 假设输入维度为10
            
            for model_name, model in self.agent.prediction_models.items():
                try:
                    prediction = model.predict(test_input)
                    if np.any(np.isnan(prediction)):
                        return {
                            "healthy": False,
                            "message": f"模型 {model_name} 预测结果包含NaN",
                            "timestamp": datetime.now().isoformat()
                        }
                except Exception as e:
                    return {
                        "healthy": False,
                        "message": f"模型 {model_name} 预测失败: {e}",
                        "timestamp": datetime.now().isoformat()
                    }
            
            return {
                "healthy": True,
                "message": f"已加载 {model_count} 个模型,预测功能正常",
                "model_count": model_count,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": "模型检查失败",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _check_optimization_health(self) -> Dict[str, Any]:
        """检查优化算法健康状态"""
        try:
            # 检查优化参数
            if not hasattr(self.agent, 'termination') or not self.agent.termination:
                await self.agent.get_optimization_parameter()
            
            # 验证优化参数的合理性
            required_params = ['population_size', 'max_generations']
            for param in required_params:
                if param not in self.agent.termination:
                    return {
                        "healthy": False,
                        "message": f"缺少优化参数: {param}",
                        "timestamp": datetime.now().isoformat()
                    }
                
                value = self.agent.termination[param]
                if not isinstance(value, (int, float)) or value <= 0:
                    return {
                        "healthy": False,
                        "message": f"优化参数 {param} 值无效: {value}",
                        "timestamp": datetime.now().isoformat()
                    }
            
            return {
                "healthy": True,
                "message": "优化参数配置正常",
                "parameters": self.agent.termination,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": "优化算法检查失败",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _check_memory_health(self) -> Dict[str, Any]:
        """检查内存健康状态"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 检查内存使用率
            if memory_percent > 90:
                return {
                    "healthy": False,
                    "message": f"内存使用率过高: {memory_percent:.1f}%",
                    "memory_percent": memory_percent,
                    "timestamp": datetime.now().isoformat()
                }
            elif memory_percent > 80:
                return {
                    "healthy": True,
                    "message": f"内存使用率较高: {memory_percent:.1f}%",
                    "memory_percent": memory_percent,
                    "warning": True,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "healthy": True,
                    "message": f"内存使用率正常: {memory_percent:.1f}%",
                    "memory_percent": memory_percent,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "message": "内存检查失败",
                "timestamp": datetime.now().isoformat()
            }
    
    def should_perform_check(self) -> bool:
        """判断是否应该执行健康检查"""
        if self.last_check_time is None:
            return True
        
        time_since_last_check = datetime.now() - self.last_check_time
        return time_since_last_check.total_seconds() >= self.check_interval
    
    def get_health_summary(self) -> str:
        """获取健康状态摘要"""
        if not self.health_status:
            return "未进行健康检查"
        
        summary = ["=== 健康状态摘要 ==="]
        
        overall = self.health_status.get("overall", {})
        if overall.get("healthy", False):
            summary.append("✅ 整体状态: 健康")
        else:
            summary.append("❌ 整体状态: 异常")
        
        for component, status in self.health_status.items():
            if component == "overall":
                continue
            
            if status.get("healthy", False):
                icon = "✅"
                if status.get("warning", False):
                    icon = "⚠️"
            else:
                icon = "❌"
            
            message = status.get("message", "未知状态")
            summary.append(f"{icon} {component}: {message}")
        
        if self.last_check_time:
            summary.append(f"\n最后检查时间: {self.last_check_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(summary)

# 使用示例
async def health_check_example():
    """健康检查示例"""
    
    agent = AsyncDecisionMaking(
        project_name="健康检查测试",
        dbconfig=dbconfig,
        local_test_mode=True
    )
    
    health_checker = HealthChecker(agent)
    
    try:
        # 执行健康检查
        health_results = await health_checker.perform_health_check()
        
        # 显示健康状态摘要
        summary = health_checker.get_health_summary()
        print(summary)
        
        # 保存健康检查报告
        with open("health_check_report.json", 'w', encoding='utf-8') as f:
            import json
            json.dump(health_results, f, indent=2, ensure_ascii=False)
        
        print("\n健康检查报告已保存: health_check_report.json")
        
    finally:
        await agent.clean_up()

# 运行健康检查示例
# asyncio.run(health_check_example())
```

### 自动恢复机制

```python
class AutoRecoveryManager:
    """自动恢复管理器"""
    
    def __init__(self, agent, max_retry_attempts=3):
        self.agent = agent
        self.max_retry_attempts = max_retry_attempts
        self.recovery_strategies = {
            "database_connection": self._recover_database_connection,
            "model_loading": self._recover_model_loading,
            "optimization_failure": self._recover_optimization_failure,
            "memory_overflow": self._recover_memory_overflow
        }
        self.recovery_history = []
    
    async def attempt_recovery(self, error_type: str, error_details: str) -> bool:
        """尝试自动恢复"""
        if error_type not in self.recovery_strategies:
            print(f"未知错误类型,无法自动恢复: {error_type}")
            return False
        
        recovery_func = self.recovery_strategies[error_type]
        
        for attempt in range(self.max_retry_attempts):
            try:
                print(f"尝试自动恢复 {error_type} (第 {attempt + 1} 次)...")
                
                success = await recovery_func(error_details, attempt)
                
                if success:
                    print(f"自动恢复成功: {error_type}")
                    self.recovery_history.append({
                        "error_type": error_type,
                        "error_details": error_details,
                        "recovery_attempt": attempt + 1,
                        "success": True,
                        "timestamp": datetime.now().isoformat()
                    })
                    return True
                
            except Exception as e:
                print(f"恢复尝试失败: {e}")
        
        print(f"自动恢复失败: {error_type}")
        self.recovery_history.append({
            "error_type": error_type,
            "error_details": error_details,
            "recovery_attempt": self.max_retry_attempts,
            "success": False,
            "timestamp": datetime.now().isoformat()
        })
        return False
    
    async def _recover_database_connection(self, error_details: str, attempt: int) -> bool:
        """恢复数据库连接"""
        try:
            # 等待一段时间后重试
            wait_time = 2 ** attempt  # 指数退避
            await asyncio.sleep(wait_time)
            
            # 清理现有连接
            if hasattr(self.agent, 'web_database_client'):
                self.agent.web_database_client = None
            if hasattr(self.agent, 'ts_database_client'):
                self.agent.ts_database_client = None
            
            # 重新建立连接
            await self.agent.async_database_connection()
            
            # 测试连接
            test_query = "SELECT 1 as test"
            await self.agent.web_database_client.fetch_all(test_query)
            
            return True
            
        except Exception as e:
            print(f"数据库连接恢复失败: {e}")
            return False
    
    async def _recover_model_loading(self, error_details: str, attempt: int) -> bool:
        """恢复模型加载"""
        try:
            # 清理现有模型
            if hasattr(self.agent, 'prediction_models'):
                self.agent.prediction_models.clear()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 重新加载模型
            await self.agent.get_model_data_and_build_models()
            
            # 验证模型
            if not self.agent.prediction_models:
                return False
            
            return True
            
        except Exception as e:
            print(f"模型加载恢复失败: {e}")
            return False
    
    async def _recover_optimization_failure(self, error_details: str, attempt: int) -> bool:
        """恢复优化失败"""
        try:
            # 调整优化参数
            if hasattr(self.agent, 'termination'):
                # 减少种群大小和迭代次数
                self.agent.termination['population_size'] = max(
                    self.agent.termination.get('population_size', 100) // 2, 20
                )
                self.agent.termination['max_generations'] = max(
                    self.agent.termination.get('max_generations', 100) // 2, 10
                )
                
                print(f"调整优化参数: 种群大小={self.agent.termination['population_size']}, "
                      f"最大迭代={self.agent.termination['max_generations']}")
            
            return True
            
        except Exception as e:
            print(f"优化参数调整失败: {e}")
            return False
    
    async def _recover_memory_overflow(self, error_details: str, attempt: int) -> bool:
        """恢复内存溢出"""
        try:
            import gc
            
            # 强制垃圾回收
            collected = gc.collect()
            print(f"垃圾回收: 回收了 {collected} 个对象")
            
            # 清理缓存
            if hasattr(self.agent, 'model_cache'):
                self.agent.model_cache.clear()
            if hasattr(self.agent, 'data_cache'):
                self.agent.data_cache.clear()
            
            # 清理PyTorch缓存
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            # 检查内存使用
            import psutil
            memory = psutil.virtual_memory()
            if memory.percent < 80:  # 如果内存使用率降到80%以下
                return True
            
            return False
            
        except Exception as e:
            print(f"内存恢复失败: {e}")
            return False
    
    def get_recovery_report(self) -> str:
        """获取恢复报告"""
        if not self.recovery_history:
            return "无恢复记录"
        
        report = ["=== 自动恢复报告 ==="]
        
        success_count = sum(1 for r in self.recovery_history if r["success"])
        total_count = len(self.recovery_history)
        
        report.append(f"总恢复尝试: {total_count}")
        report.append(f"成功恢复: {success_count}")
        report.append(f"成功率: {success_count/total_count*100:.1f}%")
        report.append("")
        
        # 按错误类型统计
        error_types = {}
        for record in self.recovery_history:
            error_type = record["error_type"]
            if error_type not in error_types:
                error_types[error_type] = {"total": 0, "success": 0}
            error_types[error_type]["total"] += 1
            if record["success"]:
                error_types[error_type]["success"] += 1
        
        report.append("=== 按错误类型统计 ===")
        for error_type, stats in error_types.items():
            success_rate = stats["success"] / stats["total"] * 100
            report.append(f"{error_type}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        return "\n".join(report)

# 使用示例
async def auto_recovery_example():
    """自动恢复示例"""
    
    agent = AsyncDecisionMaking(
        project_name="自动恢复测试",
        dbconfig=dbconfig,
        local_test_mode=True
    )
    
    recovery_manager = AutoRecoveryManager(agent)
    
    try:
        # 模拟数据库连接失败
        success = await recovery_manager.attempt_recovery(
            "database_connection", 
            "Connection timeout"
        )
        
        if success:
            print("数据库连接已恢复")
        else:
            print("数据库连接恢复失败")
        
        # 获取恢复报告
        report = recovery_manager.get_recovery_report()
        print(f"\n{report}")
        
    finally:
        await agent.clean_up()

# 运行自动恢复示例
# asyncio.run(auto_recovery_example())
```

## 总结

本故障排查指南涵盖了异步决策智能体模块的主要问题类型和解决方案:

### 🔧 主要工具
- **配置验证器**: 自动检查和修复配置问题
- **环境管理器**: 管理和验证环境变量
- **性能分析器**: 识别性能瓶颈和优化机会
- **内存管理器**: 监控和优化内存使用
- **日志分析器**: 自动分析日志文件并生成报告
- **调试器**: 交互式调试和问题定位
- **健康检查器**: 定期检查系统健康状态
- **自动恢复管理器**: 自动处理常见故障

### 📋 最佳实践
1. **预防为主**: 使用健康检查和监控系统
2. **快速定位**: 利用日志分析和调试工具
3. **自动恢复**: 实现常见问题的自动恢复机制
4. **性能优化**: 定期进行性能分析和优化
5. **文档记录**: 保持详细的故障和恢复记录

### 🚨 紧急处理流程
1. **立即响应**: 检查系统整体状态
2. **问题定位**: 使用日志和调试工具快速定位
3. **尝试恢复**: 使用自动恢复机制或手动修复
4. **验证修复**: 确认问题已解决
5. **记录总结**: 更新故障处理文档

通过使用本指南中的工具和方法,您可以快速诊断和解决异步决策智能体模块中的各种问题,确保系统的稳定运行。