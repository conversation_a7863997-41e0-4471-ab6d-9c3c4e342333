# 异步决策智能体使用示例

本文档提供了异步决策智能体模块的详细使用示例,涵盖从基础使用到生产环境部署的各种场景。通过这些示例,您可以快速掌握如何在实际项目中使用异步决策智能体进行工业优化。

## 目录



## 基础使用示例

### 简单决策任务

```python
import asyncio
import logging
from industrytslib.core_aysnc.async_decision_agents import AsyncDecisionMaking

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def simple_decision_example():
    """简单决策任务示例"""
    
    # 数据库配置
    dbconfig = {
        "web_database": {
            "host": "*************",
            "port": 1433,
            "database": "IndustryDB",
            "username": "admin",
            "password": "password123",
            "driver": "ODBC Driver 17 for SQL Server"
        },
        "ts_database": {
            "host": "*************",
            "port": 1433,
            "database": "TimeSeriesDB",
            "username": "admin",
            "password": "password123",
            "driver": "ODBC Driver 17 for SQL Server"
        }
    }
    
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="水泥A磨优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行决策任务
        await agent.main()
        logging.info("决策任务执行完成")
    except Exception as e:
        logging.error(f"决策任务执行失败: {e}")
    finally:
        # 清理资源
        await agent.clean_up()

# 运行示例
if __name__ == "__main__":
    asyncio.run(simple_decision_example())
```

### 本地测试模式

```python
async def local_test_example():
    """本地测试模式示例"""
    
    # 本地测试不需要真实数据库配置
    dbconfig = {}
    
    # 创建测试智能体
    agent = AsyncDecisionMaking(
        project_name="本地测试项目",
        dbconfig=dbconfig,
        local_test_mode=True  # 启用本地测试模式
    )
    
    try:
        await agent.main()
        print("本地测试完成")
    except Exception as e:
        print(f"本地测试失败: {e}")

# 运行本地测试
asyncio.run(local_test_example())
```

### 自定义优化参数

```python
async def custom_optimization_example():
    """自定义优化参数示例"""
    
    agent = AsyncDecisionMaking(
        project_name="自定义优化项目",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 建立数据库连接
        await agent.async_database_connection()
        
        # 获取并修改优化参数
        opt_params = await agent.get_optimization_parameter()
        opt_params.update({
            "optimization_algorithm": "NSGA2",
            "population_size": 200,
            "max_generations": 100,
            "optimization_function_type": "dual_objective",
            "crossover_prob": 0.9,
            "mutation_prob": 0.1
        })
        
        # 设置优化参数
        agent._set_optimization_interval_time(opt_params)
        agent._set_termination(opt_params)
        
        # 构建模型
        await agent.get_model_data_and_build_models()
        
        # 执行决策
        if opt_params.get("device_operation_flag"):
            await agent.decision_flag()
        else:
            await agent.decision_no_flag()
        
        logging.info("自定义优化完成")
        
    except Exception as e:
        logging.error(f"自定义优化失败: {e}")
    finally:
        await agent.clean_up()

asyncio.run(custom_optimization_example())
```

## 多输出决策示例

### 基础多输出决策

```python
import torch
import numpy as np
from industrytslib.core_aysnc.async_decision_agents import AsyncMultiOutputDecisionMaking

# 模拟多输出模型
class MultiOutputModel(torch.nn.Module):
    """多输出预测模型示例"""
    
    def __init__(self, input_size=10, hidden_size=20, output_size=5):
        super().__init__()
        self.linear1 = torch.nn.Linear(input_size, hidden_size)
        self.linear2 = torch.nn.Linear(hidden_size, output_size)
        self.dropout = torch.nn.Dropout(0.1)
    
    def forward(self, x):
        x = torch.relu(self.linear1(x))
        x = self.dropout(x)
        return self.linear2(x)  # 返回多维输出

async def multi_output_decision_example():
    """多输出决策示例"""
    
    # 创建多输出决策智能体
    agent = AsyncMultiOutputDecisionMaking(
        project_name="多输出生产优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行多输出决策任务
        await agent.main()
        logging.info("多输出决策任务执行完成")
    except Exception as e:
        logging.error(f"多输出决策任务执行失败: {e}")
    finally:
        await agent.clean_up()

asyncio.run(multi_output_decision_example())
```

### 复杂多输出场景

```python
async def complex_multi_output_example():
    """复杂多输出场景示例"""
    
    agent = AsyncMultiOutputDecisionMaking(
        project_name="复杂多输出优化",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 建立连接
        await agent.async_database_connection()
        
        # 获取优化参数
        opt_params = await agent.get_optimization_parameter()
        
        # 设置为多目标优化
        opt_params.update({
            "optimization_function_type": "multi_objective",
            "n_objectives": 4,
            "optimization_algorithm": "NSGA2",
            "population_size": 150,
            "max_generations": 200
        })
        
        # 应用参数
        agent._set_optimization_interval_time(opt_params)
        agent._set_termination(opt_params)
        
        # 构建模型
        await agent.get_model_data_and_build_models()
        
        # 执行决策
        await agent.decision_no_flag()
        
        logging.info("复杂多输出优化完成")
        
    except Exception as e:
        logging.error(f"复杂多输出优化失败: {e}")
    finally:
        await agent.clean_up()

asyncio.run(complex_multi_output_example())
```

## 生产环境部署

### 决策服务管理器

```python
import asyncio
import signal
import sys
from typing import Dict, List
from datetime import datetime, timedelta
from industrytslib.core_aysnc.async_decision_agents import (
    AsyncDecisionMaking, 
    AsyncMultiOutputDecisionMaking
)

class DecisionServiceManager:
    """决策服务管理器"""
    
    def __init__(self, config_file: str):
        self.config = self._load_config(config_file)
        self.services: Dict[str, asyncio.Task] = {}
        self.running = False
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    async def start_service(self, service_name: str, service_config: Dict):
        """启动单个决策服务"""
        try:
            # 根据配置选择决策智能体类型
            if service_config.get("multi_output", False):
                agent_class = AsyncMultiOutputDecisionMaking
            else:
                agent_class = AsyncDecisionMaking
            
            # 创建智能体
            agent = agent_class(
                project_name=service_config["project_name"],
                dbconfig=service_config["dbconfig"],
                local_test_mode=service_config.get("local_test_mode", False)
            )
            
            # 定期执行决策任务
            async def decision_loop():
                interval = service_config.get("interval_seconds", 300)  # 默认5分钟
                while self.running:
                    try:
                        await agent.main()
                        self.logger.info(f"服务 {service_name} 决策完成")
                    except Exception as e:
                        self.logger.error(f"服务 {service_name} 决策失败: {e}")
                    
                    # 等待下次执行
                    await asyncio.sleep(interval)
                
                # 清理资源
                await agent.clean_up()
            
            # 启动服务任务
            task = asyncio.create_task(decision_loop())
            self.services[service_name] = task
            
            self.logger.info(f"决策服务 {service_name} 启动成功")
            
        except Exception as e:
            self.logger.error(f"启动服务 {service_name} 失败: {e}")
    
    async def start_all_services(self):
        """启动所有决策服务"""
        self.running = True
        
        # 启动所有配置的服务
        for service_name, service_config in self.config["services"].items():
            await self.start_service(service_name, service_config)
        
        self.logger.info(f"所有决策服务已启动,共 {len(self.services)} 个服务")
    
    async def stop_all_services(self):
        """停止所有决策服务"""
        self.running = False
        
        # 取消所有任务
        for service_name, task in self.services.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                self.logger.info(f"服务 {service_name} 已停止")
        
        self.services.clear()
        self.logger.info("所有决策服务已停止")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum},准备停止服务...")
            asyncio.create_task(self.stop_all_services())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

# 生产环境配置文件示例 (config.json)
production_config = {
    "services": {
        "cement_mill_a": {
            "project_name": "水泥A磨优化",
            "multi_output": True,
            "interval_seconds": 300,
            "dbconfig": {
                "web_database": {
                    "host": "prod-web-db.company.com",
                    "port": 1433,
                    "database": "IndustryDB",
                    "username": "prod_user",
                    "password": "${WEB_DB_PASSWORD}"
                },
                "ts_database": {
                    "host": "prod-ts-db.company.com",
                    "port": 1433,
                    "database": "TimeSeriesDB",
                    "username": "prod_user",
                    "password": "${TS_DB_PASSWORD}"
                }
            }
        },
        "cement_mill_b": {
            "project_name": "水泥B磨优化",
            "multi_output": False,
            "interval_seconds": 600,
            "dbconfig": {
                # ... 类似配置
            }
        }
    }
}

# 生产环境启动脚本
async def production_main():
    """生产环境主函数"""
    
    # 创建服务管理器
    manager = DecisionServiceManager("config.json")
    
    # 设置信号处理
    manager.setup_signal_handlers()
    
    try:
        # 启动所有服务
        await manager.start_all_services()
        
        # 保持运行
        while manager.running:
            await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        logging.info("接收到中断信号")
    finally:
        await manager.stop_all_services()

if __name__ == "__main__":
    asyncio.run(production_main())
```

### Docker 部署配置

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    unixodbc \
    unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装 SQL Server ODBC 驱动
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python health_check.py

# 启动命令
CMD ["python", "production_main.py"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  decision-service:
    build: .
    container_name: async-decision-service
    restart: unless-stopped
    environment:
      - WEB_DB_PASSWORD=${WEB_DB_PASSWORD}
      - TS_DB_PASSWORD=${TS_DB_PASSWORD}
      - LOG_LEVEL=INFO
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./models:/app/models:ro
    networks:
      - decision-network
    depends_on:
      - redis
      - prometheus

  redis:
    image: redis:6-alpine
    container_name: decision-redis
    restart: unless-stopped
    networks:
      - decision-network

  prometheus:
    image: prom/prometheus:latest
    container_name: decision-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - decision-network

networks:
  decision-network:
    driver: bridge
```

## 性能监控与告警

### 性能监控器

```python
import time
import psutil
import asyncio
from typing import Dict, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    execution_time: float
    memory_usage: float
    cpu_usage: float
    success: bool
    error_message: str = None

class DecisionPerformanceMonitor:
    """决策性能监控器"""
    
    def __init__(self, alert_thresholds: Dict[str, float] = None):
        self.metrics_history = []
        self.alert_thresholds = alert_thresholds or {
            "max_execution_time": 300.0,  # 5分钟
            "max_memory_usage": 80.0,     # 80%
            "max_cpu_usage": 90.0,        # 90%
            "min_success_rate": 0.95      # 95%
        }
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def monitor_decision_execution(self, decision_func, *args, **kwargs):
        """监控决策执行"""
        start_time = time.time()
        start_memory = psutil.virtual_memory().percent
        start_cpu = psutil.cpu_percent()
        
        success = False
        error_message = None
        
        try:
            # 执行决策函数
            result = await decision_func(*args, **kwargs)
            success = True
            return result
        
        except Exception as e:
            error_message = str(e)
            self.logger.error(f"决策执行失败: {e}")
            raise
        
        finally:
            # 计算性能指标
            execution_time = time.time() - start_time
            end_memory = psutil.virtual_memory().percent
            end_cpu = psutil.cpu_percent()
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                execution_time=execution_time,
                memory_usage=max(start_memory, end_memory),
                cpu_usage=max(start_cpu, end_cpu),
                success=success,
                error_message=error_message
            )
            
            # 记录指标
            self.metrics_history.append(metrics)
            
            # 检查告警
            await self._check_alerts(metrics)
            
            # 清理历史数据(保留最近1000条)
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警条件"""
        alerts = []
        
        # 执行时间告警
        if metrics.execution_time > self.alert_thresholds["max_execution_time"]:
            alerts.append(f"执行时间过长: {metrics.execution_time:.2f}s")
        
        # 内存使用告警
        if metrics.memory_usage > self.alert_thresholds["max_memory_usage"]:
            alerts.append(f"内存使用过高: {metrics.memory_usage:.1f}%")
        
        # CPU使用告警
        if metrics.cpu_usage > self.alert_thresholds["max_cpu_usage"]:
            alerts.append(f"CPU使用过高: {metrics.cpu_usage:.1f}%")
        
        # 成功率告警
        if len(self.metrics_history) >= 10:
            recent_success_rate = sum(
                1 for m in self.metrics_history[-10:] if m.success
            ) / 10
            if recent_success_rate < self.alert_thresholds["min_success_rate"]:
                alerts.append(f"成功率过低: {recent_success_rate:.1%}")
        
        # 发送告警
        if alerts:
            await self._send_alerts(alerts, metrics)
    
    async def _send_alerts(self, alerts: List[str], metrics: PerformanceMetrics):
        """发送告警"""
        alert_message = f"决策性能告警 [{metrics.timestamp}]:\n" + "\n".join(alerts)
        
        # 记录告警日志
        self.logger.warning(alert_message)
        
        # 这里可以集成邮件、短信、钉钉等告警方式
        # await self._send_email_alert(alert_message)
        # await self._send_webhook_alert(alert_message)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"message": "暂无性能数据"}
        
        recent_metrics = self.metrics_history[-100:]  # 最近100次
        
        return {
            "total_executions": len(self.metrics_history),
            "recent_executions": len(recent_metrics),
            "success_rate": sum(1 for m in recent_metrics if m.success) / len(recent_metrics),
            "avg_execution_time": sum(m.execution_time for m in recent_metrics) / len(recent_metrics),
            "max_execution_time": max(m.execution_time for m in recent_metrics),
            "avg_memory_usage": sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            "max_memory_usage": max(m.memory_usage for m in recent_metrics),
            "avg_cpu_usage": sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            "max_cpu_usage": max(m.cpu_usage for m in recent_metrics),
            "last_execution": recent_metrics[-1].timestamp.isoformat()
        }

# 使用性能监控器的示例
async def monitored_decision_example():
    """带性能监控的决策示例"""
    
    # 创建性能监控器
    monitor = DecisionPerformanceMonitor({
        "max_execution_time": 180.0,  # 3分钟
        "max_memory_usage": 75.0,     # 75%
        "max_cpu_usage": 85.0,        # 85%
        "min_success_rate": 0.90      # 90%
    })
    
    # 创建决策智能体
    agent = AsyncDecisionMaking(
        project_name="监控测试项目",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 使用监控器执行决策
        await monitor.monitor_decision_execution(agent.main)
        
        # 获取性能摘要
        summary = monitor.get_performance_summary()
        logging.info(f"性能摘要: {summary}")
        
    except Exception as e:
        logging.error(f"监控决策执行失败: {e}")
    finally:
        await agent.clean_up()

asyncio.run(monitored_decision_example())
```

### Prometheus 指标集成

```python
from prometheus_client import Counter, Histogram, Gauge, start_http_server

class PrometheusMetrics:
    """Prometheus 指标收集器"""
    
    def __init__(self):
        # 计数器
        self.decision_total = Counter(
            'decision_executions_total',
            'Total number of decision executions',
            ['project_name', 'status']
        )
        
        # 直方图
        self.decision_duration = Histogram(
            'decision_execution_duration_seconds',
            'Time spent on decision execution',
            ['project_name']
        )
        
        # 仪表盘
        self.active_decisions = Gauge(
            'active_decisions',
            'Number of currently active decisions',
            ['project_name']
        )
        
        self.memory_usage = Gauge(
            'decision_memory_usage_percent',
            'Memory usage percentage during decision execution',
            ['project_name']
        )
    
    def record_decision_start(self, project_name: str):
        """记录决策开始"""
        self.active_decisions.labels(project_name=project_name).inc()
    
    def record_decision_end(self, project_name: str, duration: float, success: bool):
        """记录决策结束"""
        status = 'success' if success else 'failure'
        self.decision_total.labels(project_name=project_name, status=status).inc()
        self.decision_duration.labels(project_name=project_name).observe(duration)
        self.active_decisions.labels(project_name=project_name).dec()
    
    def update_memory_usage(self, project_name: str, usage: float):
        """更新内存使用率"""
        self.memory_usage.labels(project_name=project_name).set(usage)

# 集成 Prometheus 的决策监控器
class PrometheusDecisionMonitor(DecisionPerformanceMonitor):
    """集成 Prometheus 的决策监控器"""
    
    def __init__(self, metrics_port: int = 8000, **kwargs):
        super().__init__(**kwargs)
        self.prometheus_metrics = PrometheusMetrics()
        
        # 启动 Prometheus HTTP 服务器
        start_http_server(metrics_port)
        self.logger.info(f"Prometheus 指标服务器启动在端口 {metrics_port}")
    
    async def monitor_decision_execution(self, decision_func, project_name: str, *args, **kwargs):
        """监控决策执行并记录 Prometheus 指标"""
        
        # 记录开始
        self.prometheus_metrics.record_decision_start(project_name)
        
        start_time = time.time()
        success = False
        
        try:
            # 执行决策
            result = await super().monitor_decision_execution(decision_func, *args, **kwargs)
            success = True
            return result
        
        finally:
            # 记录结束
            duration = time.time() - start_time
            self.prometheus_metrics.record_decision_end(project_name, duration, success)
            
            # 更新内存使用率
            memory_usage = psutil.virtual_memory().percent
            self.prometheus_metrics.update_memory_usage(project_name, memory_usage)
```

## 故障处理与恢复

### 故障处理器

```python
import asyncio
from enum import Enum
from typing import Optional, Callable, Any
from dataclasses import dataclass

class FailureType(Enum):
    """故障类型枚举"""
    DATABASE_CONNECTION = "database_connection"
    MODEL_LOADING = "model_loading"
    OPTIMIZATION_ERROR = "optimization_error"
    MEMORY_ERROR = "memory_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class FailureInfo:
    """故障信息"""
    failure_type: FailureType
    error_message: str
    timestamp: datetime
    retry_count: int = 0
    recoverable: bool = True

class DecisionFailureHandler:
    """决策故障处理器"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 5.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.failure_history = []
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def classify_failure(self, exception: Exception) -> FailureType:
        """分类故障类型"""
        error_message = str(exception).lower()
        
        if "connection" in error_message or "database" in error_message:
            return FailureType.DATABASE_CONNECTION
        elif "model" in error_message or "load" in error_message:
            return FailureType.MODEL_LOADING
        elif "optimization" in error_message or "solve" in error_message:
            return FailureType.OPTIMIZATION_ERROR
        elif "memory" in error_message or "out of memory" in error_message:
            return FailureType.MEMORY_ERROR
        elif "timeout" in error_message or "time" in error_message:
            return FailureType.TIMEOUT_ERROR
        else:
            return FailureType.UNKNOWN_ERROR
    
    async def handle_failure(
        self, 
        exception: Exception, 
        recovery_func: Optional[Callable] = None
    ) -> bool:
        """处理故障"""
        
        failure_type = self.classify_failure(exception)
        failure_info = FailureInfo(
            failure_type=failure_type,
            error_message=str(exception),
            timestamp=datetime.now()
        )
        
        self.failure_history.append(failure_info)
        self.logger.error(f"检测到故障: {failure_type.value} - {exception}")
        
        # 根据故障类型选择处理策略
        if failure_type == FailureType.DATABASE_CONNECTION:
            return await self._handle_database_failure(failure_info, recovery_func)
        elif failure_type == FailureType.MODEL_LOADING:
            return await self._handle_model_failure(failure_info, recovery_func)
        elif failure_type == FailureType.OPTIMIZATION_ERROR:
            return await self._handle_optimization_failure(failure_info, recovery_func)
        elif failure_type == FailureType.MEMORY_ERROR:
            return await self._handle_memory_failure(failure_info, recovery_func)
        elif failure_type == FailureType.TIMEOUT_ERROR:
            return await self._handle_timeout_failure(failure_info, recovery_func)
        else:
            return await self._handle_unknown_failure(failure_info, recovery_func)
    
    async def _handle_database_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理数据库连接故障"""
        
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"尝试恢复数据库连接 (第 {attempt + 1} 次)")
                
                # 等待重试延迟
                await asyncio.sleep(self.retry_delay * (attempt + 1))
                
                # 执行恢复函数
                if recovery_func:
                    await recovery_func()
                
                self.logger.info("数据库连接恢复成功")
                return True
                
            except Exception as e:
                self.logger.warning(f"数据库连接恢复失败 (第 {attempt + 1} 次): {e}")
        
        self.logger.error("数据库连接恢复失败,已达到最大重试次数")
        return False
    
    async def _handle_model_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理模型加载故障"""
        
        try:
            self.logger.info("尝试重新加载模型")
            
            # 清理内存
            import gc
            gc.collect()
            
            # 等待一段时间
            await asyncio.sleep(self.retry_delay)
            
            # 执行恢复函数
            if recovery_func:
                await recovery_func()
            
            self.logger.info("模型重新加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"模型重新加载失败: {e}")
            return False
    
    async def _handle_optimization_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理优化故障"""
        
        try:
            self.logger.info("尝试使用降级优化参数")
            
            # 使用更保守的优化参数
            # 这里可以实现参数降级逻辑
            
            if recovery_func:
                await recovery_func()
            
            self.logger.info("优化故障恢复成功")
            return True
            
        except Exception as e:
            self.logger.error(f"优化故障恢复失败: {e}")
            return False
    
    async def _handle_memory_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理内存故障"""
        
        try:
            self.logger.info("尝试清理内存")
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 如果使用 PyTorch,清理 CUDA 缓存
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            # 等待内存释放
            await asyncio.sleep(self.retry_delay)
            
            if recovery_func:
                await recovery_func()
            
            self.logger.info("内存故障恢复成功")
            return True
            
        except Exception as e:
            self.logger.error(f"内存故障恢复失败: {e}")
            return False
    
    async def _handle_timeout_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理超时故障"""
        
        try:
            self.logger.info("尝试使用更长的超时时间")
            
            # 增加超时时间并重试
            if recovery_func:
                await recovery_func()
            
            self.logger.info("超时故障恢复成功")
            return True
            
        except Exception as e:
            self.logger.error(f"超时故障恢复失败: {e}")
            return False
    
    async def _handle_unknown_failure(
        self, 
        failure_info: FailureInfo, 
        recovery_func: Optional[Callable]
    ) -> bool:
        """处理未知故障"""
        
        self.logger.warning("检测到未知故障类型,尝试通用恢复策略")
        
        try:
            # 等待一段时间
            await asyncio.sleep(self.retry_delay)
            
            if recovery_func:
                await recovery_func()
            
            self.logger.info("未知故障恢复成功")
            return True
            
        except Exception as e:
            self.logger.error(f"未知故障恢复失败: {e}")
            return False
    
    def get_failure_statistics(self) -> Dict[str, Any]:
        """获取故障统计信息"""
        if not self.failure_history:
            return {"message": "暂无故障记录"}
        
        failure_counts = {}
        for failure in self.failure_history:
            failure_type = failure.failure_type.value
            failure_counts[failure_type] = failure_counts.get(failure_type, 0) + 1
        
        return {
            "total_failures": len(self.failure_history),
            "failure_types": failure_counts,
            "last_failure": self.failure_history[-1].timestamp.isoformat(),
            "recent_failures": len([f for f in self.failure_history 
                                   if (datetime.now() - f.timestamp).total_seconds() < 3600])
        }

# 带故障恢复的决策智能体包装器
class ResilientDecisionAgent:
    """具有故障恢复能力的决策智能体包装器"""
    
    def __init__(self, agent_class, *args, **kwargs):
        self.agent_class = agent_class
        self.agent_args = args
        self.agent_kwargs = kwargs
        self.agent = None
        self.failure_handler = DecisionFailureHandler()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def _create_agent(self):
        """创建智能体实例"""
        if self.agent:
            await self.agent.clean_up()
        
        self.agent = self.agent_class(*self.agent_args, **self.agent_kwargs)
    
    async def execute_decision(self) -> bool:
        """执行决策任务"""
        
        # 检查连续失败次数
        if self.consecutive_failures >= self.max_consecutive_failures:
            self.logger.error(f"连续失败次数达到上限 ({self.max_consecutive_failures}),暂停服务")
            return False
        
        try:
            # 确保智能体已创建
            if not self.agent:
                await self._create_agent()
            
            # 执行决策
            await self.agent.main()
            
            # 重置连续失败计数
            self.consecutive_failures = 0
            self.logger.info("决策执行成功")
            return True
            
        except Exception as e:
            self.consecutive_failures += 1
            self.logger.error(f"决策执行失败 (连续第 {self.consecutive_failures} 次): {e}")
            
            # 尝试故障恢复
            recovery_success = await self.failure_handler.handle_failure(
                e, self._create_agent
            )
            
            if recovery_success:
                # 恢复成功,重试执行
                try:
                    await self.agent.main()
                    self.consecutive_failures = 0
                    self.logger.info("故障恢复后决策执行成功")
                    return True
                except Exception as retry_e:
                    self.logger.error(f"故障恢复后重试失败: {retry_e}")
            
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.agent:
            await self.agent.clean_up()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "consecutive_failures": self.consecutive_failures,
            "max_consecutive_failures": self.max_consecutive_failures,
            "healthy": self.consecutive_failures < self.max_consecutive_failures,
            "failure_statistics": self.failure_handler.get_failure_statistics()
        }

# 使用故障恢复的示例
async def resilient_decision_example():
    """具有故障恢复能力的决策示例"""
    
    # 创建具有故障恢复能力的决策智能体
    resilient_agent = ResilientDecisionAgent(
        AsyncDecisionMaking,
        project_name="故障恢复测试",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 执行多次决策,测试故障恢复
        for i in range(10):
            success = await resilient_agent.execute_decision()
            
            if success:
                logging.info(f"第 {i+1} 次决策执行成功")
            else:
                logging.warning(f"第 {i+1} 次决策执行失败")
            
            # 获取健康状态
            health = resilient_agent.get_health_status()
            logging.info(f"健康状态: {health}")
            
            # 如果不健康,停止执行
            if not health["healthy"]:
                logging.error("智能体不健康,停止执行")
                break
            
            # 等待下次执行
            await asyncio.sleep(60)
    
    finally:
        await resilient_agent.cleanup()

asyncio.run(resilient_decision_example())
```

## 高级配置示例

### 动态配置管理

```python
import json
import aiofiles
from typing import Dict, Any
from pathlib import Path

class DynamicConfigManager:
    """动态配置管理器"""
    
    def __init__(self, config_file: str, reload_interval: int = 300):
        self.config_file = Path(config_file)
        self.reload_interval = reload_interval
        self.config = {}
        self.last_modified = 0
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            async with aiofiles.open(self.config_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                self.config = json.loads(content)
                self.last_modified = self.config_file.stat().st_mtime
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                return self.config
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return {}
    
    async def check_and_reload(self) -> bool:
        """检查并重新加载配置"""
        try:
            current_modified = self.config_file.stat().st_mtime
            if current_modified > self.last_modified:
                self.logger.info("检测到配置文件变更,重新加载")
                await self.load_config()
                return True
            return False
        except Exception as e:
            self.logger.error(f"检查配置文件失败: {e}")
            return False
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """获取配置值"""
        if key is None:
            return self.config
        return self.config.get(key, default)
    
    async def start_auto_reload(self):
        """启动自动重载"""
        while True:
            await asyncio.sleep(self.reload_interval)
            await self.check_and_reload()

# 使用动态配置的决策智能体
class ConfigurableDecisionAgent:
    """可配置的决策智能体"""
    
    def __init__(self, config_manager: DynamicConfigManager):
        self.config_manager = config_manager
        self.agent = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def create_agent_from_config(self):
        """根据配置创建智能体"""
        config = self.config_manager.get_config()
        
        # 获取智能体配置
        agent_config = config.get("agent", {})
        project_name = agent_config.get("project_name", "默认项目")
        multi_output = agent_config.get("multi_output", False)
        local_test_mode = agent_config.get("local_test_mode", False)
        
        # 获取数据库配置
        dbconfig = config.get("database", {})
        
        # 选择智能体类型
        if multi_output:
            agent_class = AsyncMultiOutputDecisionMaking
        else:
            agent_class = AsyncDecisionMaking
        
        # 创建智能体
        if self.agent:
            await self.agent.clean_up()
        
        self.agent = agent_class(
            project_name=project_name,
            dbconfig=dbconfig,
            local_test_mode=local_test_mode
        )
        
        self.logger.info(f"智能体创建成功: {project_name} ({'多输出' if multi_output else '单输出'})")
    
    async def execute_with_config(self):
        """根据配置执行决策"""
        # 检查配置更新
        config_updated = await self.config_manager.check_and_reload()
        
        # 如果配置更新或智能体未创建,重新创建智能体
        if config_updated or not self.agent:
            await self.create_agent_from_config()
        
        # 执行决策
        await self.agent.main()

# 配置文件示例 (dynamic_config.json)
dynamic_config_example = {
    "agent": {
        "project_name": "动态配置测试",
        "multi_output": True,
        "local_test_mode": False,
        "execution_interval": 300
    },
    "database": {
        "web_database": {
            "host": "localhost",
            "port": 1433,
            "database": "IndustryDB",
            "username": "admin",
            "password": "password123"
        },
        "ts_database": {
            "host": "localhost",
            "port": 1433,
            "database": "TimeSeriesDB",
            "username": "admin",
            "password": "password123"
        }
    },
    "optimization": {
        "algorithm": "NSGA2",
        "population_size": 100,
        "max_generations": 50,
        "function_type": "multi_objective"
    },
    "monitoring": {
        "enable_prometheus": True,
        "metrics_port": 8000,
        "alert_thresholds": {
            "max_execution_time": 300,
            "max_memory_usage": 80,
            "min_success_rate": 0.95
        }
    }
}

# 动态配置示例
async def dynamic_config_example():
    """动态配置示例"""
    
    # 创建配置管理器
    config_manager = DynamicConfigManager(
        config_file="dynamic_config.json",
        reload_interval=60  # 每分钟检查一次配置更新
    )
    
    # 加载初始配置
    await config_manager.load_config()
    
    # 创建可配置的决策智能体
    configurable_agent = ConfigurableDecisionAgent(config_manager)
    
    # 启动自动重载任务
    reload_task = asyncio.create_task(config_manager.start_auto_reload())
    
    try:
        # 执行决策循环
        while True:
            try:
                await configurable_agent.execute_with_config()
                
                # 获取执行间隔
                interval = config_manager.get_config("agent.execution_interval", 300)
                await asyncio.sleep(interval)
                
            except Exception as e:
                logging.error(f"配置化决策执行失败: {e}")
                await asyncio.sleep(60)  # 失败后等待1分钟
    
    except KeyboardInterrupt:
        logging.info("接收到中断信号")
    finally:
        # 清理资源
        reload_task.cancel()
        if configurable_agent.agent:
            await configurable_agent.agent.clean_up()

asyncio.run(dynamic_config_example())
```

## 总结

异步决策智能体模块提供了强大而灵活的工业优化决策解决方案。通过本文档的示例,您可以:

### 🎯 核心功能
- **基础决策**:使用 `AsyncDecisionMaking` 进行单输出模型优化
- **多输出决策**:使用 `AsyncMultiOutputDecisionMaking` 处理复杂模型
- **生产部署**:通过服务管理器实现生产环境部署
- **性能监控**:集成 Prometheus 等监控系统
- **故障恢复**:实现自动故障检测和恢复机制

### 🚀 最佳实践
- **配置管理**:使用环境变量和动态配置
- **错误处理**:实现完善的异常处理和日志记录
- **资源管理**:及时清理数据库连接和内存资源
- **性能优化**:监控执行时间和资源使用情况
- **安全性**:保护敏感配置信息和数据传输

### 📊 监控告警
- **性能指标**:执行时间、内存使用、CPU使用率
- **成功率监控**:决策执行成功率统计
- **故障分类**:自动分类和处理不同类型的故障
- **告警机制**:集成邮件、短信、钉钉等告警方式

### 🔧 扩展能力
- **插件化架构**:支持自定义优化算法和目标函数
- **多项目支持**:同时管理多个优化项目
- **动态配置**:运行时更新配置无需重启服务
- **容器化部署**:支持 Docker 和 Kubernetes 部署

通过合理使用这些示例和最佳实践,您可以构建稳定、高效、可维护的工业优化决策系统。

## 相关文档

- [异步决策智能体概述](./async_decision_overview.md) - 模块整体介绍
- [基础异步决策智能体](./async_basic_decision_agent.md) - AsyncDecisionMaking 详细文档
- [多输出异步决策智能体](./async_multi_output_decision_agent.md) - AsyncMultiOutputDecisionMaking 详细文档
- [API参考](./api_reference.md) - 完整的API参考文档

---

> **提示**:在生产环境中使用前,建议先在测试环境中验证所有配置和功能。确保数据库连接稳定,模型文件完整,监控告警正常工作。