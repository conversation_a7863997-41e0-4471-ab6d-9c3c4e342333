# 异步任务 (Async Tasks)

异步任务模块是 `industrytslib` 库中专门为解决阻塞线程问题而设计的高性能异步处理组件。该模块基于 Python 的 `asyncio` 框架,提供完整的异步模型训练、预测和决策功能。

## 核心优势

- **非阻塞执行**: 避免长时间训练任务阻塞主线程
- **并发处理**: 支持多个训练任务同时执行
- **资源优化**: 更高效的 I/O 操作和内存管理
- **可扩展性**: 易于集成到异步应用架构中

## 主要组件

### [异步模型训练器](trainers/async_trainer_overview.md)

异步训练器模块提供了完整的异步模型训练功能,包括:

- **[异步基础训练器](trainers/async_basic_trainer.md)** (`AsyncModelTrainer`)
  - 异步数据库连接管理
  - 异步数据获取和处理
  - 设备管理和资源清理
  - 训练结果可视化

- **[异步时序经典训练器](trainers/async_time_series_classic_trainer.md)** (`AsyncTimeSeriesClassicTrainer`)
  - 支持 LSTM、GRU 等经典时序模型
  - 并发数据增强和预处理
  - 异步模型评估和指标计算
  - 早停策略和模型保存

- **[异步时序序列训练器](trainers/async_time_series_sequence_trainer.md)** (`AsyncTimeSeriesSequenceTrainer`)
  - 支持 Transformer 架构及其变体
  - 异步序列数据处理
  - 多变量时序预测
  - 注意力机制优化

### [异步训练管道](trainers/async_training_pipeline.md)

异步训练管道提供了便捷的训练器创建和管理功能:

- **AsyncTrainerBuilder**: 建造者模式的训练器配置
- **create_async_trainer**: 类型安全的训练器创建函数
- **链式配置**: 支持流畅的参数配置接口

### [异步预测器](predictors/index.md)

异步预测器模块提供高性能的异步模型预测功能:

- **异步基础预测器**: 非阻塞预测执行和结果处理
- **异步时序预测器**: 时序数据流式预测和多步预测
- **异步多输出预测器**: 多目标预测和并行输出处理
- **预测管道**: 类型安全的预测器创建和管理

### [异步决策器](decisions/index.md)

异步决策器模块提供基于优化算法的异步决策制定功能:

- **异步基础决策器**: 非阻塞优化计算和决策执行
- **异步多输出决策器**: 多目标优化和帕累托前沿分析
- **决策管道**: 智能调度和决策流程管理
- **优化算法**: 遗传算法、粒子群优化、差分进化等

## 使用场景

### 1. Web 应用集成
```python
# 在 FastAPI 应用中使用异步训练器
@app.post("/train")
async def start_training(request: TrainingRequest):
    trainer = await create_async_trainer(
        project_name=request.project_name,
        model_type="async_time_series_classic",
        dbconfig=request.db_config
    )
    # 异步训练不会阻塞 API 响应
    asyncio.create_task(trainer.main())
    return {"status": "training_started"}
```

### 2. 批量训练任务
```python
# 并发执行多个训练任务
async def batch_training(projects):
    tasks = []
    for project in projects:
        trainer = await create_async_trainer(
            project_name=project.name,
            model_type=project.model_type,
            dbconfig=project.db_config
        )
        tasks.append(trainer.main())
    
    # 并发执行所有训练任务
    await asyncio.gather(*tasks)
```

### 3. 实时监控系统
```python
# 在后台持续运行训练任务
async def continuous_training():
    while True:
        trainer = await create_async_trainer(
            project_name="realtime_model",
            model_type="async_time_series_sequence",
            local_test_mode=False
        )
        await trainer.main()
        await asyncio.sleep(3600)  # 每小时重新训练
```

## 架构设计

异步任务模块采用分层架构设计:

```
异步任务层 (Async Tasks Layer)
├── 异步训练管道 (Async Training Pipeline)
│   ├── AsyncTrainerBuilder
│   └── create_async_trainer
├── 异步训练器 (Async Trainers)
│   ├── AsyncModelTrainer (基础)
│   ├── AsyncTimeSeriesClassicTrainer
│   └── AsyncTimeSeriesSequenceTrainer
└── 异步基础设施 (Async Infrastructure)
    ├── AsyncScheduledTask
    ├── 异步数据库连接
    └── 异步数据处理
```

## 性能特点

- **内存效率**: 异步 I/O 减少内存占用
- **CPU 利用率**: 更好的 CPU 资源分配
- **响应性**: 不阻塞主线程,保持应用响应
- **可扩展性**: 支持大规模并发训练任务

## 快速开始

查看 [使用示例](usage_examples.md) 了解如何快速开始使用异步训练功能。

## 注意事项

1. **Python 版本**: 需要 Python 3.7+ 支持 asyncio
2. **事件循环**: 确保在正确的事件循环中运行
3. **资源管理**: 注意异步任务的生命周期管理
4. **错误处理**: 实现适当的异常处理机制

## 相关文档

- [同步训练器文档](../tasks/trainer_tasks/index.md)
- [数据库异步连接](../utils/database_async/index.md)
- [配置管理](../configuration/index.md)