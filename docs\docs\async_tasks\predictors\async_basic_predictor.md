# AsyncBaseRealtimePredictor - 异步基础预测器

`AsyncBaseRealtimePredictor` 是所有异步实时预测器的抽象基类,提供了统一的异步预测功能和接口规范。

## 📋 类概述

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncBaseRealtimePredictor
from abc import ABC, abstractmethod

class AsyncBaseRealtimePredictor(AsyncScheduledTask, ABC):
    """异步实时预测抽象基类
    
    统一所有异步实时预测类的共同功能:
    - 异步数据库连接管理
    - 项目和模型信息管理  
    - 变量名列表管理
    - 模型加载和归一化参数管理
    - 预测计数和热重载机制
    - 模型文件修改时间监测
    - 异步日志记录
    - 异步资源清理
    """
```

## 🔧 初始化参数

### 构造函数

```python
def __init__(
    self,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]],
    local_test_mode: bool = False,
    task_type: str = "async_realtime_predict",
) -> None:
```

**参数说明**:

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_name` | `str` | ✅ | - | 实时预测项目名称 |
| `dbconfig` | `Optional[Dict[str, Any]]` | ✅ | - | 数据库配置字典 |
| `local_test_mode` | `bool` | ❌ | `False` | 本地测试模式标志 |
| `task_type` | `str` | ❌ | `"async_realtime_predict"` | 任务类型标识 |

**数据库配置示例**:

```python
dbconfig = {
    "web": {
        "server": "localhost",
        "database": "web_db",
        "username": "user",
        "password": "password"
    },
    "timeseries": {
        "server": "localhost",
        "database": "ts_db",
        "username": "user",
        "password": "password"
    },
    "realtimepredict": {
        "server": "localhost",
        "database": "predict_db",
        "username": "user",
        "password": "password"
    },
    "quality": {  # 可选,用于质量数据库
        "server": "localhost",
        "database": "quality_db",
        "username": "user",
        "password": "password"
    }
}
```

## 🏗️ 核心属性

### 基础属性

```python
# 项目信息
self.project_name: str                    # 项目名称
self.sample_name: str                     # 样本名称
self.model_name: str                      # 模型名称
self.sample_table_in_name: str            # 输入样本表名
self.sample_table_out_name: str           # 输出样本表名

# 变量名列表
self.input_name_list: List[str]           # 输入变量名列表
self.output_name_list: List[str]          # 输出变量名列表

# 设备相关
self.feed_amount_column_name: Optional[Union[str, List[str]]]  # 喂料量列名
self.device_operation_flag: Optional[str]                     # 设备运行标志位
```

### 数据库客户端

```python
# 异步数据库客户端
self.web_db_client: Optional[AsyncMSSQLWeb]                    # Web数据库客户端
self.ts_db_client: Optional[AsyncMSSQLTimeSeries]              # 时序数据库客户端
self.realtime_db_client: Optional[AsyncMSSQLRealtimePredict]   # 实时预测数据库客户端
self.output_db_client: Optional[Union[AsyncMSSQLTimeSeries, AsyncMSSQLQuality]]  # 输出数据库客户端
```

### 模型管理

```python
# 预测计数机制(用于模型热重载)
self.predict_count: int = 0                    # 当前预测计数
self.predict_count_threshold: int = 10         # 预测计数阈值

# 模型文件监测
self.model_file_last_modified: Optional[float]    # 模型文件最后修改时间
self.scaler_x_last_modified: Optional[float]      # X归一化文件最后修改时间
self.scaler_y_last_modified: Optional[float]      # Y归一化文件最后修改时间
```

## 🔄 核心方法

### 异步初始化方法

#### `async def initialize(self) -> None`
异步初始化方法,在需要异步操作的初始化逻辑时调用。

```python
async def initialize(self) -> None:
    """异步初始化方法"""
    await super().initialize()
    
    if hasattr(self, '_need_async_init') and self._need_async_init:
        await self._init_database_connections()
        await self._get_sample_model_name()
        delattr(self, '_need_async_init')
```

#### `async def _init_database_connections(self) -> None`
异步初始化数据库连接。

```python
# 使用示例
predictor = CustomAsyncPredictor(project_name, dbconfig)
await predictor.initialize()  # 触发异步初始化
```

### 模型管理方法

#### `async def get_model_parameter(self, model_name: str) -> Tuple[str, Dict[str, Any]]`
异步获取模型参数。

```python
# 返回值
algorithm_name, model_parameter = await predictor.get_model_parameter("my_model")

# algorithm_name: 算法名称,如 "LSTM", "Transformer"
# model_parameter: 模型参数字典
```

#### `async def load_model_and_scaler(self, project_name: str, algorithm_name: str, model_parameter: Dict[str, Any]) -> Tuple[torch.nn.Module, Any, Any]`
异步加载模型和归一化参数。

```python
# 加载模型和归一化器
model, scaler_x, scaler_y = await predictor.load_model_and_scaler(
    project_name="my_project",
    algorithm_name="LSTM",
    model_parameter=model_params
)

# model: PyTorch模型实例
# scaler_x: 输入数据归一化器
# scaler_y: 输出数据归一化器
```

#### `async def should_reload_model(self, project_name: Optional[str] = None) -> bool`
异步检查是否应该重载模型(优化版本)。

**特性**:
- 基于计数触发的文件检测策略
- 每次预测累计计数
- 达到预测次数阈值时才检查文件修改时间
- 只有文件确实修改时才触发重载
- 大幅减少文件系统IO操作,提高性能

```python
# 使用示例
if await predictor.should_reload_model():
    # 重新加载模型
    model, scaler_x, scaler_y = await predictor.load_model_and_scaler(
        project_name, algorithm_name, model_parameter
    )
    print("模型已重载")
```

#### `async def check_model_files_modified(self, project_name: str) -> bool`
异步检查模型文件是否被修改(严格三文件同步检查)。

**特性**:
- 只有当所有三个文件(checkpoint.pth, scaler_x.pkl, scaler_y.pkl)都被修改时才返回True
- 确保模型组件的完整性,避免加载不匹配的模型和归一化参数组合

```python
# 检查文件修改状态
files_modified = await predictor.check_model_files_modified("my_project")
if files_modified:
    print("所有模型文件都已更新")
```

### 数据获取方法

#### `async def get_sample_table_information(self) -> None`
异步获取样本表信息。

```python
# 获取样本表信息
await predictor.get_sample_table_information()

# 信息存储在 self.sample_table_information 中
print(predictor.sample_table_information)
```

#### `async def get_var_name_list(self) -> None`
异步获取输入和输出变量名列表。

```python
# 获取变量名列表
await predictor.get_var_name_list()

# 变量名存储在对应属性中
print(f"输入变量: {predictor.input_name_list}")
print(f"输出变量: {predictor.output_name_list}")
```

#### `async def get_device_info(self) -> None`
异步获取设备相关信息。

```python
# 获取设备信息
await predictor.get_device_info()

# 设备信息存储在对应属性中
print(f"喂料量列名: {predictor.feed_amount_column_name}")
print(f"设备运行标志位: {predictor.device_operation_flag}")
```

### 数据库管理方法

#### `async def get_output_db_client(self, model_parameter: Dict[str, Any]) -> Tuple[str, Union[AsyncMSSQLTimeSeries, AsyncMSSQLQuality]]`
异步根据模型参数创建输出数据库连接。

```python
# 获取输出数据库客户端
output_flag, output_db_client = await predictor.get_output_db_client(model_parameter)

# output_flag: "control" 或 "quality"
# output_db_client: 对应的数据库客户端
```

#### `async def db_reconnect(self) -> None`
异步数据库重连机制。

```python
# 数据库重连
try:
    # 执行数据库操作
    result = await db_client.query(sql)
except pyodbc.Error:
    await predictor.db_reconnect()
    # 重试操作
```

### 资源管理方法

#### `async def cleanup(self) -> None`
异步清理资源。

```python
# 资源清理
try:
    # 执行预测任务
    await predictor.main()
finally:
    # 确保资源被正确清理
    await predictor.cleanup()
```

## 🎯 抽象方法

子类必须实现以下抽象方法:

### `async def get_basic_info(self) -> None`
异步获取基本信息的抽象方法。

```python
@abstractmethod
async def get_basic_info(self) -> None:
    """子类必须实现此方法来异步获取具体的预测配置信息"""
    pass
```

### `async def main(self) -> None`
异步主执行方法的抽象方法。

```python
@abstractmethod
async def main(self) -> None:
    """子类必须实现此方法来定义具体的异步预测流程"""
    pass
```

## 💡 实现示例

### 自定义异步预测器

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncBaseRealtimePredictor
import asyncio
import datetime

class CustomAsyncPredictor(AsyncBaseRealtimePredictor):
    """自定义异步预测器示例"""
    
    def __init__(self, project_name: str, dbconfig: dict):
        super().__init__(
            project_name=project_name,
            dbconfig=dbconfig,
            local_test_mode=False,
            task_type="custom_async_predict"
        )
        
        # 自定义属性
        self.custom_model = None
        self.custom_scaler = None
    
    async def get_basic_info(self) -> None:
        """获取基本信息"""
        self.logger.info(f"正在获取 {self.project_name} 的基本信息...")
        
        # 确保数据库连接已初始化
        if hasattr(self, '_need_async_init') and self._need_async_init:
            await self._init_database_connections()
            await self._get_sample_model_name()
            self._need_async_init = False
        
        # 异步获取模型参数
        self.algorithm_name, self.model_parameter = await self.get_model_parameter(self.model_name)
        
        # 异步加载模型和归一化参数
        self.model, self.scaler_x, self.scaler_y = await self.load_model_and_scaler(
            self.project_name, 
            self.algorithm_name, 
            self.model_parameter
        )
        
        # 获取其他基本信息
        await self.get_sample_table_information()
        await self.get_var_name_list()
        await self.get_device_info()
        
        self.logger.info("基本信息获取完毕!")
    
    async def main(self) -> None:
        """主预测流程"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        # 确保基本信息已初始化
        if not hasattr(self, 'model') or self.model is None:
            await self.get_basic_info()
        
        # 检查是否需要重载模型
        if await self.should_reload_model():
            self.logger.warning("检测到模型需要重载,重新加载模型...")
            try:
                self.model, self.scaler_x, self.scaler_y = await self.load_model_and_scaler(
                    self.project_name,
                    self.algorithm_name,
                    self.model_parameter
                )
                self.logger.warning("模型重载成功")
            except Exception as e:
                self.logger.error(f"模型重载失败: {e}")
                return
        
        # 执行自定义预测逻辑
        try:
            # 获取输入数据
            input_data = await self._get_input_data()
            
            # 执行预测
            prediction = await self._predict(input_data)
            
            # 保存预测结果
            await self._save_prediction(prediction, current_time)
            
            self.logger.info(f"预测完成: {current_time}")
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
    
    async def _get_input_data(self):
        """获取输入数据(自定义实现)"""
        # 实现数据获取逻辑
        pass
    
    async def _predict(self, input_data):
        """执行预测(自定义实现)"""
        # 实现预测逻辑
        pass
    
    async def _save_prediction(self, prediction, timestamp):
        """保存预测结果(自定义实现)"""
        # 实现结果保存逻辑
        pass

# 使用示例
async def main():
    # 创建预测器实例
    predictor = CustomAsyncPredictor(
        project_name="my_custom_project",
        dbconfig=database_config
    )
    
    try:
        # 初始化
        await predictor.initialize()
        
        # 执行预测
        await predictor.main()
        
    finally:
        # 清理资源
        await predictor.cleanup()

# 运行
if __name__ == "__main__":
    asyncio.run(main())
```

## 🔍 最佳实践

### 1. 异步初始化

```python
# ✅ 正确的异步初始化
predictor = AsyncPredictor(project_name, dbconfig)
await predictor.initialize()  # 显式调用异步初始化

# ❌ 错误:在 __init__ 中直接调用异步方法
class BadPredictor(AsyncBaseRealtimePredictor):
    def __init__(self, ...):
        super().__init__(...)
        asyncio.run(self.some_async_method())  # 不要这样做
```

### 2. 资源管理

```python
# ✅ 使用 try-finally 确保资源清理
try:
    await predictor.main()
finally:
    await predictor.cleanup()

# ✅ 或使用异步上下文管理器
class AsyncPredictor(AsyncBaseRealtimePredictor):
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

# 使用
async with AsyncPredictor(project_name, dbconfig) as predictor:
    await predictor.main()
```

### 3. 错误处理

```python
# ✅ 完善的错误处理
try:
    result = await predictor.some_operation()
except pyodbc.Error as e:
    logger.error(f"数据库错误: {e}")
    await predictor.db_reconnect()
    # 重试逻辑
except Exception as e:
    logger.error(f"未知错误: {e}")
    # 其他处理逻辑
```

### 4. 类型安全

```python
# ✅ 使用类型注解
from typing import Optional, Dict, Any, List

class TypeSafePredictor(AsyncBaseRealtimePredictor):
    def __init__(
        self,
        project_name: str,
        dbconfig: Optional[Dict[str, Any]],
        custom_param: Optional[str] = None
    ) -> None:
        super().__init__(project_name, dbconfig)
        self.custom_param: Optional[str] = custom_param
    
    async def process_data(self, data: List[float]) -> List[float]:
        # 类型安全的方法实现
        return processed_data
```

## 🚨 注意事项

1. **数据库配置验证**: 确保 `dbconfig` 包含所有必需的数据库连接信息
2. **异步初始化**: 必须调用 `initialize()` 方法完成异步初始化
3. **资源清理**: 使用完毕后必须调用 `cleanup()` 方法清理资源
4. **错误处理**: 实现完善的异常处理机制,特别是数据库连接错误
5. **模型文件**: 确保模型文件路径正确且文件完整
6. **类型安全**: 充分利用类型注解提高代码质量

## 🔗 相关文档

- [异步经典预测器使用指南](./async_classic_predictor.md)
- [异步时间序列预测器指南](./async_time_series_predictor.md)
- [异步预测器使用示例](./usage_examples.md)