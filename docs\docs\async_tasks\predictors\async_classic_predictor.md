# AsyncClassicRealtimePredictor - 异步经典实时预测器

`AsyncClassicRealtimePredictor` 是基于经典机器学习算法的异步实时预测器,支持多输出变量的软测量预测,具备智能模型热重载、类型安全变量管理、设备状态检查和数据预处理等功能。

## 📋 类概述

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

class AsyncClassicRealtimePredictor(AsyncBaseRealtimePredictor):
    """异步经典实时预测器
    
    支持多输出变量的软测量异步实时预测,具备以下特性:
    - 智能模型热重载机制
    - 类型安全的变量管理
    - 设备状态检查
    - 数据预处理(分钟均值、平滑滤波)
    - 异步数据库操作
    - 完整的错误处理
    """
```

## 🔧 初始化参数

### 构造函数

```python
def __init__(
    self,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]],
    local_test_mode: bool = False,
    task_type: str = "async_realtime_predict",
) -> None:
```

**参数说明**:

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_name` | `str` | ✅ | - | 实时预测项目名称 |
| `dbconfig` | `Optional[Dict[str, Any]]` | ✅ | - | 数据库配置字典 |
| `local_test_mode` | `bool` | ❌ | `False` | 本地测试模式标志 |
| `task_type` | `str` | ❌ | `"async_realtime_predict"` | 任务类型标识 |

**使用示例**:

```python
# 创建异步经典预测器实例
predictor = AsyncClassicRealtimePredictor(
    project_name="steel_quality_prediction",
    dbconfig={
        "web": {"server": "localhost", "database": "web_db", "username": "user", "password": "pass"},
        "timeseries": {"server": "localhost", "database": "ts_db", "username": "user", "password": "pass"},
        "realtimepredict": {"server": "localhost", "database": "predict_db", "username": "user", "password": "pass"}
    },
    local_test_mode=False
)
```

## 🏗️ 核心属性

### 预测配置属性

```python
# 预测时间配置
self.pred_length: int                     # 预测长度(分钟)
self.pred_time: datetime.datetime         # 当前预测时间

# 模型相关
self.algorithm_name: str                  # 算法名称(如 "RandomForest", "XGBoost")
self.model_parameter: Dict[str, Any]      # 模型参数字典
self.model: torch.nn.Module               # 加载的模型实例
self.scaler_x: Any                        # 输入数据归一化器
self.scaler_y: Any                        # 输出数据归一化器

# 输出数据库配置
self.output_flag: str                     # 输出标志("control" 或 "quality")
self.output_db_client: Union[AsyncMSSQLTimeSeries, AsyncMSSQLQuality]  # 输出数据库客户端
```

### 数据处理属性

```python
# 设备状态
self.device_operation_flag: Optional[str]                     # 设备运行标志位
self.feed_amount_column_name: Optional[Union[str, List[str]]]  # 喂料量列名

# 变量管理
self.input_name_list: List[str]           # 输入变量名列表
self.output_name_list: List[str]          # 输出变量名列表

# 样本表信息
self.sample_table_information: Dict[str, Any]  # 样本表信息
self.sample_table_in_name: str                 # 输入样本表名
self.sample_table_out_name: str                # 输出样本表名
```

## 🔄 核心方法

### 异步初始化方法

#### `async def get_basic_info(self) -> None`
异步获取基本信息和初始化配置。

**功能流程**:
1. 异步获取模型参数
2. 异步加载模型和归一化参数
3. 获取预测长度和时间配置
4. 创建输出数据库连接
5. 获取样本表信息
6. 获取变量名列表
7. 获取设备相关信息

```python
# 使用示例
predictor = AsyncClassicRealtimePredictor(project_name, dbconfig)
await predictor.initialize()  # 触发异步初始化
await predictor.get_basic_info()  # 获取基本配置信息

# 检查初始化结果
print(f"算法名称: {predictor.algorithm_name}")
print(f"预测长度: {predictor.pred_length} 分钟")
print(f"输入变量数量: {len(predictor.input_name_list)}")
print(f"输出变量数量: {len(predictor.output_name_list)}")
```

### 数据获取方法

#### `async def get_pred_input(self, pred_time: datetime.datetime) -> Optional[pd.DataFrame]`
异步从数据库获取最新的输入数据。

**参数**:
- `pred_time`: 预测时间点

**返回值**:
- `Optional[pd.DataFrame]`: 输入数据DataFrame,如果获取失败返回None

**功能特性**:
- 自动处理数据库连接错误
- 支持数据库重连机制
- 返回指定时间点的最新数据

```python
# 获取预测输入数据
current_time = datetime.datetime.now().replace(second=0, microsecond=0)
input_data = await predictor.get_pred_input(current_time)

if input_data is not None:
    print(f"获取到 {len(input_data)} 行输入数据")
    print(f"数据列: {list(input_data.columns)}")
else:
    print("未能获取到输入数据")
```

#### `def generate_local_input(self) -> pd.DataFrame`
本地测试模式下生成随机输入数据。

**返回值**:
- `pd.DataFrame`: 随机生成的输入数据

**使用场景**:
- 本地开发测试
- 模型验证
- 性能测试

```python
# 本地测试模式
predictor = AsyncClassicRealtimePredictor(
    project_name="test_project",
    dbconfig=None,
    local_test_mode=True
)

# 生成测试数据
test_input = predictor.generate_local_input()
print(f"生成测试数据: {test_input.shape}")
```

### 数据处理方法

#### `async def rt_input_process(self, input_data: pd.DataFrame, pred_time: datetime.datetime) -> Optional[np.ndarray]`
异步实时预测输入处理。

**处理流程**:
1. **分钟均值处理**: 对输入数据进行分钟级别的均值计算
2. **平滑滤波**: 应用滤波算法减少噪声
3. **数据截取**: 根据预测长度截取所需的数据段
4. **数据验证**: 检查数据完整性和有效性

**参数**:
- `input_data`: 原始输入数据
- `pred_time`: 预测时间点

**返回值**:
- `Optional[np.ndarray]`: 处理后的输入数组,如果处理失败返回None

```python
# 数据处理示例
input_data = await predictor.get_pred_input(current_time)
if input_data is not None:
    processed_input = await predictor.rt_input_process(input_data, current_time)
    
    if processed_input is not None:
        print(f"处理后数据形状: {processed_input.shape}")
        print(f"数据范围: [{processed_input.min():.3f}, {processed_input.max():.3f}]")
    else:
        print("数据处理失败")
```

### 预测执行方法

#### `async def pred_when_device_running(self, pred_time: datetime.datetime) -> None`
设备运行时的异步预测流程。

**执行流程**:
1. **模型初始化检查**: 验证模型和归一化器是否已正确加载
2. **输入数据预处理**: 获取并处理输入数据
3. **真实值获取**: 从数据库获取对应的真实值(如果存在)
4. **数据转换**: 将数据转换为模型所需的格式
5. **模型预测**: 执行前向推理
6. **反归一化**: 将预测结果转换回原始尺度
7. **结果写入**: 将预测值写入数据库

```python
# 设备运行时预测
current_time = datetime.datetime.now().replace(second=0, microsecond=0)

# 检查设备状态(示例)
device_running = True  # 实际应从数据库或传感器获取

if device_running:
    await predictor.pred_when_device_running(current_time)
    print(f"设备运行预测完成: {current_time}")
```

#### `async def pred_when_device_not_running(self, pred_time: datetime.datetime) -> None`
设备不运行时的异步预测流程。

**执行流程**:
1. **预测值置零**: 将所有输出变量的预测值设为0
2. **结果写入**: 将零值预测结果写入数据库
3. **状态记录**: 记录设备非运行状态

```python
# 设备不运行时预测
if not device_running:
    await predictor.pred_when_device_not_running(current_time)
    print(f"设备停机预测完成: {current_time}")
```

### 主执行方法

#### `async def main(self) -> None`
主要的异步实时预测流程。

**执行流程**:
1. **时间同步**: 获取当前预测时间(秒和微秒置零)
2. **基本信息检查**: 确保预测器已正确初始化
3. **模型热重载检查**: 检查是否需要重新加载模型
4. **输入数据获取**: 从数据库或本地生成输入数据
5. **设备状态判断**: 根据设备运行状态选择预测策略
6. **预测执行**: 执行相应的预测流程
7. **GPU缓存清理**: 清理GPU内存缓存
8. **异常处理**: 处理预测过程中的各种异常

```python
# 完整的预测流程
async def run_prediction_loop():
    predictor = AsyncClassicRealtimePredictor(project_name, dbconfig)
    
    try:
        # 初始化
        await predictor.initialize()
        await predictor.get_basic_info()
        
        # 持续预测循环
        while True:
            try:
                await predictor.main()
                
                # 等待下一个预测周期
                await asyncio.sleep(60)  # 每分钟预测一次
                
            except KeyboardInterrupt:
                print("收到停止信号,正在退出...")
                break
            except Exception as e:
                print(f"预测过程中发生错误: {e}")
                await asyncio.sleep(10)  # 错误后等待10秒再重试
                
    finally:
        # 清理资源
        await predictor.cleanup()

# 运行预测循环
if __name__ == "__main__":
    asyncio.run(run_prediction_loop())
```

## 💡 完整使用示例

### 基础使用示例

```python
import asyncio
import datetime
from typing import Dict, Any
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

# 数据库配置
dbconfig = {
    "web": {
        "server": "192.168.1.100",
        "database": "IndustryWeb",
        "username": "predict_user",
        "password": "secure_password"
    },
    "timeseries": {
        "server": "192.168.1.101",
        "database": "TimeSeriesDB",
        "username": "ts_user",
        "password": "ts_password"
    },
    "realtimepredict": {
        "server": "192.168.1.102",
        "database": "RealtimePredictDB",
        "username": "rt_user",
        "password": "rt_password"
    }
}

async def basic_prediction_example():
    """基础预测示例"""
    
    # 创建预测器实例
    predictor = AsyncClassicRealtimePredictor(
        project_name="steel_temperature_prediction",
        dbconfig=dbconfig,
        local_test_mode=False
    )
    
    try:
        # 异步初始化
        print("正在初始化预测器...")
        await predictor.initialize()
        
        # 获取基本配置信息
        print("正在获取基本配置信息...")
        await predictor.get_basic_info()
        
        # 显示配置信息
        print(f"项目名称: {predictor.project_name}")
        print(f"算法名称: {predictor.algorithm_name}")
        print(f"预测长度: {predictor.pred_length} 分钟")
        print(f"输入变量: {predictor.input_name_list}")
        print(f"输出变量: {predictor.output_name_list}")
        print(f"设备运行标志位: {predictor.device_operation_flag}")
        
        # 执行单次预测
        print("\n开始执行预测...")
        await predictor.main()
        print("预测完成!")
        
    except Exception as e:
        print(f"预测过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await predictor.cleanup()
        print("资源清理完成")

# 运行示例
if __name__ == "__main__":
    asyncio.run(basic_prediction_example())
```

### 持续预测服务示例

```python
import asyncio
import signal
import datetime
from typing import Dict, Any
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

class PredictionService:
    """预测服务类"""
    
    def __init__(self, project_name: str, dbconfig: Dict[str, Any]):
        self.project_name = project_name
        self.dbconfig = dbconfig
        self.predictor = None
        self.running = False
        self.prediction_interval = 60  # 预测间隔(秒)
    
    async def start(self):
        """启动预测服务"""
        print(f"启动预测服务: {self.project_name}")
        
        # 创建预测器实例
        self.predictor = AsyncClassicRealtimePredictor(
            project_name=self.project_name,
            dbconfig=self.dbconfig,
            local_test_mode=False
        )
        
        try:
            # 初始化预测器
            await self.predictor.initialize()
            await self.predictor.get_basic_info()
            
            print(f"预测器初始化完成")
            print(f"算法: {self.predictor.algorithm_name}")
            print(f"输入变量数: {len(self.predictor.input_name_list)}")
            print(f"输出变量数: {len(self.predictor.output_name_list)}")
            
            # 开始预测循环
            self.running = True
            await self._prediction_loop()
            
        except Exception as e:
            print(f"预测服务启动失败: {e}")
            raise
    
    async def stop(self):
        """停止预测服务"""
        print("正在停止预测服务...")
        self.running = False
        
        if self.predictor:
            await self.predictor.cleanup()
        
        print("预测服务已停止")
    
    async def _prediction_loop(self):
        """预测循环"""
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.running:
            try:
                start_time = datetime.datetime.now()
                
                # 执行预测
                await self.predictor.main()
                
                # 重置错误计数
                consecutive_errors = 0
                
                # 计算执行时间
                execution_time = (datetime.datetime.now() - start_time).total_seconds()
                print(f"预测完成 - 执行时间: {execution_time:.2f}秒")
                
                # 等待下一个预测周期
                await asyncio.sleep(self.prediction_interval)
                
            except Exception as e:
                consecutive_errors += 1
                print(f"预测错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                # 如果连续错误过多,停止服务
                if consecutive_errors >= max_consecutive_errors:
                    print(f"连续错误次数过多,停止预测服务")
                    break
                
                # 错误后等待较短时间再重试
                await asyncio.sleep(10)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.predictor:
                return False
            
            # 检查数据库连接
            if self.predictor.ts_db_client:
                await self.predictor.ts_db_client.test_connection()
            
            return True
            
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False

# 全局服务实例
service = None

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum},正在优雅关闭...")
    if service:
        asyncio.create_task(service.stop())

async def main():
    """主函数"""
    global service
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 数据库配置
    dbconfig = {
        "web": {"server": "localhost", "database": "web_db", "username": "user", "password": "pass"},
        "timeseries": {"server": "localhost", "database": "ts_db", "username": "user", "password": "pass"},
        "realtimepredict": {"server": "localhost", "database": "predict_db", "username": "user", "password": "pass"}
    }
    
    # 创建并启动服务
    service = PredictionService(
        project_name="production_quality_prediction",
        dbconfig=dbconfig
    )
    
    try:
        await service.start()
    except KeyboardInterrupt:
        print("\n收到键盘中断信号")
    finally:
        if service:
            await service.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

### 本地测试示例

```python
import asyncio
import pandas as pd
import numpy as np
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

async def local_test_example():
    """本地测试示例"""
    
    # 创建本地测试预测器
    predictor = AsyncClassicRealtimePredictor(
        project_name="local_test_project",
        dbconfig=None,  # 本地测试不需要数据库配置
        local_test_mode=True
    )
    
    try:
        # 初始化(本地测试模式下会跳过数据库相关初始化)
        await predictor.initialize()
        
        # 模拟设置基本信息
        predictor.input_name_list = ["temperature", "pressure", "flow_rate", "concentration"]
        predictor.output_name_list = ["quality_index", "yield_rate"]
        predictor.pred_length = 10
        
        print(f"本地测试配置:")
        print(f"输入变量: {predictor.input_name_list}")
        print(f"输出变量: {predictor.output_name_list}")
        print(f"预测长度: {predictor.pred_length}")
        
        # 生成测试数据
        test_input = predictor.generate_local_input()
        print(f"\n生成测试数据形状: {test_input.shape}")
        print(f"测试数据预览:")
        print(test_input.head())
        
        # 模拟数据处理
        current_time = pd.Timestamp.now().replace(second=0, microsecond=0)
        processed_input = await predictor.rt_input_process(test_input, current_time)
        
        if processed_input is not None:
            print(f"\n处理后数据形状: {processed_input.shape}")
            print(f"数据统计:")
            print(f"  均值: {processed_input.mean():.3f}")
            print(f"  标准差: {processed_input.std():.3f}")
            print(f"  最小值: {processed_input.min():.3f}")
            print(f"  最大值: {processed_input.max():.3f}")
        
        print("\n本地测试完成!")
        
    except Exception as e:
        print(f"本地测试失败: {e}")
        
    finally:
        await predictor.cleanup()

if __name__ == "__main__":
    asyncio.run(local_test_example())
```

## 🔍 最佳实践

### 1. 错误处理和重试机制

```python
import asyncio
from typing import Optional

class RobustAsyncPredictor(AsyncClassicRealtimePredictor):
    """增强的异步预测器,具备完善的错误处理"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_retries = 3
        self.retry_delay = 5
    
    async def robust_main(self) -> bool:
        """带重试机制的主预测方法"""
        for attempt in range(self.max_retries):
            try:
                await self.main()
                return True
                
            except Exception as e:
                self.logger.error(f"预测失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                else:
                    self.logger.error("所有重试均失败")
                    return False
        
        return False
    
    async def safe_get_pred_input(self, pred_time) -> Optional[pd.DataFrame]:
        """安全的输入数据获取"""
        try:
            return await self.get_pred_input(pred_time)
        except Exception as e:
            self.logger.error(f"获取输入数据失败: {e}")
            
            # 尝试数据库重连
            try:
                await self.db_reconnect()
                return await self.get_pred_input(pred_time)
            except Exception as reconnect_error:
                self.logger.error(f"数据库重连失败: {reconnect_error}")
                return None
```

### 2. 性能监控

```python
import time
from typing import Dict, Any

class MonitoredAsyncPredictor(AsyncClassicRealtimePredictor):
    """带性能监控的异步预测器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.performance_stats = {
            "total_predictions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "average_execution_time": 0.0,
            "total_execution_time": 0.0
        }
    
    async def monitored_main(self) -> None:
        """带性能监控的主预测方法"""
        start_time = time.time()
        
        try:
            await self.main()
            self.performance_stats["successful_predictions"] += 1
            
        except Exception as e:
            self.performance_stats["failed_predictions"] += 1
            self.logger.error(f"预测失败: {e}")
            raise
            
        finally:
            # 更新性能统计
            execution_time = time.time() - start_time
            self.performance_stats["total_predictions"] += 1
            self.performance_stats["total_execution_time"] += execution_time
            self.performance_stats["average_execution_time"] = (
                self.performance_stats["total_execution_time"] / 
                self.performance_stats["total_predictions"]
            )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        stats = self.performance_stats.copy()
        
        if stats["total_predictions"] > 0:
            stats["success_rate"] = (
                stats["successful_predictions"] / stats["total_predictions"] * 100
            )
        else:
            stats["success_rate"] = 0.0
        
        return stats
    
    def print_performance_report(self) -> None:
        """打印性能报告"""
        report = self.get_performance_report()
        
        print("\n=== 性能报告 ===")
        print(f"总预测次数: {report['total_predictions']}")
        print(f"成功预测: {report['successful_predictions']}")
        print(f"失败预测: {report['failed_predictions']}")
        print(f"成功率: {report['success_rate']:.2f}%")
        print(f"平均执行时间: {report['average_execution_time']:.3f}秒")
        print(f"总执行时间: {report['total_execution_time']:.3f}秒")
        print("================\n")
```

### 3. 配置管理

```python
import json
from pathlib import Path
from typing import Dict, Any

class ConfigurableAsyncPredictor(AsyncClassicRealtimePredictor):
    """支持配置文件的异步预测器"""
    
    @classmethod
    def from_config_file(cls, config_path: str) -> 'ConfigurableAsyncPredictor':
        """从配置文件创建预测器实例"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        return cls(
            project_name=config['project_name'],
            dbconfig=config['database'],
            local_test_mode=config.get('local_test_mode', False)
        )
    
    def save_config(self, config_path: str) -> None:
        """保存当前配置到文件"""
        config = {
            'project_name': self.project_name,
            'database': self.dbconfig,
            'local_test_mode': self.local_test_mode,
            'task_type': self.task_type
        }
        
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

# 配置文件示例 (config.json)
"""
{
  "project_name": "steel_quality_prediction",
  "database": {
    "web": {
      "server": "192.168.1.100",
      "database": "IndustryWeb",
      "username": "predict_user",
      "password": "secure_password"
    },
    "timeseries": {
      "server": "192.168.1.101",
      "database": "TimeSeriesDB",
      "username": "ts_user",
      "password": "ts_password"
    },
    "realtimepredict": {
      "server": "192.168.1.102",
      "database": "RealtimePredictDB",
      "username": "rt_user",
      "password": "rt_password"
    }
  },
  "local_test_mode": false
}
"""

# 使用配置文件
async def config_example():
    predictor = ConfigurableAsyncPredictor.from_config_file("config.json")
    
    try:
        await predictor.initialize()
        await predictor.get_basic_info()
        await predictor.main()
    finally:
        await predictor.cleanup()
```

## 🚨 注意事项

### 1. 数据库连接管理

- **连接池**: 确保数据库连接池配置合理,避免连接泄漏
- **重连机制**: 实现健壮的数据库重连逻辑
- **超时设置**: 设置合适的数据库操作超时时间

### 2. 模型热重载

- **文件同步**: 确保模型文件、归一化文件同步更新
- **版本控制**: 建议对模型文件进行版本管理
- **回滚机制**: 实现模型加载失败时的回滚策略

### 3. 性能优化

- **批量处理**: 考虑批量处理多个预测请求
- **缓存策略**: 对频繁访问的数据实现缓存
- **GPU管理**: 合理管理GPU内存,避免内存泄漏

### 4. 错误处理

- **分类处理**: 对不同类型的错误实现不同的处理策略
- **日志记录**: 详细记录错误信息,便于问题排查
- **监控告警**: 实现关键错误的监控和告警机制

## 🔗 相关文档

- [异步基础预测器文档](./async_basic_predictor.md)
- [异步时间序列预测器文档](./async_time_series_predictor.md)
- [异步预测器使用示例](./usage_examples.md)