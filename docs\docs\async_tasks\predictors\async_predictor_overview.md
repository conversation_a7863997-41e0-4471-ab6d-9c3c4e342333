# 异步预测器模块概览

异步预测器模块 (`async_predictor_agents`) 是 IndustryTSLib 中专门用于实时预测任务的异步实现,旨在解决传统同步预测器在高并发场景下的线程阻塞问题。

## 🚀 核心特性

### 完全异步架构
- **非阻塞操作**: 所有数据库操作、文件I/O和模型推理均采用异步实现
- **高并发支持**: 支持多个预测任务并发执行,提升系统吞吐量
- **资源优化**: 通过异步操作减少线程阻塞,提高CPU和内存利用率

### 智能模型管理
- **热重载机制**: 基于文件修改时间的智能模型热重载
- **三文件同步检查**: 确保模型文件、归一化参数的完整性
- **类型安全**: 完整的类型注解和运行时类型检查

### 多样化预测支持
- **经典预测**: 支持传统机器学习模型的实时预测
- **时间序列预测**: 支持Encoder-Decoder架构的长序列预测
- **多输出预测**: 支持单模型多变量输出预测

### 企业级特性
- **设备状态检测**: 智能设备运行状态检查和处理
- **数据预处理**: 内置数据清洗、滤波和归一化功能
- **异常处理**: 完善的错误处理和数据库重连机制
- **本地测试模式**: 支持离线开发和测试

## 📋 模块组件

### 核心基类

#### AsyncBaseRealtimePredictor
异步实时预测抽象基类,提供所有异步预测器的通用功能:

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncBaseRealtimePredictor

class CustomAsyncPredictor(AsyncBaseRealtimePredictor):
    async def get_basic_info(self) -> None:
        # 实现基本信息获取逻辑
        pass
    
    async def main(self) -> None:
        # 实现主预测流程
        pass
```

**主要功能**:
- 异步数据库连接管理
- 项目和模型信息管理
- 变量名列表管理
- 模型加载和归一化参数管理
- 预测计数和热重载机制
- 模型文件修改时间监测
- 异步日志记录和资源清理

### 具体实现类

#### AsyncClassicRealtimePredictor
异步经典实时预测器,适用于传统机器学习模型:

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

# 创建经典异步预测器
predictor = AsyncClassicRealtimePredictor(
    project_name="production_quality_predict",
    dbconfig=database_config,
    local_test_mode=False
)

# 异步执行预测
await predictor.main()
```

**特性**:
- 多输出变量软测量预测
- 设备状态检查和数据预处理
- 基于文件修改时间的智能模型热重载
- 类型安全的变量名管理
- 完全异步的数据库操作

#### AsyncTimeSeriesRealtimePredictor
异步时间序列实时预测器,支持Encoder-Decoder架构:

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncTimeSeriesRealtimePredictor

# 创建时间序列异步预测器
predictor = AsyncTimeSeriesRealtimePredictor(
    project_name="energy_consumption_forecast",
    dbconfig=database_config,
    local_test_mode=False
)

# 异步执行时间序列预测
await predictor.main()
```

**特性**:
- Encoder-Decoder架构支持
- 时间特征自动提取
- 序列预测表和实值表更新
- 峰值检测和数据滤波
- 智能设备状态检测
- 长序列时间序列预测

## 🔧 技术架构

### 异步设计模式

```python
# 异步初始化模式
async def initialize(self) -> None:
    await super().initialize()
    if hasattr(self, '_need_async_init') and self._need_async_init:
        await self._init_database_connections()
        await self._get_sample_model_name()
        delattr(self, '_need_async_init')

# 异步资源管理模式
async def cleanup(self) -> None:
    if self.web_db_client:
        await self.web_db_client.close()
    if self.ts_db_client:
        await self.ts_db_client.close()
    # ... 其他资源清理
```

### 模型热重载机制

```python
# 基于计数触发的文件检测策略
async def should_reload_model(self, project_name: Optional[str] = None) -> bool:
    self.predict_count += 1
    
    if self.predict_count >= self.predict_count_threshold:
        file_based_reload = await self.check_model_files_modified(project_name)
        
        if file_based_reload:
            self.predict_count = 0
            return True
        else:
            self.predict_count = 0
            return False
    
    return False
```

### 类型安全设计

```python
from typing import Optional, Dict, Any, List, Tuple, Union

class AsyncBaseRealtimePredictor(AsyncScheduledTask, ABC):
    def __init__(
        self,
        project_name: str,
        dbconfig: Optional[Dict[str, Any]],
        local_test_mode: bool = False,
        task_type: str = "async_realtime_predict",
    ) -> None:
        # 类型安全的参数验证
        if dbconfig is None:
            raise ValueError("dbconfig 不能为 None")
```

## 🎯 使用场景

### 工业软测量
- **质量预测**: 产品质量指标的实时预测
- **设备监控**: 设备状态和性能参数预测
- **过程优化**: 生产过程关键参数预测

### 时间序列预测
- **能耗预测**: 设备或系统能耗的长期预测
- **负荷预测**: 生产负荷和需求预测
- **趋势分析**: 关键指标的趋势预测

### 实时决策支持
- **异常检测**: 基于预测值的异常检测
- **预警系统**: 提前预警潜在问题
- **自动控制**: 为控制系统提供预测输入

## 📊 性能优势

### 并发性能
- **异步I/O**: 数据库操作不阻塞主线程
- **并发预测**: 支持多个预测任务同时运行
- **资源复用**: 高效的连接池和资源管理

### 内存优化
- **惰性加载**: 按需加载模型和数据
- **GPU缓存管理**: 自动清理GPU内存
- **数据流处理**: 流式数据处理减少内存占用

### 可靠性保障
- **自动重连**: 数据库连接断开自动重连
- **错误恢复**: 完善的异常处理和恢复机制
- **状态监控**: 实时监控预测器运行状态

## 🔗 相关文档

- [异步基础预测器详解](./async_basic_predictor.md)
- [异步经典预测器使用指南](./async_classic_predictor.md)
- [异步时间序列预测器指南](./async_time_series_predictor.md)
- [异步预测器使用示例](./usage_examples.md)


## 📝 快速开始

查看 [使用示例](./usage_examples.md) 了解如何快速开始使用异步预测器模块。