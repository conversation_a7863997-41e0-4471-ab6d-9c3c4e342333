# AsyncTimeSeriesRealtimePredictor - 异步时间序列实时预测器

`AsyncTimeSeriesRealtimePredictor` 是基于 Encoder-Decoder 架构的异步时间序列预测器,专门用于处理时间序列数据的实时预测任务,支持复杂的时序模式识别和多步预测。

## 📋 类概述

```python
from industrytslib.core_aysnc.async_predictor_agents import AsyncTimeSeriesRealtimePredictor

class AsyncTimeSeriesRealtimePredictor(AsyncBaseRealtimePredictor):
    """异步时间序列实时预测器
    
    基于 Encoder-Decoder 架构的异步时间序列预测,具备以下特性:
    - 时间特征处理(时间戳、周期性特征)
    - 序列预测表和实值表更新
    - 峰值检测和数据滤波
    - 智能设备状态检测
    - 类型安全和模型热重载
    - 完全异步操作
    """
```

## 🔧 初始化参数

### 构造函数

```python
def __init__(
    self,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]],
    local_test_mode: bool = False,
    task_type: str = "async_realtime_predict",
    freq: str = "T",
    seq_len: int = 96,
    label_len: int = 48,
    pred_len: int = 96,
) -> None:
```

**参数说明**:

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `project_name` | `str` | ✅ | - | 时间序列预测项目名称 |
| `dbconfig` | `Optional[Dict[str, Any]]` | ✅ | - | 数据库配置字典 |
| `local_test_mode` | `bool` | ❌ | `False` | 本地测试模式标志 |
| `task_type` | `str` | ❌ | `"async_realtime_predict"` | 任务类型标识 |
| `freq` | `str` | ❌ | `"T"` | 时间频率(T=分钟,H=小时,D=天) |
| `seq_len` | `int` | ❌ | `96` | 编码器输入序列长度 |
| `label_len` | `int` | ❌ | `48` | 解码器标签长度 |
| `pred_len` | `int` | ❌ | `96` | 预测序列长度 |

**时间序列参数详解**:

- **`freq`**: 时间频率标识
  - `"T"` 或 `"min"`: 分钟级别
  - `"H"`: 小时级别
  - `"D"`: 天级别
  - `"W"`: 周级别
  - `"M"`: 月级别

- **`seq_len`**: 编码器输入序列长度,决定模型能够"看到"的历史数据长度
- **`label_len`**: 解码器标签长度,用于解码器的初始输入
- **`pred_len`**: 预测序列长度,决定模型预测未来多少个时间步

**使用示例**:

```python
# 创建时间序列预测器实例
predictor = AsyncTimeSeriesRealtimePredictor(
    project_name="power_consumption_forecast",
    dbconfig={
        "web": {"server": "localhost", "database": "web_db", "username": "user", "password": "pass"},
        "timeseries": {"server": "localhost", "database": "ts_db", "username": "user", "password": "pass"},
        "realtimepredict": {"server": "localhost", "database": "predict_db", "username": "user", "password": "pass"}
    },
    local_test_mode=False,
    freq="T",        # 分钟级预测
    seq_len=144,     # 使用过去144分钟的数据(2.4小时)
    label_len=72,    # 解码器标签长度72分钟
    pred_len=96      # 预测未来96分钟(1.6小时)
)
```

## 🏗️ 核心属性

### 时间序列专有属性

```python
# 时间序列配置
self.freq: str                            # 时间频率
self.seq_len: int                         # 编码器输入序列长度
self.label_len: int                       # 解码器标签长度
self.pred_len: int                        # 预测序列长度

# 序列预测数据库连接
self.sequence_ts_db_client: Optional[AsyncMSSQLTimeSeries]      # 序列时序数据库客户端
self.sequence_output_db_client: Optional[Union[AsyncMSSQLTimeSeries, AsyncMSSQLQuality]]  # 序列输出数据库客户端
```

### 继承的基础属性

```python
# 预测配置(继承自基类)
self.pred_length: int                     # 预测长度(分钟)
self.pred_time: datetime.datetime         # 当前预测时间
self.algorithm_name: str                  # 算法名称(如 "Transformer", "Informer")
self.model_parameter: Dict[str, Any]      # 模型参数字典
self.model: torch.nn.Module               # 加载的时间序列模型实例
self.scaler_x: Any                        # 输入数据归一化器
self.scaler_y: Any                        # 输出数据归一化器

# 变量管理
self.input_name_list: List[str]           # 输入变量名列表
self.output_name_list: List[str]          # 输出变量名列表

# 设备状态
self.device_operation_flag: Optional[str]                     # 设备运行标志位
self.feed_amount_column_name: Optional[Union[str, List[str]]]  # 喂料量列名
```

## 🔄 核心方法

### 异步初始化方法

#### `async def __init__(self, ...)`
异步初始化时间序列预测器。

**初始化流程**:
1. 调用父类初始化方法
2. 设置时间序列专有参数
3. 标记需要异步初始化序列数据库连接

```python
# 创建实例(同步)
predictor = AsyncTimeSeriesRealtimePredictor(
    project_name="energy_forecast",
    dbconfig=dbconfig,
    freq="H",        # 小时级预测
    seq_len=168,     # 使用过去一周的数据
    label_len=24,    # 解码器标签长度24小时
    pred_len=48      # 预测未来48小时
)

# 异步初始化
await predictor.initialize()
```

#### `async def _async_init_sequence_database_connections(self) -> None`
异步初始化序列预测专用数据库连接。

**功能**:
- 创建序列时序数据库连接
- 创建序列输出数据库连接
- 确保连接的可用性

```python
# 手动初始化序列数据库连接(通常在 initialize() 中自动调用)
await predictor._async_init_sequence_database_connections()

# 检查连接状态
if predictor.sequence_ts_db_client:
    print("序列时序数据库连接已建立")
if predictor.sequence_output_db_client:
    print("序列输出数据库连接已建立")
```

#### `async def get_basic_info(self) -> None`
重写基类方法,实现完整的时间序列预测配置。

**配置流程**:
1. 异步获取模型参数
2. 异步加载模型和归一化参数
3. 获取时间序列专有参数(freq, seq_len, label_len, pred_len)
4. 创建输出数据库连接
5. 获取样本表信息和变量名列表
6. 获取设备相关信息
7. 检查序列预测表是否存在

```python
# 获取完整配置信息
await predictor.get_basic_info()

# 检查配置结果
print(f"时间频率: {predictor.freq}")
print(f"序列长度: {predictor.seq_len}")
print(f"标签长度: {predictor.label_len}")
print(f"预测长度: {predictor.pred_len}")
print(f"算法名称: {predictor.algorithm_name}")
print(f"输入变量: {predictor.input_name_list}")
print(f"输出变量: {predictor.output_name_list}")
```

### 数据获取方法

#### `async def get_sequence_model_input(self, pred_time: datetime.datetime) -> Tuple[Optional[torch.Tensor], Optional[torch.Tensor]]`
异步从数据库中根据变量名称列表获取编码器和解码器输入。

**参数**:
- `pred_time`: 预测时间点

**返回值**:
- `Tuple[Optional[torch.Tensor], Optional[torch.Tensor]]`: (编码器输入, 解码器输入)
  - 编码器输入形状: `[batch_size, seq_len, feature_dim]`
  - 解码器输入形状: `[batch_size, label_len + pred_len, feature_dim]`

**功能特性**:
- 自动处理时间窗口计算
- 支持多变量时间序列
- 包含时间特征(时间戳编码)
- 自动数据归一化
- 异常数据处理

```python
# 获取序列模型输入
current_time = datetime.datetime.now().replace(second=0, microsecond=0)
encoder_input, decoder_input = await predictor.get_sequence_model_input(current_time)

if encoder_input is not None and decoder_input is not None:
    print(f"编码器输入形状: {encoder_input.shape}")
    print(f"解码器输入形状: {decoder_input.shape}")
    
    # 检查数据质量
    print(f"编码器输入统计:")
    print(f"  均值: {encoder_input.mean().item():.3f}")
    print(f"  标准差: {encoder_input.std().item():.3f}")
    print(f"  最小值: {encoder_input.min().item():.3f}")
    print(f"  最大值: {encoder_input.max().item():.3f}")
else:
    print("未能获取到序列输入数据")
```

### 数据更新方法

#### `async def update_sequence_tables(self, pred_time: datetime.datetime, predictions: torch.Tensor, real_values: Optional[torch.Tensor] = None) -> None`
异步更新序列预测表和实值表。

**参数**:
- `pred_time`: 预测时间点
- `predictions`: 预测结果张量,形状为 `[batch_size, pred_len, output_dim]`
- `real_values`: 可选的真实值张量,形状为 `[batch_size, pred_len, output_dim]`

**功能特性**:
- 批量更新预测结果
- 支持多步预测结果写入
- 自动处理时间戳生成
- 支持真实值对比更新
- 异常处理和重试机制

```python
# 模拟预测结果
predictions = torch.randn(1, predictor.pred_len, len(predictor.output_name_list))
real_values = torch.randn(1, predictor.pred_len, len(predictor.output_name_list))  # 可选

# 更新序列表
current_time = datetime.datetime.now().replace(second=0, microsecond=0)
await predictor.update_sequence_tables(
    pred_time=current_time,
    predictions=predictions,
    real_values=real_values  # 可选参数
)

print(f"序列预测表更新完成: {current_time}")
```

### 主执行方法

#### `async def main(self) -> None`
主要的异步时间序列实时预测流程。

**执行流程**:
1. **时间同步**: 获取当前预测时间(秒和微秒置零)
2. **基本信息检查**: 确保预测器已正确初始化
3. **模型热重载检查**: 检查是否需要重新加载模型
4. **序列输入获取**: 从数据库获取编码器和解码器输入
5. **设备状态判断**: 根据设备运行状态选择预测策略
6. **时间序列预测**: 执行 Encoder-Decoder 预测
7. **结果处理**: 反归一化和后处理
8. **数据库更新**: 更新序列预测表和实值表
9. **GPU缓存清理**: 清理GPU内存缓存
10. **异常处理**: 处理预测过程中的各种异常

```python
# 完整的时间序列预测流程
async def run_time_series_prediction():
    predictor = AsyncTimeSeriesRealtimePredictor(
        project_name="industrial_time_series",
        dbconfig=dbconfig,
        freq="T",
        seq_len=96,
        label_len=48,
        pred_len=96
    )
    
    try:
        # 初始化
        await predictor.initialize()
        await predictor.get_basic_info()
        
        print(f"时间序列预测器初始化完成")
        print(f"模型: {predictor.algorithm_name}")
        print(f"序列配置: seq_len={predictor.seq_len}, pred_len={predictor.pred_len}")
        
        # 执行预测
        await predictor.main()
        print("时间序列预测完成!")
        
    except Exception as e:
        print(f"时间序列预测失败: {e}")
        
    finally:
        # 清理资源
        await predictor.cleanup()

# 运行预测
if __name__ == "__main__":
    asyncio.run(run_time_series_prediction())
```

## 💡 完整使用示例

### 电力负荷预测示例

```python
import asyncio
import datetime
import torch
import pandas as pd
from typing import Dict, Any, Optional
from industrytslib.core_aysnc.async_predictor_agents import AsyncTimeSeriesRealtimePredictor

class PowerLoadPredictor(AsyncTimeSeriesRealtimePredictor):
    """电力负荷预测器"""
    
    def __init__(self, dbconfig: Dict[str, Any]):
        super().__init__(
            project_name="power_load_forecast",
            dbconfig=dbconfig,
            local_test_mode=False,
            freq="H",        # 小时级预测
            seq_len=168,     # 使用过去一周的数据(168小时)
            label_len=24,    # 解码器标签长度24小时
            pred_len=48      # 预测未来48小时
        )
        
        # 电力负荷预测专有属性
        self.peak_threshold = 0.8  # 峰值检测阈值
        self.load_categories = ["residential", "commercial", "industrial"]
    
    async def analyze_load_pattern(self, encoder_input: torch.Tensor) -> Dict[str, Any]:
        """分析负荷模式"""
        analysis = {
            "peak_hours": [],
            "valley_hours": [],
            "average_load": 0.0,
            "load_volatility": 0.0
        }
        
        # 转换为numpy进行分析
        load_data = encoder_input.squeeze().cpu().numpy()
        
        # 计算统计指标
        analysis["average_load"] = float(load_data.mean())
        analysis["load_volatility"] = float(load_data.std())
        
        # 峰值检测
        max_load = load_data.max()
        min_load = load_data.min()
        threshold_high = min_load + (max_load - min_load) * self.peak_threshold
        threshold_low = min_load + (max_load - min_load) * (1 - self.peak_threshold)
        
        for i, load in enumerate(load_data):
            if load >= threshold_high:
                analysis["peak_hours"].append(i)
            elif load <= threshold_low:
                analysis["valley_hours"].append(i)
        
        return analysis
    
    async def enhanced_main(self) -> Optional[Dict[str, Any]]:
        """增强的主预测流程,包含负荷分析"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        try:
            # 确保基本信息已初始化
            if not hasattr(self, 'model') or self.model is None:
                await self.get_basic_info()
            
            # 检查模型重载
            if await self.should_reload_model():
                self.logger.warning("检测到模型需要重载...")
                await self.get_basic_info()
                self.logger.warning("模型重载完成")
            
            # 获取序列输入
            encoder_input, decoder_input = await self.get_sequence_model_input(current_time)
            
            if encoder_input is None or decoder_input is None:
                self.logger.error("无法获取序列输入数据")
                return None
            
            # 负荷模式分析
            load_analysis = await self.analyze_load_pattern(encoder_input)
            self.logger.info(f"负荷分析结果: {load_analysis}")
            
            # 执行预测
            with torch.no_grad():
                self.model.eval()
                predictions = self.model(encoder_input, decoder_input)
            
            # 反归一化
            if self.scaler_y is not None:
                predictions_np = predictions.cpu().numpy()
                predictions_denorm = self.scaler_y.inverse_transform(
                    predictions_np.reshape(-1, predictions_np.shape[-1])
                ).reshape(predictions_np.shape)
                predictions = torch.from_numpy(predictions_denorm)
            
            # 更新数据库
            await self.update_sequence_tables(
                pred_time=current_time,
                predictions=predictions
            )
            
            # 返回预测结果和分析
            result = {
                "prediction_time": current_time,
                "predictions": predictions.cpu().numpy().tolist(),
                "load_analysis": load_analysis,
                "model_info": {
                    "algorithm": self.algorithm_name,
                    "seq_len": self.seq_len,
                    "pred_len": self.pred_len
                }
            }
            
            self.logger.info(f"电力负荷预测完成: {current_time}")
            return result
            
        except Exception as e:
            self.logger.error(f"电力负荷预测失败: {e}")
            return None
        
        finally:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

# 使用示例
async def power_load_prediction_example():
    """电力负荷预测示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "power-db-server",
            "database": "PowerSystemDB",
            "username": "power_user",
            "password": "power_password"
        },
        "timeseries": {
            "server": "timeseries-server",
            "database": "PowerTimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-server",
            "database": "PowerPredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建电力负荷预测器
    predictor = PowerLoadPredictor(dbconfig)
    
    try:
        # 初始化
        print("正在初始化电力负荷预测器...")
        await predictor.initialize()
        await predictor.get_basic_info()
        
        print(f"预测器配置:")
        print(f"  项目: {predictor.project_name}")
        print(f"  算法: {predictor.algorithm_name}")
        print(f"  时间频率: {predictor.freq}")
        print(f"  序列长度: {predictor.seq_len}")
        print(f"  预测长度: {predictor.pred_len}")
        print(f"  输入变量: {predictor.input_name_list}")
        print(f"  输出变量: {predictor.output_name_list}")
        
        # 执行增强预测
        print("\n开始执行电力负荷预测...")
        result = await predictor.enhanced_main()
        
        if result:
            print("\n=== 预测结果 ===")
            print(f"预测时间: {result['prediction_time']}")
            print(f"预测形状: {len(result['predictions'])}x{len(result['predictions'][0])}")
            
            print("\n=== 负荷分析 ===")
            analysis = result['load_analysis']
            print(f"平均负荷: {analysis['average_load']:.2f}")
            print(f"负荷波动性: {analysis['load_volatility']:.2f}")
            print(f"峰值时段数: {len(analysis['peak_hours'])}")
            print(f"谷值时段数: {len(analysis['valley_hours'])}")
            
            print("\n电力负荷预测完成!")
        else:
            print("电力负荷预测失败")
        
    except Exception as e:
        print(f"电力负荷预测过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await predictor.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(power_load_prediction_example())
```

### 工业设备状态预测示例

```python
import asyncio
import datetime
import torch
import numpy as np
from typing import Dict, Any, List, Optional
from industrytslib.core_aysnc.async_predictor_agents import AsyncTimeSeriesRealtimePredictor

class EquipmentHealthPredictor(AsyncTimeSeriesRealtimePredictor):
    """工业设备健康状态预测器"""
    
    def __init__(self, dbconfig: Dict[str, Any]):
        super().__init__(
            project_name="equipment_health_forecast",
            dbconfig=dbconfig,
            local_test_mode=False,
            freq="T",        # 分钟级监控
            seq_len=1440,    # 使用过去24小时的数据(1440分钟)
            label_len=60,    # 解码器标签长度1小时
            pred_len=120     # 预测未来2小时的设备状态
        )
        
        # 设备健康预测专有属性
        self.health_thresholds = {
            "normal": (0.8, 1.0),
            "warning": (0.6, 0.8),
            "critical": (0.0, 0.6)
        }
        self.anomaly_detection_window = 30  # 异常检测窗口(分钟)
    
    def classify_health_status(self, health_scores: np.ndarray) -> List[str]:
        """分类设备健康状态"""
        statuses = []
        
        for score in health_scores:
            if self.health_thresholds["normal"][0] <= score <= self.health_thresholds["normal"][1]:
                statuses.append("normal")
            elif self.health_thresholds["warning"][0] <= score < self.health_thresholds["warning"][1]:
                statuses.append("warning")
            else:
                statuses.append("critical")
        
        return statuses
    
    def detect_anomalies(self, data: np.ndarray) -> Dict[str, Any]:
        """检测异常模式"""
        # 使用滑动窗口检测异常
        window_size = min(self.anomaly_detection_window, len(data))
        anomalies = []
        
        for i in range(len(data) - window_size + 1):
            window = data[i:i + window_size]
            
            # 计算统计指标
            mean_val = np.mean(window)
            std_val = np.std(window)
            
            # 异常检测(基于3-sigma规则)
            threshold = mean_val + 3 * std_val
            
            if np.any(window > threshold) or np.any(window < mean_val - 3 * std_val):
                anomalies.append({
                    "start_index": i,
                    "end_index": i + window_size - 1,
                    "severity": "high" if np.max(window) > threshold * 1.5 else "medium"
                })
        
        return {
            "anomaly_count": len(anomalies),
            "anomalies": anomalies,
            "overall_stability": "stable" if len(anomalies) == 0 else "unstable"
        }
    
    async def equipment_health_main(self) -> Optional[Dict[str, Any]]:
        """设备健康预测主流程"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        try:
            # 确保基本信息已初始化
            if not hasattr(self, 'model') or self.model is None:
                await self.get_basic_info()
            
            # 检查模型重载
            if await self.should_reload_model():
                self.logger.warning("检测到模型需要重载...")
                await self.get_basic_info()
                self.logger.warning("模型重载完成")
            
            # 获取序列输入
            encoder_input, decoder_input = await self.get_sequence_model_input(current_time)
            
            if encoder_input is None or decoder_input is None:
                self.logger.error("无法获取序列输入数据")
                return None
            
            # 异常检测
            historical_data = encoder_input.squeeze().cpu().numpy()
            anomaly_analysis = self.detect_anomalies(historical_data)
            self.logger.info(f"异常检测结果: {anomaly_analysis}")
            
            # 执行健康状态预测
            with torch.no_grad():
                self.model.eval()
                health_predictions = self.model(encoder_input, decoder_input)
            
            # 反归一化
            if self.scaler_y is not None:
                predictions_np = health_predictions.cpu().numpy()
                predictions_denorm = self.scaler_y.inverse_transform(
                    predictions_np.reshape(-1, predictions_np.shape[-1])
                ).reshape(predictions_np.shape)
                health_predictions = torch.from_numpy(predictions_denorm)
            
            # 健康状态分类
            health_scores = health_predictions.squeeze().cpu().numpy()
            if health_scores.ndim == 1:
                health_scores = health_scores.reshape(-1, 1)
            
            # 对每个输出变量进行健康状态分类
            health_classifications = []
            for i in range(health_scores.shape[1]):
                variable_scores = health_scores[:, i]
                variable_statuses = self.classify_health_status(variable_scores)
                health_classifications.append({
                    "variable": self.output_name_list[i] if i < len(self.output_name_list) else f"output_{i}",
                    "scores": variable_scores.tolist(),
                    "statuses": variable_statuses,
                    "critical_count": variable_statuses.count("critical"),
                    "warning_count": variable_statuses.count("warning"),
                    "normal_count": variable_statuses.count("normal")
                })
            
            # 更新数据库
            await self.update_sequence_tables(
                pred_time=current_time,
                predictions=health_predictions
            )
            
            # 生成预警信息
            alerts = []
            for classification in health_classifications:
                if classification["critical_count"] > 0:
                    alerts.append({
                        "level": "critical",
                        "variable": classification["variable"],
                        "message": f"检测到 {classification['critical_count']} 个关键健康状态点"
                    })
                elif classification["warning_count"] > self.pred_len * 0.3:  # 超过30%的预测点为警告状态
                    alerts.append({
                        "level": "warning",
                        "variable": classification["variable"],
                        "message": f"检测到 {classification['warning_count']} 个警告健康状态点"
                    })
            
            # 返回完整结果
            result = {
                "prediction_time": current_time,
                "health_classifications": health_classifications,
                "anomaly_analysis": anomaly_analysis,
                "alerts": alerts,
                "overall_health": "critical" if any(alert["level"] == "critical" for alert in alerts) else 
                                 "warning" if any(alert["level"] == "warning" for alert in alerts) else "normal",
                "model_info": {
                    "algorithm": self.algorithm_name,
                    "seq_len": self.seq_len,
                    "pred_len": self.pred_len,
                    "prediction_horizon_minutes": self.pred_len
                }
            }
            
            self.logger.info(f"设备健康预测完成: {current_time}, 整体状态: {result['overall_health']}")
            return result
            
        except Exception as e:
            self.logger.error(f"设备健康预测失败: {e}")
            return None
        
        finally:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

# 使用示例
async def equipment_health_monitoring():
    """设备健康监控示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "equipment-db-server",
            "database": "EquipmentDB",
            "username": "equipment_user",
            "password": "equipment_password"
        },
        "timeseries": {
            "server": "timeseries-server",
            "database": "EquipmentTimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-server",
            "database": "EquipmentPredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建设备健康预测器
    predictor = EquipmentHealthPredictor(dbconfig)
    
    try:
        # 初始化
        print("正在初始化设备健康预测器...")
        await predictor.initialize()
        await predictor.get_basic_info()
        
        print(f"预测器配置:")
        print(f"  项目: {predictor.project_name}")
        print(f"  监控频率: {predictor.freq}")
        print(f"  历史数据长度: {predictor.seq_len} 分钟")
        print(f"  预测长度: {predictor.pred_len} 分钟")
        print(f"  监控变量: {predictor.input_name_list}")
        print(f"  健康指标: {predictor.output_name_list}")
        
        # 持续监控循环
        monitoring_count = 0
        max_monitoring_cycles = 10  # 示例中只运行10个周期
        
        while monitoring_count < max_monitoring_cycles:
            print(f"\n=== 监控周期 {monitoring_count + 1} ===")
            
            # 执行健康预测
            result = await predictor.equipment_health_main()
            
            if result:
                print(f"预测时间: {result['prediction_time']}")
                print(f"整体健康状态: {result['overall_health']}")
                
                # 显示预警信息
                if result['alerts']:
                    print("\n⚠️  预警信息:")
                    for alert in result['alerts']:
                        print(f"  [{alert['level'].upper()}] {alert['variable']}: {alert['message']}")
                else:
                    print("✅ 无预警信息")
                
                # 显示异常检测结果
                anomaly = result['anomaly_analysis']
                print(f"\n异常检测: {anomaly['overall_stability']} (检测到 {anomaly['anomaly_count']} 个异常)")
                
                # 显示健康分类统计
                print("\n健康状态统计:")
                for classification in result['health_classifications']:
                    var_name = classification['variable']
                    normal = classification['normal_count']
                    warning = classification['warning_count']
                    critical = classification['critical_count']
                    print(f"  {var_name}: 正常={normal}, 警告={warning}, 关键={critical}")
            else:
                print("健康预测失败")
            
            monitoring_count += 1
            
            # 等待下一个监控周期(示例中等待30秒)
            if monitoring_count < max_monitoring_cycles:
                print("等待下一个监控周期...")
                await asyncio.sleep(30)
        
        print("\n设备健康监控完成!")
        
    except Exception as e:
        print(f"设备健康监控过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await predictor.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(equipment_health_monitoring())
```

## 🔍 最佳实践

### 1. 时间序列数据质量管理

```python
import pandas as pd
import numpy as np
from typing import Tuple, Optional

class DataQualityManager:
    """时间序列数据质量管理器"""
    
    def __init__(self, missing_threshold: float = 0.1, outlier_std_threshold: float = 3.0):
        self.missing_threshold = missing_threshold
        self.outlier_std_threshold = outlier_std_threshold
    
    def check_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """检查数据质量"""
        quality_report = {
            "total_records": len(data),
            "missing_data": {},
            "outliers": {},
            "data_gaps": [],
            "overall_quality": "good"
        }
        
        # 检查缺失数据
        for column in data.columns:
            missing_count = data[column].isnull().sum()
            missing_ratio = missing_count / len(data)
            quality_report["missing_data"][column] = {
                "count": missing_count,
                "ratio": missing_ratio
            }
            
            if missing_ratio > self.missing_threshold:
                quality_report["overall_quality"] = "poor"
        
        # 检查异常值
        for column in data.select_dtypes(include=[np.number]).columns:
            mean_val = data[column].mean()
            std_val = data[column].std()
            outliers = data[
                (data[column] > mean_val + self.outlier_std_threshold * std_val) |
                (data[column] < mean_val - self.outlier_std_threshold * std_val)
            ]
            
            quality_report["outliers"][column] = {
                "count": len(outliers),
                "ratio": len(outliers) / len(data)
            }
        
        return quality_report
    
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理数据"""
        cleaned_data = data.copy()
        
        # 处理缺失值(前向填充 + 后向填充)
        cleaned_data = cleaned_data.fillna(method='ffill').fillna(method='bfill')
        
        # 处理异常值(使用中位数替换)
        for column in cleaned_data.select_dtypes(include=[np.number]).columns:
            Q1 = cleaned_data[column].quantile(0.25)
            Q3 = cleaned_data[column].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            median_val = cleaned_data[column].median()
            cleaned_data[column] = cleaned_data[column].apply(
                lambda x: median_val if x < lower_bound or x > upper_bound else x
            )
        
        return cleaned_data

# 在预测器中集成数据质量管理
class QualityAwareTimeSeriesPredictor(AsyncTimeSeriesRealtimePredictor):
    """具备数据质量感知的时间序列预测器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_quality_manager = DataQualityManager()
    
    async def get_quality_checked_input(self, pred_time: datetime.datetime) -> Tuple[Optional[torch.Tensor], Optional[torch.Tensor], Dict[str, Any]]:
        """获取质量检查后的输入数据"""
        # 获取原始数据
        encoder_input, decoder_input = await self.get_sequence_model_input(pred_time)
        
        if encoder_input is None or decoder_input is None:
            return None, None, {"quality": "no_data"}
        
        # 转换为DataFrame进行质量检查
        encoder_df = pd.DataFrame(encoder_input.squeeze().cpu().numpy())
        quality_report = self.data_quality_manager.check_data_quality(encoder_df)
        
        # 如果数据质量差,进行清理
        if quality_report["overall_quality"] == "poor":
            self.logger.warning("检测到数据质量问题,正在清理数据...")
            cleaned_df = self.data_quality_manager.clean_data(encoder_df)
            encoder_input = torch.tensor(cleaned_df.values, dtype=torch.float32).unsqueeze(0)
        
        return encoder_input, decoder_input, quality_report
```

### 2. 模型性能监控

```python
import time
from collections import deque
from typing import Dict, Any, List

class ModelPerformanceMonitor:
    """模型性能监控器"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.prediction_times = deque(maxlen=window_size)
        self.prediction_errors = deque(maxlen=window_size)
        self.model_accuracies = deque(maxlen=window_size)
    
    def record_prediction(self, execution_time: float, error: Optional[float] = None, accuracy: Optional[float] = None):
        """记录预测性能"""
        self.prediction_times.append(execution_time)
        
        if error is not None:
            self.prediction_errors.append(error)
        
        if accuracy is not None:
            self.model_accuracies.append(accuracy)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        metrics = {
            "avg_execution_time": np.mean(self.prediction_times) if self.prediction_times else 0,
            "max_execution_time": np.max(self.prediction_times) if self.prediction_times else 0,
            "min_execution_time": np.min(self.prediction_times) if self.prediction_times else 0,
            "total_predictions": len(self.prediction_times)
        }
        
        if self.prediction_errors:
            metrics.update({
                "avg_error": np.mean(self.prediction_errors),
                "max_error": np.max(self.prediction_errors),
                "min_error": np.min(self.prediction_errors)
            })
        
        if self.model_accuracies:
            metrics.update({
                "avg_accuracy": np.mean(self.model_accuracies),
                "accuracy_trend": "improving" if len(self.model_accuracies) > 10 and 
                                 np.mean(list(self.model_accuracies)[-5:]) > np.mean(list(self.model_accuracies)[:5]) else "stable"
            })
        
        return metrics
    
    def should_alert(self) -> Tuple[bool, List[str]]:
        """检查是否需要告警"""
        alerts = []
        should_alert = False
        
        if self.prediction_times:
            avg_time = np.mean(self.prediction_times)
            if avg_time > 10.0:  # 平均执行时间超过10秒
                alerts.append(f"平均执行时间过长: {avg_time:.2f}秒")
                should_alert = True
        
        if self.prediction_errors:
            recent_errors = list(self.prediction_errors)[-10:]  # 最近10次的错误
            if len(recent_errors) >= 5 and np.mean(recent_errors) > 0.1:  # 最近错误率过高
                alerts.append(f"最近预测错误率过高: {np.mean(recent_errors):.3f}")
                should_alert = True
        
        return should_alert, alerts

# 集成性能监控的预测器
class MonitoredTimeSeriesPredictor(AsyncTimeSeriesRealtimePredictor):
    """带性能监控的时间序列预测器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.performance_monitor = ModelPerformanceMonitor()
    
    async def monitored_main(self) -> Optional[Dict[str, Any]]:
        """带性能监控的主预测方法"""
        start_time = time.time()
        
        try:
            # 执行预测
            result = await self.main()
            
            # 记录成功的预测
            execution_time = time.time() - start_time
            self.performance_monitor.record_prediction(execution_time)
            
            # 检查是否需要告警
            should_alert, alerts = self.performance_monitor.should_alert()
            if should_alert:
                for alert in alerts:
                    self.logger.warning(f"性能告警: {alert}")
            
            return result
            
        except Exception as e:
            # 记录失败的预测
            execution_time = time.time() - start_time
            self.performance_monitor.record_prediction(execution_time, error=1.0)
            
            self.logger.error(f"预测失败: {e}")
            raise
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return self.performance_monitor.get_performance_metrics()
```

### 3. 配置管理和部署

```python
import yaml
from pathlib import Path
from typing import Dict, Any

class TimeSeriesConfig:
    """时间序列预测配置管理"""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            if self.config_path.suffix.lower() == '.yaml' or self.config_path.suffix.lower() == '.yml':
                return yaml.safe_load(f)
            elif self.config_path.suffix.lower() == '.json':
                import json
                return json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {self.config_path.suffix}")
    
    def get_predictor_config(self) -> Dict[str, Any]:
        """获取预测器配置"""
        return self.config.get('predictor', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config.get('database', {})
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.config.get('model', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get('monitoring', {})

# 配置文件示例 (time_series_config.yaml)
"""
predictor:
  project_name: "industrial_time_series_forecast"
  local_test_mode: false
  freq: "T"  # 分钟级
  seq_len: 144  # 2.4小时历史数据
  label_len: 72  # 1.2小时标签
  pred_len: 96   # 1.6小时预测

database:
  web:
    server: "192.168.1.100"
    database: "IndustryWeb"
    username: "ts_user"
    password: "ts_password"
  timeseries:
    server: "192.168.1.101"
    database: "TimeSeriesDB"
    username: "ts_user"
    password: "ts_password"
  realtimepredict:
    server: "192.168.1.102"
    database: "RealtimePredictDB"
    username: "predict_user"
    password: "predict_password"

model:
  hot_reload_check_interval: 10  # 每10次预测检查一次模型文件
  gpu_memory_cleanup: true
  prediction_timeout: 30  # 预测超时时间(秒)

monitoring:
  performance_window_size: 100
  alert_thresholds:
    max_execution_time: 10.0
    max_error_rate: 0.1
  log_level: "INFO"
"""

# 使用配置的预测器
class ConfigurableTimeSeriesPredictor(AsyncTimeSeriesRealtimePredictor):
    """可配置的时间序列预测器"""
    
    @classmethod
    def from_config(cls, config_path: str) -> 'ConfigurableTimeSeriesPredictor':
        """从配置文件创建预测器"""
        config_manager = TimeSeriesConfig(config_path)
        
        predictor_config = config_manager.get_predictor_config()
        database_config = config_manager.get_database_config()
        
        return cls(
            project_name=predictor_config['project_name'],
            dbconfig=database_config,
            local_test_mode=predictor_config.get('local_test_mode', False),
            freq=predictor_config.get('freq', 'T'),
            seq_len=predictor_config.get('seq_len', 96),
            label_len=predictor_config.get('label_len', 48),
            pred_len=predictor_config.get('pred_len', 96)
        )

# 使用示例
async def config_based_prediction():
    """基于配置文件的预测"""
    predictor = ConfigurableTimeSeriesPredictor.from_config("time_series_config.yaml")
    
    try:
        await predictor.initialize()
        await predictor.get_basic_info()
        await predictor.main()
    finally:
        await predictor.cleanup()
```

## 🚨 注意事项

### 1. 时间序列数据特殊性

- **时间对齐**: 确保所有输入变量的时间戳对齐
- **数据连续性**: 避免时间序列中的数据缺口
- **季节性处理**: 考虑数据的周期性和季节性特征
- **时间特征**: 合理使用时间戳、星期、月份等时间特征

### 2. 模型架构考虑

- **序列长度**: 根据业务需求和计算资源平衡序列长度
- **预测长度**: 预测长度不宜过长,避免误差累积
- **特征工程**: 充分利用时间特征和滞后特征
- **多变量关系**: 考虑变量间的相互依赖关系

### 3. 性能优化

- **批处理**: 考虑批量处理多个时间序列
- **模型压缩**: 使用模型量化或剪枝技术
- **缓存策略**: 缓存频繁访问的历史数据
- **并行计算**: 利用多GPU或分布式计算

### 4. 监控和维护

- **预测精度监控**: 持续监控预测精度变化
- **数据漂移检测**: 检测输入数据分布的变化
- **模型退化**: 定期评估模型性能是否退化
- **自动重训练**: 实现模型的自动重训练机制

## 🔗 相关文档

- [异步基础预测器文档](./async_basic_predictor.md)
- [异步经典预测器文档](./async_classic_predictor.md)
- [异步预测器使用示例](./usage_examples.md)