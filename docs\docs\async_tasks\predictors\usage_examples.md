# 异步预测器使用示例

本文档提供了 `async_predictor_agents` 模块中各种异步预测器的详细使用示例,涵盖从基础使用到高级应用场景的完整代码示例。

## 📋 目录



## 🚀 基础使用示例

### 快速开始

```python
import asyncio
import datetime
from typing import Dict, Any
from industrytslib.core_aysnc.async_predictor_agents import (
    AsyncClassicRealtimePredictor,
    AsyncTimeSeriesRealtimePredictor
)

# 数据库配置
dbconfig = {
    "web": {
        "server": "localhost",
        "database": "IndustryWeb",
        "username": "predict_user",
        "password": "secure_password"
    },
    "timeseries": {
        "server": "localhost",
        "database": "TimeSeriesDB",
        "username": "ts_user",
        "password": "ts_password"
    },
    "realtimepredict": {
        "server": "localhost",
        "database": "RealtimePredictDB",
        "username": "rt_user",
        "password": "rt_password"
    }
}

async def quick_start_example():
    """快速开始示例"""
    
    # 创建经典预测器
    classic_predictor = AsyncClassicRealtimePredictor(
        project_name="quick_start_classic",
        dbconfig=dbconfig
    )
    
    try:
        # 初始化和配置
        await classic_predictor.initialize()
        await classic_predictor.get_basic_info()
        
        print(f"经典预测器初始化完成:")
        print(f"  项目: {classic_predictor.project_name}")
        print(f"  算法: {classic_predictor.algorithm_name}")
        print(f"  输入变量数: {len(classic_predictor.input_name_list)}")
        print(f"  输出变量数: {len(classic_predictor.output_name_list)}")
        
        # 执行单次预测
        await classic_predictor.main()
        print("经典预测完成!")
        
    finally:
        await classic_predictor.cleanup()
    
    # 创建时间序列预测器
    ts_predictor = AsyncTimeSeriesRealtimePredictor(
        project_name="quick_start_timeseries",
        dbconfig=dbconfig,
        freq="T",
        seq_len=96,
        pred_len=48
    )
    
    try:
        # 初始化和配置
        await ts_predictor.initialize()
        await ts_predictor.get_basic_info()
        
        print(f"\n时间序列预测器初始化完成:")
        print(f"  项目: {ts_predictor.project_name}")
        print(f"  时间频率: {ts_predictor.freq}")
        print(f"  序列长度: {ts_predictor.seq_len}")
        print(f"  预测长度: {ts_predictor.pred_len}")
        
        # 执行单次预测
        await ts_predictor.main()
        print("时间序列预测完成!")
        
    finally:
        await ts_predictor.cleanup()

if __name__ == "__main__":
    asyncio.run(quick_start_example())
```

### 本地测试模式

```python
import asyncio
import pandas as pd
import numpy as np
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

async def local_test_example():
    """本地测试模式示例"""
    
    # 创建本地测试预测器(不需要数据库连接)
    predictor = AsyncClassicRealtimePredictor(
        project_name="local_test_project",
        dbconfig=None,  # 本地测试不需要数据库配置
        local_test_mode=True
    )
    
    try:
        # 初始化
        await predictor.initialize()
        
        # 手动设置测试配置
        predictor.input_name_list = ["temperature", "pressure", "flow_rate", "concentration"]
        predictor.output_name_list = ["quality_index", "yield_rate"]
        predictor.pred_length = 10
        
        print("本地测试配置:")
        print(f"  输入变量: {predictor.input_name_list}")
        print(f"  输出变量: {predictor.output_name_list}")
        print(f"  预测长度: {predictor.pred_length}")
        
        # 生成测试数据
        test_input = predictor.generate_local_input()
        print(f"\n生成测试数据:")
        print(f"  数据形状: {test_input.shape}")
        print(f"  数据预览:")
        print(test_input.head())
        
        # 模拟数据处理
        current_time = pd.Timestamp.now().replace(second=0, microsecond=0)
        processed_input = await predictor.rt_input_process(test_input, current_time)
        
        if processed_input is not None:
            print(f"\n处理后数据:")
            print(f"  形状: {processed_input.shape}")
            print(f"  统计: 均值={processed_input.mean():.3f}, 标准差={processed_input.std():.3f}")
        
        print("\n本地测试完成!")
        
    except Exception as e:
        print(f"本地测试失败: {e}")
        
    finally:
        await predictor.cleanup()

if __name__ == "__main__":
    asyncio.run(local_test_example())
```

## 🔧 经典预测器示例

### 钢铁质量预测

```python
import asyncio
import datetime
import logging
from typing import Dict, Any, Optional
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

class SteelQualityPredictor(AsyncClassicRealtimePredictor):
    """钢铁质量预测器"""
    
    def __init__(self, dbconfig: Dict[str, Any]):
        super().__init__(
            project_name="steel_quality_prediction",
            dbconfig=dbconfig,
            local_test_mode=False
        )
        
        # 钢铁质量预测专有配置
        self.quality_thresholds = {
            "excellent": 0.95,
            "good": 0.85,
            "acceptable": 0.75,
            "poor": 0.60
        }
        self.temperature_range = (1400, 1600)  # 正常温度范围(摄氏度)
        self.pressure_range = (2.0, 4.0)       # 正常压力范围(MPa)
    
    def classify_quality(self, quality_score: float) -> str:
        """质量分类"""
        if quality_score >= self.quality_thresholds["excellent"]:
            return "excellent"
        elif quality_score >= self.quality_thresholds["good"]:
            return "good"
        elif quality_score >= self.quality_thresholds["acceptable"]:
            return "acceptable"
        elif quality_score >= self.quality_thresholds["poor"]:
            return "poor"
        else:
            return "defective"
    
    def check_process_parameters(self, input_data: pd.DataFrame) -> Dict[str, Any]:
        """检查工艺参数"""
        alerts = []
        
        # 检查温度
        if "temperature" in input_data.columns:
            temp_values = input_data["temperature"].dropna()
            if not temp_values.empty:
                avg_temp = temp_values.mean()
                if avg_temp < self.temperature_range[0]:
                    alerts.append(f"温度过低: {avg_temp:.1f}°C (正常范围: {self.temperature_range[0]}-{self.temperature_range[1]}°C)")
                elif avg_temp > self.temperature_range[1]:
                    alerts.append(f"温度过高: {avg_temp:.1f}°C (正常范围: {self.temperature_range[0]}-{self.temperature_range[1]}°C)")
        
        # 检查压力
        if "pressure" in input_data.columns:
            pressure_values = input_data["pressure"].dropna()
            if not pressure_values.empty:
                avg_pressure = pressure_values.mean()
                if avg_pressure < self.pressure_range[0]:
                    alerts.append(f"压力过低: {avg_pressure:.2f}MPa (正常范围: {self.pressure_range[0]}-{self.pressure_range[1]}MPa)")
                elif avg_pressure > self.pressure_range[1]:
                    alerts.append(f"压力过高: {avg_pressure:.2f}MPa (正常范围: {self.pressure_range[0]}-{self.pressure_range[1]}MPa)")
        
        return {
            "alerts": alerts,
            "status": "normal" if not alerts else "warning"
        }
    
    async def enhanced_prediction(self) -> Optional[Dict[str, Any]]:
        """增强的预测流程"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        try:
            # 确保基本信息已初始化
            if not hasattr(self, 'model') or self.model is None:
                await self.get_basic_info()
            
            # 检查模型重载
            if await self.should_reload_model():
                self.logger.warning("检测到模型需要重载...")
                await self.get_basic_info()
                self.logger.warning("模型重载完成")
            
            # 获取输入数据
            input_data = await self.get_pred_input(current_time)
            if input_data is None:
                self.logger.error("无法获取输入数据")
                return None
            
            # 检查工艺参数
            parameter_check = self.check_process_parameters(input_data)
            if parameter_check["alerts"]:
                for alert in parameter_check["alerts"]:
                    self.logger.warning(f"工艺参数告警: {alert}")
            
            # 执行标准预测流程
            await self.main()
            
            # 模拟获取预测结果(实际应从数据库读取)
            # 这里为了示例,我们模拟一些预测结果
            predicted_quality = 0.87  # 模拟质量分数
            predicted_yield = 0.92    # 模拟产量
            
            # 质量分类
            quality_class = self.classify_quality(predicted_quality)
            
            # 构建结果
            result = {
                "prediction_time": current_time,
                "quality_score": predicted_quality,
                "quality_class": quality_class,
                "yield_rate": predicted_yield,
                "parameter_check": parameter_check,
                "recommendations": self._generate_recommendations(quality_class, parameter_check)
            }
            
            self.logger.info(f"钢铁质量预测完成: 质量={quality_class}, 分数={predicted_quality:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"钢铁质量预测失败: {e}")
            return None
    
    def _generate_recommendations(self, quality_class: str, parameter_check: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if quality_class in ["poor", "defective"]:
            recommendations.append("建议检查原料质量和配比")
            recommendations.append("建议优化冶炼工艺参数")
        
        if parameter_check["status"] == "warning":
            recommendations.append("建议调整工艺参数到正常范围")
            recommendations.append("建议检查设备运行状态")
        
        if quality_class == "acceptable":
            recommendations.append("建议微调工艺参数以提高质量")
        
        return recommendations

# 使用示例
async def steel_quality_prediction_example():
    """钢铁质量预测示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "steel-db-server",
            "database": "SteelProductionDB",
            "username": "steel_user",
            "password": "steel_password"
        },
        "timeseries": {
            "server": "timeseries-server",
            "database": "SteelTimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-server",
            "database": "SteelPredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建钢铁质量预测器
    predictor = SteelQualityPredictor(dbconfig)
    
    try:
        # 初始化
        print("正在初始化钢铁质量预测器...")
        await predictor.initialize()
        await predictor.get_basic_info()
        
        print(f"预测器配置:")
        print(f"  项目: {predictor.project_name}")
        print(f"  算法: {predictor.algorithm_name}")
        print(f"  输入变量: {predictor.input_name_list}")
        print(f"  输出变量: {predictor.output_name_list}")
        
        # 执行增强预测
        print("\n开始执行钢铁质量预测...")
        result = await predictor.enhanced_prediction()
        
        if result:
            print("\n=== 预测结果 ===")
            print(f"预测时间: {result['prediction_time']}")
            print(f"质量分数: {result['quality_score']:.3f}")
            print(f"质量等级: {result['quality_class']}")
            print(f"产量率: {result['yield_rate']:.3f}")
            
            # 显示工艺参数检查结果
            param_check = result['parameter_check']
            print(f"\n=== 工艺参数检查 ===")
            print(f"状态: {param_check['status']}")
            if param_check['alerts']:
                print("告警信息:")
                for alert in param_check['alerts']:
                    print(f"  - {alert}")
            else:
                print("无告警信息")
            
            # 显示改进建议
            if result['recommendations']:
                print("\n=== 改进建议 ===")
                for i, rec in enumerate(result['recommendations'], 1):
                    print(f"  {i}. {rec}")
            
            print("\n钢铁质量预测完成!")
        else:
            print("钢铁质量预测失败")
        
    except Exception as e:
        print(f"钢铁质量预测过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await predictor.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(steel_quality_prediction_example())
```

### 化工过程优化预测

```python
import asyncio
import datetime
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

class ChemicalProcessOptimizer(AsyncClassicRealtimePredictor):
    """化工过程优化预测器"""
    
    def __init__(self, dbconfig: Dict[str, Any]):
        super().__init__(
            project_name="chemical_process_optimization",
            dbconfig=dbconfig,
            local_test_mode=False
        )
        
        # 化工过程专有配置
        self.optimal_ranges = {
            "temperature": (80, 120),    # 最优温度范围(摄氏度)
            "pressure": (1.5, 2.5),     # 最优压力范围(atm)
            "ph_value": (6.5, 7.5),     # 最优pH范围
            "concentration": (0.1, 0.3) # 最优浓度范围(mol/L)
        }
        
        self.safety_limits = {
            "temperature": (60, 150),    # 安全温度限制
            "pressure": (1.0, 3.0),     # 安全压力限制
            "ph_value": (5.0, 9.0),     # 安全pH限制
            "concentration": (0.05, 0.5) # 安全浓度限制
        }
        
        self.efficiency_weights = {
            "yield": 0.4,      # 产量权重
            "purity": 0.3,     # 纯度权重
            "energy": 0.2,     # 能耗权重(越低越好)
            "waste": 0.1       # 废料权重(越低越好)
        }
    
    def calculate_process_efficiency(self, predictions: Dict[str, float]) -> float:
        """计算过程效率"""
        efficiency = 0.0
        
        # 产量效率(归一化到0-1)
        yield_eff = min(predictions.get("yield", 0) / 100.0, 1.0)
        efficiency += yield_eff * self.efficiency_weights["yield"]
        
        # 纯度效率(归一化到0-1)
        purity_eff = min(predictions.get("purity", 0) / 100.0, 1.0)
        efficiency += purity_eff * self.efficiency_weights["purity"]
        
        # 能耗效率(反向,越低越好)
        energy_consumption = predictions.get("energy_consumption", 100)
        energy_eff = max(0, 1 - energy_consumption / 200.0)  # 假设200为最大能耗
        efficiency += energy_eff * self.efficiency_weights["energy"]
        
        # 废料效率(反向,越低越好)
        waste_rate = predictions.get("waste_rate", 50)
        waste_eff = max(0, 1 - waste_rate / 100.0)  # 假设100为最大废料率
        efficiency += waste_eff * self.efficiency_weights["waste"]
        
        return efficiency
    
    def check_safety_parameters(self, input_data: pd.DataFrame) -> Dict[str, Any]:
        """检查安全参数"""
        safety_alerts = []
        parameter_status = {}
        
        for param, (min_val, max_val) in self.safety_limits.items():
            if param in input_data.columns:
                values = input_data[param].dropna()
                if not values.empty:
                    avg_val = values.mean()
                    min_recorded = values.min()
                    max_recorded = values.max()
                    
                    status = "safe"
                    if min_recorded < min_val or max_recorded > max_val:
                        status = "unsafe"
                        safety_alerts.append(
                            f"{param} 超出安全范围: {min_recorded:.2f}-{max_recorded:.2f} "
                            f"(安全范围: {min_val}-{max_val})"
                        )
                    elif avg_val < min_val * 1.1 or avg_val > max_val * 0.9:
                        status = "warning"
                        safety_alerts.append(
                            f"{param} 接近安全边界: 平均值={avg_val:.2f} "
                            f"(安全范围: {min_val}-{max_val})"
                        )
                    
                    parameter_status[param] = {
                        "status": status,
                        "current_avg": avg_val,
                        "current_range": (min_recorded, max_recorded),
                        "safety_range": (min_val, max_val)
                    }
        
        return {
            "overall_safety": "safe" if not any(p["status"] == "unsafe" for p in parameter_status.values()) else "unsafe",
            "alerts": safety_alerts,
            "parameters": parameter_status
        }
    
    def generate_optimization_suggestions(self, input_data: pd.DataFrame, predictions: Dict[str, float]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 检查参数是否在最优范围内
        for param, (opt_min, opt_max) in self.optimal_ranges.items():
            if param in input_data.columns:
                values = input_data[param].dropna()
                if not values.empty:
                    avg_val = values.mean()
                    
                    if avg_val < opt_min:
                        suggestions.append(f"建议提高 {param} 至 {opt_min:.2f} 以上(当前: {avg_val:.2f})")
                    elif avg_val > opt_max:
                        suggestions.append(f"建议降低 {param} 至 {opt_max:.2f} 以下(当前: {avg_val:.2f})")
        
        # 基于预测结果的建议
        if predictions.get("yield", 0) < 80:
            suggestions.append("产量较低,建议检查反应条件和催化剂活性")
        
        if predictions.get("purity", 0) < 90:
            suggestions.append("纯度较低,建议优化分离工艺")
        
        if predictions.get("energy_consumption", 0) > 150:
            suggestions.append("能耗较高,建议优化加热和冷却系统")
        
        if predictions.get("waste_rate", 0) > 20:
            suggestions.append("废料率较高,建议优化反应条件和回收工艺")
        
        return suggestions
    
    async def optimize_process(self) -> Optional[Dict[str, Any]]:
        """过程优化主流程"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        try:
            # 确保基本信息已初始化
            if not hasattr(self, 'model') or self.model is None:
                await self.get_basic_info()
            
            # 检查模型重载
            if await self.should_reload_model():
                self.logger.warning("检测到模型需要重载...")
                await self.get_basic_info()
                self.logger.warning("模型重载完成")
            
            # 获取输入数据
            input_data = await self.get_pred_input(current_time)
            if input_data is None:
                self.logger.error("无法获取输入数据")
                return None
            
            # 安全参数检查
            safety_check = self.check_safety_parameters(input_data)
            if safety_check["overall_safety"] == "unsafe":
                self.logger.error("检测到不安全参数,停止优化")
                return {
                    "status": "unsafe",
                    "safety_check": safety_check,
                    "message": "检测到不安全参数,请立即检查工艺条件"
                }
            
            # 执行预测
            await self.main()
            
            # 模拟获取预测结果(实际应从数据库读取)
            predictions = {
                "yield": 85.2,              # 产量百分比
                "purity": 92.8,             # 纯度百分比
                "energy_consumption": 135.5, # 能耗
                "waste_rate": 15.3          # 废料率百分比
            }
            
            # 计算过程效率
            efficiency = self.calculate_process_efficiency(predictions)
            
            # 生成优化建议
            suggestions = self.generate_optimization_suggestions(input_data, predictions)
            
            # 构建结果
            result = {
                "optimization_time": current_time,
                "predictions": predictions,
                "process_efficiency": efficiency,
                "safety_check": safety_check,
                "optimization_suggestions": suggestions,
                "status": "optimized",
                "efficiency_grade": self._grade_efficiency(efficiency)
            }
            
            self.logger.info(f"化工过程优化完成: 效率={efficiency:.3f}, 等级={result['efficiency_grade']}")
            return result
            
        except Exception as e:
            self.logger.error(f"化工过程优化失败: {e}")
            return None
    
    def _grade_efficiency(self, efficiency: float) -> str:
        """效率等级评定"""
        if efficiency >= 0.9:
            return "优秀"
        elif efficiency >= 0.8:
            return "良好"
        elif efficiency >= 0.7:
            return "一般"
        elif efficiency >= 0.6:
            return "较差"
        else:
            return "差"

# 使用示例
async def chemical_process_optimization_example():
    """化工过程优化示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "chemical-db-server",
            "database": "ChemicalProcessDB",
            "username": "chem_user",
            "password": "chem_password"
        },
        "timeseries": {
            "server": "timeseries-server",
            "database": "ChemicalTimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-server",
            "database": "ChemicalPredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建化工过程优化器
    optimizer = ChemicalProcessOptimizer(dbconfig)
    
    try:
        # 初始化
        print("正在初始化化工过程优化器...")
        await optimizer.initialize()
        await optimizer.get_basic_info()
        
        print(f"优化器配置:")
        print(f"  项目: {optimizer.project_name}")
        print(f"  算法: {optimizer.algorithm_name}")
        print(f"  输入变量: {optimizer.input_name_list}")
        print(f"  输出变量: {optimizer.output_name_list}")
        
        # 执行过程优化
        print("\n开始执行化工过程优化...")
        result = await optimizer.optimize_process()
        
        if result:
            print("\n=== 优化结果 ===")
            print(f"优化时间: {result['optimization_time']}")
            print(f"状态: {result['status']}")
            
            if result['status'] == 'unsafe':
                print(f"⚠️  {result['message']}")
                safety = result['safety_check']
                if safety['alerts']:
                    print("安全告警:")
                    for alert in safety['alerts']:
                        print(f"  - {alert}")
            else:
                print(f"过程效率: {result['process_efficiency']:.3f} ({result['efficiency_grade']})")
                
                # 显示预测结果
                predictions = result['predictions']
                print("\n=== 预测结果 ===")
                print(f"产量: {predictions['yield']:.1f}%")
                print(f"纯度: {predictions['purity']:.1f}%")
                print(f"能耗: {predictions['energy_consumption']:.1f}")
                print(f"废料率: {predictions['waste_rate']:.1f}%")
                
                # 显示安全检查结果
                safety = result['safety_check']
                print(f"\n=== 安全检查 ===")
                print(f"整体安全状态: {safety['overall_safety']}")
                if safety['alerts']:
                    print("安全提醒:")
                    for alert in safety['alerts']:
                        print(f"  - {alert}")
                
                # 显示优化建议
                if result['optimization_suggestions']:
                    print("\n=== 优化建议 ===")
                    for i, suggestion in enumerate(result['optimization_suggestions'], 1):
                        print(f"  {i}. {suggestion}")
            
            print("\n化工过程优化完成!")
        else:
            print("化工过程优化失败")
        
    except Exception as e:
        print(f"化工过程优化过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await optimizer.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(chemical_process_optimization_example())
```

## 📈 时间序列预测器示例

### 能源消耗预测

```python
import asyncio
import datetime
import torch
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from industrytslib.core_aysnc.async_predictor_agents import AsyncTimeSeriesRealtimePredictor

class EnergyConsumptionForecaster(AsyncTimeSeriesRealtimePredictor):
    """能源消耗预测器"""
    
    def __init__(self, dbconfig: Dict[str, Any]):
        super().__init__(
            project_name="energy_consumption_forecast",
            dbconfig=dbconfig,
            local_test_mode=False,
            freq="H",        # 小时级预测
            seq_len=168,     # 使用过去一周的数据(168小时)
            label_len=24,    # 解码器标签长度24小时
            pred_len=48      # 预测未来48小时
        )
        
        # 能源预测专有配置
        self.peak_hours = list(range(8, 12)) + list(range(18, 22))  # 用电高峰时段
        self.valley_hours = list(range(0, 6)) + list(range(23, 24))  # 用电低谷时段
        
        self.consumption_thresholds = {
            "very_high": 1000,  # kWh
            "high": 800,
            "normal": 600,
            "low": 400,
            "very_low": 200
        }
        
        self.cost_rates = {
            "peak": 1.2,      # 峰时电价倍率
            "normal": 1.0,    # 平时电价倍率
            "valley": 0.8     # 谷时电价倍率
        }
    
    def classify_consumption_level(self, consumption: float) -> str:
        """消耗水平分类"""
        if consumption >= self.consumption_thresholds["very_high"]:
            return "very_high"
        elif consumption >= self.consumption_thresholds["high"]:
            return "high"
        elif consumption >= self.consumption_thresholds["normal"]:
            return "normal"
        elif consumption >= self.consumption_thresholds["low"]:
            return "low"
        else:
            return "very_low"
    
    def get_time_period_type(self, hour: int) -> str:
        """获取时段类型"""
        if hour in self.peak_hours:
            return "peak"
        elif hour in self.valley_hours:
            return "valley"
        else:
            return "normal"
    
    def calculate_energy_cost(self, consumption_forecast: np.ndarray, start_time: datetime.datetime) -> Dict[str, Any]:
        """计算能源成本"""
        total_cost = 0.0
        hourly_costs = []
        base_rate = 0.8  # 基础电价(元/kWh)
        
        for i, consumption in enumerate(consumption_forecast):
            current_hour = (start_time + datetime.timedelta(hours=i)).hour
            period_type = self.get_time_period_type(current_hour)
            rate_multiplier = self.cost_rates[period_type]
            
            hourly_cost = consumption * base_rate * rate_multiplier
            total_cost += hourly_cost
            
            hourly_costs.append({
                "hour": current_hour,
                "consumption": consumption,
                "period_type": period_type,
                "rate_multiplier": rate_multiplier,
                "cost": hourly_cost
            })
        
        return {
            "total_cost": total_cost,
            "average_hourly_cost": total_cost / len(consumption_forecast),
            "hourly_breakdown": hourly_costs,
            "peak_hours_cost": sum(h["cost"] for h in hourly_costs if h["period_type"] == "peak"),
            "valley_hours_cost": sum(h["cost"] for h in hourly_costs if h["period_type"] == "valley")
        }
    
    def analyze_consumption_pattern(self, historical_data: np.ndarray) -> Dict[str, Any]:
        """分析消耗模式"""
        # 基础统计
        mean_consumption = np.mean(historical_data)
        std_consumption = np.std(historical_data)
        max_consumption = np.max(historical_data)
        min_consumption = np.min(historical_data)
        
        # 趋势分析(简单线性趋势)
        x = np.arange(len(historical_data))
        trend_coef = np.polyfit(x, historical_data, 1)[0]
        trend_direction = "increasing" if trend_coef > 0.1 else "decreasing" if trend_coef < -0.1 else "stable"
        
        # 波动性分析
        volatility = std_consumption / mean_consumption if mean_consumption > 0 else 0
        volatility_level = "high" if volatility > 0.3 else "medium" if volatility > 0.15 else "low"
        
        # 异常检测(基于3-sigma规则)
        threshold_upper = mean_consumption + 3 * std_consumption
        threshold_lower = mean_consumption - 3 * std_consumption
        anomalies = np.where((historical_data > threshold_upper) | (historical_data < threshold_lower))[0]
        
        return {
            "statistics": {
                "mean": mean_consumption,
                "std": std_consumption,
                "max": max_consumption,
                "min": min_consumption,
                "range": max_consumption - min_consumption
            },
            "trend": {
                "direction": trend_direction,
                "coefficient": trend_coef
            },
            "volatility": {
                "level": volatility_level,
                "value": volatility
            },
            "anomalies": {
                "count": len(anomalies),
                "indices": anomalies.tolist(),
                "threshold_upper": threshold_upper,
                "threshold_lower": threshold_lower
            }
        }
    
    def generate_energy_recommendations(self, forecast_analysis: Dict[str, Any], cost_analysis: Dict[str, Any]) -> List[str]:
        """生成能源管理建议"""
        recommendations = []
        
        # 基于成本的建议
        peak_cost_ratio = cost_analysis["peak_hours_cost"] / cost_analysis["total_cost"]
        if peak_cost_ratio > 0.4:
            recommendations.append("峰时用电成本占比较高,建议将部分用电负荷转移到谷时")
        
        # 基于消耗水平的建议
        high_consumption_hours = sum(1 for h in cost_analysis["hourly_breakdown"] 
                                   if self.classify_consumption_level(h["consumption"]) in ["high", "very_high"])
        if high_consumption_hours > len(cost_analysis["hourly_breakdown"]) * 0.3:
            recommendations.append("高耗能时段较多,建议检查设备运行效率")
        
        # 基于趋势的建议
        if forecast_analysis["trend"]["direction"] == "increasing":
            recommendations.append("能耗呈上升趋势,建议制定节能措施")
        
        # 基于波动性的建议
        if forecast_analysis["volatility"]["level"] == "high":
            recommendations.append("能耗波动较大,建议优化负荷管理")
        
        # 基于异常的建议
        if forecast_analysis["anomalies"]["count"] > 0:
            recommendations.append(f"检测到 {forecast_analysis['anomalies']['count']} 个异常消耗点,建议检查相关时段的设备状态")
        
        return recommendations
    
    async def forecast_energy_consumption(self) -> Optional[Dict[str, Any]]:
        """能源消耗预测主流程"""
        current_time = datetime.datetime.now().replace(second=0, microsecond=0)
        
        try:
            # 确保基本信息已初始化
            if not hasattr(self, 'model') or self.model is None:
                await self.get_basic_info()
            
            # 检查模型重载
            if await self.should_reload_model():
                self.logger.warning("检测到模型需要重载...")
                await self.get_basic_info()
                self.logger.warning("模型重载完成")
            
            # 获取序列输入
            encoder_input, decoder_input = await self.get_sequence_model_input(current_time)
            
            if encoder_input is None or decoder_input is None:
                self.logger.error("无法获取序列输入数据")
                return None
            
            # 分析历史消耗模式
            historical_data = encoder_input.squeeze().cpu().numpy()
            if historical_data.ndim > 1:
                historical_data = historical_data[:, 0]  # 取第一个变量作为主要能耗指标
            
            pattern_analysis = self.analyze_consumption_pattern(historical_data)
            self.logger.info(f"历史消耗模式分析: 趋势={pattern_analysis['trend']['direction']}, 波动性={pattern_analysis['volatility']['level']}")
            
            # 执行预测
            with torch.no_grad():
                self.model.eval()
                predictions = self.model(encoder_input, decoder_input)
            
            # 反归一化
            if self.scaler_y is not None:
                predictions_np = predictions.cpu().numpy()
                predictions_denorm = self.scaler_y.inverse_transform(
                    predictions_np.reshape(-1, predictions_np.shape[-1])
                ).reshape(predictions_np.shape)
                predictions = torch.from_numpy(predictions_denorm)
            
            # 提取能耗预测(假设第一个输出变量是主要能耗)
            consumption_forecast = predictions.squeeze().cpu().numpy()
            if consumption_forecast.ndim > 1:
                consumption_forecast = consumption_forecast[:, 0]
            
            # 成本分析
            cost_analysis = self.calculate_energy_cost(consumption_forecast, current_time)
            
            # 消耗水平分类
            consumption_levels = [self.classify_consumption_level(c) for c in consumption_forecast]
            level_distribution = {level: consumption_levels.count(level) for level in set(consumption_levels)}
            
            # 生成建议
            recommendations = self.generate_energy_recommendations(pattern_analysis, cost_analysis)
            
            # 更新数据库
            await self.update_sequence_tables(
                pred_time=current_time,
                predictions=predictions
            )
            
            # 构建结果
            result = {
                "forecast_time": current_time,
                "forecast_horizon_hours": self.pred_len,
                "consumption_forecast": consumption_forecast.tolist(),
                "consumption_statistics": {
                    "total": float(np.sum(consumption_forecast)),
                    "average": float(np.mean(consumption_forecast)),
                    "peak": float(np.max(consumption_forecast)),
                    "valley": float(np.min(consumption_forecast))
                },
                "consumption_level_distribution": level_distribution,
                "cost_analysis": cost_analysis,
                "pattern_analysis": pattern_analysis,
                "recommendations": recommendations,
                "forecast_quality": self._assess_forecast_quality(pattern_analysis)
            }
            
            self.logger.info(f"能源消耗预测完成: 总预测消耗={result['consumption_statistics']['total']:.1f}kWh, 总成本={cost_analysis['total_cost']:.2f}元")
            return result
            
        except Exception as e:
            self.logger.error(f"能源消耗预测失败: {e}")
            return None
        
        finally:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    def _assess_forecast_quality(self, pattern_analysis: Dict[str, Any]) -> str:
        """评估预测质量"""
        volatility_level = pattern_analysis["volatility"]["level"]
        anomaly_count = pattern_analysis["anomalies"]["count"]
        
        if volatility_level == "low" and anomaly_count == 0:
            return "high"
        elif volatility_level == "medium" and anomaly_count <= 2:
            return "medium"
        else:
            return "low"

# 使用示例
async def energy_consumption_forecast_example():
    """能源消耗预测示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "energy-db-server",
            "database": "EnergyManagementDB",
            "username": "energy_user",
            "password": "energy_password"
        },
        "timeseries": {
            "server": "timeseries-server",
            "database": "EnergyTimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-server",
            "database": "EnergyPredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建能源消耗预测器
    forecaster = EnergyConsumptionForecaster(dbconfig)
    
    try:
        # 初始化
        print("正在初始化能源消耗预测器...")
        await forecaster.initialize()
        await forecaster.get_basic_info()
        
        print(f"预测器配置:")
        print(f"  项目: {forecaster.project_name}")
        print(f"  时间频率: {forecaster.freq}")
        print(f"  历史数据长度: {forecaster.seq_len} 小时")
        print(f"  预测长度: {forecaster.pred_len} 小时")
        print(f"  输入变量: {forecaster.input_name_list}")
        print(f"  输出变量: {forecaster.output_name_list}")
        
        # 执行能源消耗预测
        print("\n开始执行能源消耗预测...")
        result = await forecaster.forecast_energy_consumption()
        
        if result:
            print("\n=== 预测结果 ===")
            print(f"预测时间: {result['forecast_time']}")
            print(f"预测时长: {result['forecast_horizon_hours']} 小时")
            print(f"预测质量: {result['forecast_quality']}")
            
            # 显示消耗统计
            stats = result['consumption_statistics']
            print(f"\n=== 消耗统计 ===")
            print(f"总消耗: {stats['total']:.1f} kWh")
            print(f"平均消耗: {stats['average']:.1f} kWh/h")
            print(f"峰值消耗: {stats['peak']:.1f} kWh/h")
            print(f"谷值消耗: {stats['valley']:.1f} kWh/h")
            
            # 显示消耗水平分布
            print(f"\n=== 消耗水平分布 ===")
            for level, count in result['consumption_level_distribution'].items():
                print(f"  {level}: {count} 小时")
            
            # 显示成本分析
            cost = result['cost_analysis']
            print(f"\n=== 成本分析 ===")
            print(f"总成本: {cost['total_cost']:.2f} 元")
            print(f"平均小时成本: {cost['average_hourly_cost']:.2f} 元/h")
            print(f"峰时成本: {cost['peak_hours_cost']:.2f} 元")
            print(f"谷时成本: {cost['valley_hours_cost']:.2f} 元")
            
            # 显示模式分析
            pattern = result['pattern_analysis']
            print(f"\n=== 模式分析 ===")
            print(f"趋势: {pattern['trend']['direction']}")
            print(f"波动性: {pattern['volatility']['level']} ({pattern['volatility']['value']:.3f})")
            print(f"异常点数量: {pattern['anomalies']['count']}")
            
            # 显示建议
            if result['recommendations']:
                print(f"\n=== 能源管理建议 ===")
                for i, rec in enumerate(result['recommendations'], 1):
                    print(f"  {i}. {rec}")
            
            print("\n能源消耗预测完成!")
        else:
            print("能源消耗预测失败")
        
    except Exception as e:
        print(f"能源消耗预测过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("正在清理资源...")
        await forecaster.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(energy_consumption_forecast_example())
```

## 🏭 生产环境部署示例

### 多预测器服务管理

```python
import asyncio
import signal
import logging
import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
from industrytslib.core_aysnc.async_predictor_agents import (
    AsyncClassicRealtimePredictor,
    AsyncTimeSeriesRealtimePredictor
)

class PredictorType(Enum):
    CLASSIC = "classic"
    TIME_SERIES = "time_series"

class ServiceStatus(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

@dataclass
class PredictorConfig:
    """预测器配置"""
    name: str
    type: PredictorType
    project_name: str
    dbconfig: Dict[str, Any]
    prediction_interval: int = 60  # 预测间隔(秒)
    enabled: bool = True
    
    # 时间序列专有配置
    freq: Optional[str] = None
    seq_len: Optional[int] = None
    label_len: Optional[int] = None
    pred_len: Optional[int] = None

class PredictorService:
    """单个预测器服务"""
    
    def __init__(self, config: PredictorConfig):
        self.config = config
        self.predictor = None
        self.status = ServiceStatus.STOPPED
        self.task = None
        self.error_count = 0
        self.last_prediction_time = None
        self.total_predictions = 0
        
        # 设置日志
        self.logger = logging.getLogger(f"PredictorService.{config.name}")
    
    async def start(self) -> bool:
        """启动预测器服务"""
        if self.status != ServiceStatus.STOPPED:
            self.logger.warning(f"服务已在运行,当前状态: {self.status.value}")
            return False
        
        try:
            self.status = ServiceStatus.STARTING
            self.logger.info(f"正在启动预测器服务: {self.config.name}")
            
            # 创建预测器实例
            if self.config.type == PredictorType.CLASSIC:
                self.predictor = AsyncClassicRealtimePredictor(
                    project_name=self.config.project_name,
                    dbconfig=self.config.dbconfig,
                    local_test_mode=False
                )
            elif self.config.type == PredictorType.TIME_SERIES:
                self.predictor = AsyncTimeSeriesRealtimePredictor(
                    project_name=self.config.project_name,
                    dbconfig=self.config.dbconfig,
                    local_test_mode=False,
                    freq=self.config.freq or "T",
                    seq_len=self.config.seq_len or 96,
                    label_len=self.config.label_len or 48,
                    pred_len=self.config.pred_len or 96
                )
            else:
                raise ValueError(f"不支持的预测器类型: {self.config.type}")
            
            # 初始化预测器
            await self.predictor.initialize()
            await self.predictor.get_basic_info()
            
            # 启动预测任务
            self.task = asyncio.create_task(self._prediction_loop())
            self.status = ServiceStatus.RUNNING
            
            self.logger.info(f"预测器服务启动成功: {self.config.name}")
            return True
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"预测器服务启动失败: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止预测器服务"""
        if self.status == ServiceStatus.STOPPED:
            return True
        
        try:
            self.status = ServiceStatus.STOPPING
            self.logger.info(f"正在停止预测器服务: {self.config.name}")
            
            # 取消预测任务
            if self.task and not self.task.done():
                self.task.cancel()
                try:
                    await self.task
                except asyncio.CancelledError:
                    pass
            
            # 清理预测器资源
            if self.predictor:
                await self.predictor.cleanup()
                self.predictor = None
            
            self.status = ServiceStatus.STOPPED
            self.logger.info(f"预测器服务已停止: {self.config.name}")
            return True
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"预测器服务停止失败: {e}")
            return False
    
    async def _prediction_loop(self):
        """预测循环"""
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.status == ServiceStatus.RUNNING:
            try:
                start_time = datetime.datetime.now()
                
                # 执行预测
                await self.predictor.main()
                
                # 更新统计信息
                self.last_prediction_time = start_time
                self.total_predictions += 1
                consecutive_errors = 0
                
                # 计算执行时间
                execution_time = (datetime.datetime.now() - start_time).total_seconds()
                self.logger.debug(f"预测完成 - 执行时间: {execution_time:.2f}秒")
                
                # 等待下一个预测周期
                await asyncio.sleep(self.config.prediction_interval)
                
            except asyncio.CancelledError:
                self.logger.info("预测循环被取消")
                break
            except Exception as e:
                consecutive_errors += 1
                self.error_count += 1
                self.logger.error(f"预测错误 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                # 如果连续错误过多,停止服务
                if consecutive_errors >= max_consecutive_errors:
                    self.logger.error(f"连续错误次数过多,停止预测服务")
                    self.status = ServiceStatus.ERROR
                    break
                
                # 错误后等待较短时间再重试
                await asyncio.sleep(10)
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        return {
            "name": self.config.name,
            "type": self.config.type.value,
            "status": self.status.value,
            "enabled": self.config.enabled,
            "error_count": self.error_count,
            "total_predictions": self.total_predictions,
            "last_prediction_time": self.last_prediction_time.isoformat() if self.last_prediction_time else None,
            "prediction_interval": self.config.prediction_interval
        }

class PredictorServiceManager:
    """预测器服务管理器"""
    
    def __init__(self, configs: List[PredictorConfig]):
        self.configs = {config.name: config for config in configs}
        self.services = {}
        self.running = False
        
        # 设置日志
        self.logger = logging.getLogger("PredictorServiceManager")
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum},开始优雅关闭...")
        asyncio.create_task(self.stop_all())
    
    async def start_service(self, service_name: str) -> bool:
        """启动指定服务"""
        if service_name not in self.configs:
            self.logger.error(f"服务配置不存在: {service_name}")
            return False
        
        if service_name in self.services:
            self.logger.warning(f"服务已存在: {service_name}")
            return False
        
        config = self.configs[service_name]
        if not config.enabled:
            self.logger.info(f"服务已禁用: {service_name}")
            return False
        
        service = PredictorService(config)
        success = await service.start()
        
        if success:
            self.services[service_name] = service
            self.logger.info(f"服务启动成功: {service_name}")
        
        return success
    
    async def stop_service(self, service_name: str) -> bool:
        """停止指定服务"""
        if service_name not in self.services:
            self.logger.warning(f"服务不存在: {service_name}")
            return True
        
        service = self.services[service_name]
        success = await service.stop()
        
        if success:
            del self.services[service_name]
            self.logger.info(f"服务停止成功: {service_name}")
        
        return success
    
    async def start_all(self) -> Dict[str, bool]:
        """启动所有服务"""
        self.running = True
        results = {}
        
        self.logger.info("开始启动所有预测器服务...")
        
        for service_name in self.configs:
            results[service_name] = await self.start_service(service_name)
        
        successful_count = sum(results.values())
        total_count = len(results)
        
        self.logger.info(f"服务启动完成: {successful_count}/{total_count} 成功")
        return results
    
    async def stop_all(self) -> Dict[str, bool]:
        """停止所有服务"""
        self.running = False
        results = {}
        
        self.logger.info("开始停止所有预测器服务...")
        
        # 并发停止所有服务
        stop_tasks = []
        for service_name in list(self.services.keys()):
            task = asyncio.create_task(self.stop_service(service_name))
            stop_tasks.append((service_name, task))
        
        for service_name, task in stop_tasks:
            try:
                results[service_name] = await task
            except Exception as e:
                self.logger.error(f"停止服务失败 {service_name}: {e}")
                results[service_name] = False
        
        successful_count = sum(results.values())
        total_count = len(results)
        
        self.logger.info(f"服务停止完成: {successful_count}/{total_count} 成功")
        return results
    
    def get_all_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status_info = {
            "manager_running": self.running,
            "total_services": len(self.configs),
            "running_services": len([s for s in self.services.values() if s.status == ServiceStatus.RUNNING]),
            "services": {}
        }
        
        for name, config in self.configs.items():
            if name in self.services:
                status_info["services"][name] = self.services[name].get_status_info()
            else:
                status_info["services"][name] = {
                    "name": name,
                    "type": config.type.value,
                    "status": ServiceStatus.STOPPED.value,
                    "enabled": config.enabled
                }
        
        return status_info
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_info = {
            "overall_health": "healthy",
            "timestamp": datetime.datetime.now().isoformat(),
            "services": {}
        }
        
        unhealthy_count = 0
        
        for name, service in self.services.items():
            service_health = {
                "status": service.status.value,
                "error_count": service.error_count,
                "last_prediction": service.last_prediction_time.isoformat() if service.last_prediction_time else None,
                "health": "healthy"
            }
            
            # 检查服务健康状态
            if service.status == ServiceStatus.ERROR:
                service_health["health"] = "unhealthy"
                unhealthy_count += 1
            elif service.status != ServiceStatus.RUNNING:
                service_health["health"] = "warning"
            elif service.error_count > 10:
                service_health["health"] = "warning"
            elif service.last_prediction_time:
                # 检查最后预测时间是否过久
                time_since_last = datetime.datetime.now() - service.last_prediction_time
                if time_since_last.total_seconds() > service.config.prediction_interval * 3:
                    service_health["health"] = "warning"
            
            health_info["services"][name] = service_health
        
        # 确定整体健康状态
        if unhealthy_count > 0:
            health_info["overall_health"] = "unhealthy"
        elif any(s["health"] == "warning" for s in health_info["services"].values()):
            health_info["overall_health"] = "warning"
        
        return health_info

# 使用示例
async def production_deployment_example():
    """生产环境部署示例"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('/var/log/predictor_services.log'),
            logging.StreamHandler()
        ]
    )
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "prod-db-server",
            "database": "ProductionDB",
            "username": "prod_user",
            "password": "prod_password"
        },
        "timeseries": {
            "server": "ts-db-server",
            "database": "TimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "predict-db-server",
            "database": "PredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建服务配置
    service_configs = [
        PredictorConfig(
            name="steel_quality",
            type=PredictorType.CLASSIC,
            project_name="steel_quality_prediction",
            dbconfig=dbconfig,
            prediction_interval=300,  # 5分钟
            enabled=True
        ),
        PredictorConfig(
            name="chemical_process",
            type=PredictorType.CLASSIC,
            project_name="chemical_process_optimization",
            dbconfig=dbconfig,
            prediction_interval=180,  # 3分钟
            enabled=True
        ),
        PredictorConfig(
            name="energy_forecast",
            type=PredictorType.TIME_SERIES,
            project_name="energy_consumption_forecast",
            dbconfig=dbconfig,
            prediction_interval=3600,  # 1小时
            enabled=True,
            freq="H",
            seq_len=168,
            label_len=24,
            pred_len=48
        ),
        PredictorConfig(
            name="equipment_monitoring",
            type=PredictorType.TIME_SERIES,
            project_name="equipment_health_monitoring",
            dbconfig=dbconfig,
            prediction_interval=600,  # 10分钟
            enabled=True,
            freq="T",
            seq_len=1440,  # 24小时的分钟数据
            label_len=60,
            pred_len=120
        )
    ]
    
    # 创建服务管理器
    manager = PredictorServiceManager(service_configs)
    
    try:
        print("=== 生产环境预测器服务部署 ===")
        
        # 启动所有服务
        print("\n正在启动所有预测器服务...")
        start_results = await manager.start_all()
        
        print("\n启动结果:")
        for service_name, success in start_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {service_name}: {status}")
        
        # 显示服务状态
        print("\n=== 服务状态 ===")
        status = manager.get_all_status()
        print(f"管理器运行状态: {status['manager_running']}")
        print(f"总服务数: {status['total_services']}")
        print(f"运行中服务数: {status['running_services']}")
        
        print("\n各服务详细状态:")
        for name, service_status in status["services"].items():
            print(f"  {name}:")
            print(f"    类型: {service_status['type']}")
            print(f"    状态: {service_status['status']}")
            print(f"    启用: {service_status['enabled']}")
            if 'total_predictions' in service_status:
                print(f"    总预测次数: {service_status['total_predictions']}")
                print(f"    错误次数: {service_status['error_count']}")
                print(f"    预测间隔: {service_status['prediction_interval']}秒")
        
        # 运行一段时间进行监控
        print("\n=== 开始监控服务运行 ===")
        print("服务正在运行中,按 Ctrl+C 停止...")
        
        # 定期健康检查
        for i in range(10):  # 运行10次健康检查
            await asyncio.sleep(30)  # 每30秒检查一次
            
            health = await manager.health_check()
            print(f"\n健康检查 #{i+1} - 整体状态: {health['overall_health']}")
            
            for name, service_health in health["services"].items():
                if service_health["health"] != "healthy":
                    print(f"  ⚠️  {name}: {service_health['health']} (状态: {service_health['status']})")
        
        print("\n监控完成,开始停止服务...")
        
    except KeyboardInterrupt:
        print("\n接收到中断信号,开始优雅关闭...")
    except Exception as e:
        print(f"\n部署过程中发生错误: {e}")
    finally:
        # 停止所有服务
        print("\n正在停止所有服务...")
        stop_results = await manager.stop_all()
        
        print("\n停止结果:")
        for service_name, success in stop_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {service_name}: {status}")
        
        print("\n生产环境部署示例完成!")

if __name__ == "__main__":
    asyncio.run(production_deployment_example())
```

## 📊 监控和维护示例

### 性能监控和告警

```python
import asyncio
import time
import psutil
import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor

@dataclass
class PerformanceMetrics:
    """性能指标"""
    prediction_time: float
    memory_usage: float
    cpu_usage: float
    gpu_memory_usage: Optional[float]
    timestamp: datetime.datetime

class PredictorMonitor:
    """预测器监控器"""
    
    def __init__(self, predictor: AsyncClassicRealtimePredictor, alert_thresholds: Dict[str, float]):
        self.predictor = predictor
        self.alert_thresholds = alert_thresholds
        self.metrics_history = []
        self.alerts = []
        self.max_history_size = 1000
        
        # 性能阈值
        self.default_thresholds = {
            "max_prediction_time": 30.0,    # 最大预测时间(秒)
            "max_memory_usage": 80.0,       # 最大内存使用率(%)
            "max_cpu_usage": 90.0,          # 最大CPU使用率(%)
            "max_gpu_memory": 90.0          # 最大GPU内存使用率(%)
        }
        self.default_thresholds.update(alert_thresholds)
    
    async def monitor_prediction(self) -> PerformanceMetrics:
        """监控单次预测性能"""
        # 记录开始时间和资源使用
        start_time = time.time()
        start_memory = psutil.virtual_memory().percent
        start_cpu = psutil.cpu_percent()
        
        # GPU内存使用(如果可用)
        gpu_memory = None
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100
        except ImportError:
            pass
        
        try:
            # 执行预测
            await self.predictor.main()
            
            # 计算性能指标
            prediction_time = time.time() - start_time
            end_memory = psutil.virtual_memory().percent
            end_cpu = psutil.cpu_percent()
            
            # GPU内存使用(预测后)
            if gpu_memory is not None:
                try:
                    import torch
                    if torch.cuda.is_available():
                        gpu_memory = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100
                except ImportError:
                    gpu_memory = None
            
            metrics = PerformanceMetrics(
                prediction_time=prediction_time,
                memory_usage=max(start_memory, end_memory),
                cpu_usage=max(start_cpu, end_cpu),
                gpu_memory_usage=gpu_memory,
                timestamp=datetime.datetime.now()
            )
            
            # 添加到历史记录
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)
            
            # 检查告警
            self._check_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            # 记录错误
            error_metrics = PerformanceMetrics(
                prediction_time=time.time() - start_time,
                memory_usage=psutil.virtual_memory().percent,
                cpu_usage=psutil.cpu_percent(),
                gpu_memory_usage=gpu_memory,
                timestamp=datetime.datetime.now()
            )
            
            self.alerts.append({
                "type": "prediction_error",
                "message": f"预测执行失败: {e}",
                "timestamp": datetime.datetime.now(),
                "severity": "high"
            })
            
            raise e
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警条件"""
        current_time = datetime.datetime.now()
        
        # 预测时间告警
        if metrics.prediction_time > self.default_thresholds["max_prediction_time"]:
            self.alerts.append({
                "type": "slow_prediction",
                "message": f"预测时间过长: {metrics.prediction_time:.2f}秒 (阈值: {self.default_thresholds['max_prediction_time']}秒)",
                "timestamp": current_time,
                "severity": "medium"
            })
        
        # 内存使用告警
        if metrics.memory_usage > self.default_thresholds["max_memory_usage"]:
            self.alerts.append({
                "type": "high_memory",
                "message": f"内存使用率过高: {metrics.memory_usage:.1f}% (阈值: {self.default_thresholds['max_memory_usage']}%)",
                "timestamp": current_time,
                "severity": "medium"
            })
        
        # CPU使用告警
        if metrics.cpu_usage > self.default_thresholds["max_cpu_usage"]:
            self.alerts.append({
                "type": "high_cpu",
                "message": f"CPU使用率过高: {metrics.cpu_usage:.1f}% (阈值: {self.default_thresholds['max_cpu_usage']}%)",
                "timestamp": current_time,
                "severity": "medium"
            })
        
        # GPU内存使用告警
        if metrics.gpu_memory_usage and metrics.gpu_memory_usage > self.default_thresholds["max_gpu_memory"]:
            self.alerts.append({
                "type": "high_gpu_memory",
                "message": f"GPU内存使用率过高: {metrics.gpu_memory_usage:.1f}% (阈值: {self.default_thresholds['max_gpu_memory']}%)",
                "timestamp": current_time,
                "severity": "high"
            })
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"message": "暂无性能数据"}
        
        # 过滤指定时间范围内的数据
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {"message": f"过去{hours}小时内暂无性能数据"}
        
        # 计算统计信息
        prediction_times = [m.prediction_time for m in recent_metrics]
        memory_usages = [m.memory_usage for m in recent_metrics]
        cpu_usages = [m.cpu_usage for m in recent_metrics]
        gpu_memory_usages = [m.gpu_memory_usage for m in recent_metrics if m.gpu_memory_usage is not None]
        
        summary = {
            "time_range": f"过去{hours}小时",
            "total_predictions": len(recent_metrics),
            "prediction_time": {
                "avg": sum(prediction_times) / len(prediction_times),
                "min": min(prediction_times),
                "max": max(prediction_times),
                "p95": sorted(prediction_times)[int(len(prediction_times) * 0.95)]
            },
            "memory_usage": {
                "avg": sum(memory_usages) / len(memory_usages),
                "min": min(memory_usages),
                "max": max(memory_usages)
            },
            "cpu_usage": {
                "avg": sum(cpu_usages) / len(cpu_usages),
                "min": min(cpu_usages),
                "max": max(cpu_usages)
            }
        }
        
        if gpu_memory_usages:
            summary["gpu_memory_usage"] = {
                "avg": sum(gpu_memory_usages) / len(gpu_memory_usages),
                "min": min(gpu_memory_usages),
                "max": max(gpu_memory_usages)
            }
        
        return summary
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        return [alert for alert in self.alerts if alert["timestamp"] >= cutoff_time]
    
    def clear_old_alerts(self, hours: int = 168):  # 默认保留一周
        """清理旧告警"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        self.alerts = [alert for alert in self.alerts if alert["timestamp"] >= cutoff_time]

# 使用示例
async def monitoring_example():
    """监控示例"""
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "localhost",
            "database": "MonitoringTestDB",
            "username": "test_user",
            "password": "test_password"
        },
        "timeseries": {
            "server": "localhost",
            "database": "TimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "localhost",
            "database": "PredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建预测器
    predictor = AsyncClassicRealtimePredictor(
        project_name="monitoring_test",
        dbconfig=dbconfig,
        local_test_mode=True  # 使用本地测试模式
    )
    
    # 创建监控器
    alert_thresholds = {
        "max_prediction_time": 10.0,  # 10秒
        "max_memory_usage": 70.0,     # 70%
        "max_cpu_usage": 80.0,        # 80%
        "max_gpu_memory": 85.0        # 85%
    }
    
    monitor = PredictorMonitor(predictor, alert_thresholds)
    
    try:
        # 初始化预测器
        print("正在初始化预测器...")
        await predictor.initialize()
        
        # 手动设置测试配置(本地测试模式)
        predictor.input_name_list = ["temp", "pressure", "flow"]
        predictor.output_name_list = ["quality"]
        predictor.pred_length = 5
        
        print("=== 预测器性能监控示例 ===")
        print(f"告警阈值: {alert_thresholds}")
        
        # 执行多次监控预测
        print("\n开始执行监控预测...")
        for i in range(10):
            print(f"\n--- 第 {i+1} 次预测 ---")
            
            try:
                metrics = await monitor.monitor_prediction()
                
                print(f"预测时间: {metrics.prediction_time:.3f}秒")
                print(f"内存使用: {metrics.memory_usage:.1f}%")
                print(f"CPU使用: {metrics.cpu_usage:.1f}%")
                if metrics.gpu_memory_usage:
                    print(f"GPU内存: {metrics.gpu_memory_usage:.1f}%")
                
                # 检查是否有新告警
                recent_alerts = monitor.get_recent_alerts(hours=1)
                new_alerts = [a for a in recent_alerts if a["timestamp"] >= metrics.timestamp]
                
                if new_alerts:
                    print("⚠️  新告警:")
                    for alert in new_alerts:
                        print(f"  - {alert['type']}: {alert['message']} (严重性: {alert['severity']})")
                
            except Exception as e:
                print(f"❌ 预测失败: {e}")
            
            # 等待一段时间
            await asyncio.sleep(2)
        
        # 显示性能摘要
        print("\n=== 性能摘要 ===")
        summary = monitor.get_performance_summary(hours=1)
        
        if "message" in summary:
            print(summary["message"])
        else:
            print(f"时间范围: {summary['time_range']}")
            print(f"总预测次数: {summary['total_predictions']}")
            
            pred_time = summary['prediction_time']
            print(f"\n预测时间统计:")
            print(f"  平均: {pred_time['avg']:.3f}秒")
            print(f"  最小: {pred_time['min']:.3f}秒")
            print(f"  最大: {pred_time['max']:.3f}秒")
            print(f"  P95: {pred_time['p95']:.3f}秒")
            
            memory = summary['memory_usage']
            print(f"\n内存使用统计:")
            print(f"  平均: {memory['avg']:.1f}%")
            print(f"  最小: {memory['min']:.1f}%")
            print(f"  最大: {memory['max']:.1f}%")
            
            cpu = summary['cpu_usage']
            print(f"\nCPU使用统计:")
            print(f"  平均: {cpu['avg']:.1f}%")
            print(f"  最小: {cpu['min']:.1f}%")
            print(f"  最大: {cpu['max']:.1f}%")
            
            if 'gpu_memory_usage' in summary:
                gpu = summary['gpu_memory_usage']
                print(f"\nGPU内存使用统计:")
                print(f"  平均: {gpu['avg']:.1f}%")
                print(f"  最小: {gpu['min']:.1f}%")
                print(f"  最大: {gpu['max']:.1f}%")
        
        # 显示告警摘要
        print("\n=== 告警摘要 ===")
        all_alerts = monitor.get_recent_alerts(hours=1)
        
        if not all_alerts:
            print("✅ 无告警")
        else:
            print(f"总告警数: {len(all_alerts)}")
            
            # 按类型分组
            alert_types = {}
            for alert in all_alerts:
                alert_type = alert['type']
                if alert_type not in alert_types:
                    alert_types[alert_type] = 0
                alert_types[alert_type] += 1
            
            print("告警类型分布:")
            for alert_type, count in alert_types.items():
                print(f"  {alert_type}: {count}")
            
            # 显示最近的告警
            print("\n最近的告警:")
            for alert in all_alerts[-5:]:  # 显示最近5个告警
                print(f"  [{alert['timestamp'].strftime('%H:%M:%S')}] {alert['type']}: {alert['message']}")
        
        print("\n监控示例完成!")
        
    except Exception as e:
        print(f"监控过程中发生错误: {e}")
        
    finally:
        # 清理资源
        print("\n正在清理资源...")
        await predictor.cleanup()
        print("资源清理完成")

if __name__ == "__main__":
    asyncio.run(monitoring_example())
```

## 🔧 故障处理示例

### 自动故障恢复和重试机制

```python
import asyncio
import logging
import traceback
from typing import Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
import datetime
from industrytslib.core_aysnc.async_predictor_agents import AsyncClassicRealtimePredictor, AsyncTimeSeriesRealtimePredictor

class FailureType(Enum):
    """故障类型枚举"""
    DATABASE_CONNECTION = "database_connection"
    MODEL_LOADING = "model_loading"
    PREDICTION_ERROR = "prediction_error"
    MEMORY_ERROR = "memory_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class FailureInfo:
    """故障信息"""
    failure_type: FailureType
    error_message: str
    timestamp: datetime.datetime
    retry_count: int
    resolved: bool = False
    resolution_time: Optional[datetime.datetime] = None

class PredictorFailureHandler:
    """预测器故障处理器"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 5.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.failure_history = []
        self.logger = logging.getLogger("PredictorFailureHandler")
        
        # 故障处理策略
        self.failure_handlers = {
            FailureType.DATABASE_CONNECTION: self._handle_database_failure,
            FailureType.MODEL_LOADING: self._handle_model_loading_failure,
            FailureType.PREDICTION_ERROR: self._handle_prediction_failure,
            FailureType.MEMORY_ERROR: self._handle_memory_failure,
            FailureType.TIMEOUT_ERROR: self._handle_timeout_failure,
            FailureType.UNKNOWN_ERROR: self._handle_unknown_failure
        }
    
    def classify_error(self, error: Exception) -> FailureType:
        """分类错误类型"""
        error_str = str(error).lower()
        
        # 数据库连接错误
        if any(keyword in error_str for keyword in ['connection', 'database', 'sql', 'timeout']):
            return FailureType.DATABASE_CONNECTION
        
        # 模型加载错误
        elif any(keyword in error_str for keyword in ['model', 'checkpoint', 'load', 'file not found']):
            return FailureType.MODEL_LOADING
        
        # 内存错误
        elif any(keyword in error_str for keyword in ['memory', 'cuda out of memory', 'allocation']):
            return FailureType.MEMORY_ERROR
        
        # 超时错误
        elif any(keyword in error_str for keyword in ['timeout', 'timed out']):
            return FailureType.TIMEOUT_ERROR
        
        # 预测错误
        elif any(keyword in error_str for keyword in ['prediction', 'inference', 'forward']):
            return FailureType.PREDICTION_ERROR
        
        else:
            return FailureType.UNKNOWN_ERROR
    
    async def handle_failure(self, predictor, error: Exception, retry_count: int = 0) -> bool:
        """处理故障"""
        failure_type = self.classify_error(error)
        
        failure_info = FailureInfo(
            failure_type=failure_type,
            error_message=str(error),
            timestamp=datetime.datetime.now(),
            retry_count=retry_count
        )
        
        self.failure_history.append(failure_info)
        
        self.logger.error(f"检测到故障: {failure_type.value} - {error}")
        
        # 如果重试次数超过限制,返回失败
        if retry_count >= self.max_retries:
            self.logger.error(f"重试次数已达上限 ({self.max_retries}),停止重试")
            return False
        
        # 调用对应的故障处理器
        handler = self.failure_handlers.get(failure_type, self._handle_unknown_failure)
        
        try:
            success = await handler(predictor, error, retry_count)
            
            if success:
                failure_info.resolved = True
                failure_info.resolution_time = datetime.datetime.now()
                self.logger.info(f"故障已解决: {failure_type.value}")
            
            return success
            
        except Exception as handler_error:
            self.logger.error(f"故障处理器执行失败: {handler_error}")
            return False
    
    async def _handle_database_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理数据库连接故障"""
        self.logger.info(f"尝试修复数据库连接故障 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 等待一段时间后重新连接
            await asyncio.sleep(self.retry_delay * (retry_count + 1))
            
            # 重新连接数据库
            await predictor.db_reconnect()
            
            # 测试连接
            if hasattr(predictor, 'web_db_client') and predictor.web_db_client:
                # 执行简单查询测试连接
                test_query = "SELECT 1"
                await predictor.web_db_client.execute_query(test_query)
            
            self.logger.info("数据库连接已恢复")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接恢复失败: {e}")
            return False
    
    async def _handle_model_loading_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理模型加载故障"""
        self.logger.info(f"尝试修复模型加载故障 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 等待一段时间
            await asyncio.sleep(self.retry_delay)
            
            # 清理现有模型
            if hasattr(predictor, 'model'):
                predictor.model = None
            if hasattr(predictor, 'scaler_x'):
                predictor.scaler_x = None
            if hasattr(predictor, 'scaler_y'):
                predictor.scaler_y = None
            
            # 清理GPU缓存
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            # 重新加载模型
            await predictor.load_model_and_scaler()
            
            self.logger.info("模型重新加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"模型重新加载失败: {e}")
            return False
    
    async def _handle_prediction_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理预测故障"""
        self.logger.info(f"尝试修复预测故障 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 等待一段时间
            await asyncio.sleep(self.retry_delay)
            
            # 检查模型状态
            if not hasattr(predictor, 'model') or predictor.model is None:
                await predictor.load_model_and_scaler()
            
            # 清理GPU缓存
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            self.logger.info("预测环境已重置")
            return True
            
        except Exception as e:
            self.logger.error(f"预测环境重置失败: {e}")
            return False
    
    async def _handle_memory_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理内存故障"""
        self.logger.info(f"尝试修复内存故障 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 清理GPU缓存
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
            except ImportError:
                pass
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 等待一段时间让系统释放内存
            await asyncio.sleep(self.retry_delay * 2)
            
            self.logger.info("内存已清理")
            return True
            
        except Exception as e:
            self.logger.error(f"内存清理失败: {e}")
            return False
    
    async def _handle_timeout_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理超时故障"""
        self.logger.info(f"尝试修复超时故障 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 增加等待时间
            wait_time = self.retry_delay * (retry_count + 2)
            await asyncio.sleep(wait_time)
            
            # 重新连接数据库(超时通常与网络相关)
            if hasattr(predictor, 'db_reconnect'):
                await predictor.db_reconnect()
            
            self.logger.info("超时问题已处理")
            return True
            
        except Exception as e:
            self.logger.error(f"超时问题处理失败: {e}")
            return False
    
    async def _handle_unknown_failure(self, predictor, error: Exception, retry_count: int) -> bool:
        """处理未知故障"""
        self.logger.info(f"尝试通用故障恢复 (重试 {retry_count + 1}/{self.max_retries})")
        
        try:
            # 等待一段时间
            await asyncio.sleep(self.retry_delay * (retry_count + 1))
            
            # 尝试重新初始化预测器
            await predictor.initialize()
            
            self.logger.info("通用故障恢复完成")
            return True
            
        except Exception as e:
            self.logger.error(f"通用故障恢复失败: {e}")
            return False
    
    def get_failure_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取故障统计信息"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        recent_failures = [f for f in self.failure_history if f.timestamp >= cutoff_time]
        
        if not recent_failures:
            return {"message": f"过去{hours}小时内无故障记录"}
        
        # 按类型统计
        failure_counts = {}
        resolved_counts = {}
        
        for failure in recent_failures:
            failure_type = failure.failure_type.value
            failure_counts[failure_type] = failure_counts.get(failure_type, 0) + 1
            
            if failure.resolved:
                resolved_counts[failure_type] = resolved_counts.get(failure_type, 0) + 1
        
        # 计算解决率
        resolution_rates = {}
        for failure_type, count in failure_counts.items():
            resolved = resolved_counts.get(failure_type, 0)
            resolution_rates[failure_type] = (resolved / count) * 100 if count > 0 else 0
        
        return {
            "time_range": f"过去{hours}小时",
            "total_failures": len(recent_failures),
            "resolved_failures": len([f for f in recent_failures if f.resolved]),
            "overall_resolution_rate": (len([f for f in recent_failures if f.resolved]) / len(recent_failures)) * 100,
            "failure_counts": failure_counts,
            "resolution_rates": resolution_rates,
            "recent_failures": [
                {
                    "type": f.failure_type.value,
                    "message": f.error_message,
                    "timestamp": f.timestamp.isoformat(),
                    "retry_count": f.retry_count,
                    "resolved": f.resolved,
                    "resolution_time": f.resolution_time.isoformat() if f.resolution_time else None
                }
                for f in recent_failures[-10:]  # 最近10个故障
            ]
        }

class ResilientPredictor:
    """具有故障恢复能力的预测器包装器"""
    
    def __init__(self, predictor, failure_handler: PredictorFailureHandler):
        self.predictor = predictor
        self.failure_handler = failure_handler
        self.logger = logging.getLogger("ResilientPredictor")
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5
    
    async def safe_predict(self) -> bool:
        """安全预测(带故障恢复)"""
        retry_count = 0
        
        while retry_count <= self.failure_handler.max_retries:
            try:
                # 执行预测
                await self.predictor.main()
                
                # 预测成功,重置连续失败计数
                self.consecutive_failures = 0
                return True
                
            except Exception as e:
                self.logger.error(f"预测失败 (尝试 {retry_count + 1}): {e}")
                
                # 增加连续失败计数
                self.consecutive_failures += 1
                
                # 如果连续失败次数过多,暂停一段时间
                if self.consecutive_failures >= self.max_consecutive_failures:
                    self.logger.warning(f"连续失败{self.consecutive_failures}次,暂停预测服务")
                    await asyncio.sleep(60)  # 暂停1分钟
                    self.consecutive_failures = 0
                
                # 尝试故障恢复
                recovery_success = await self.failure_handler.handle_failure(
                    self.predictor, e, retry_count
                )
                
                if not recovery_success:
                    retry_count += 1
                    if retry_count <= self.failure_handler.max_retries:
                        await asyncio.sleep(self.failure_handler.retry_delay)
                else:
                    # 故障恢复成功,重试预测
                    continue
        
        # 所有重试都失败了
        self.logger.error("预测失败,已达最大重试次数")
        return False
    
    async def run_with_recovery(self, prediction_interval: float = 60.0):
        """运行预测服务(带故障恢复)"""
        self.logger.info(f"启动具有故障恢复能力的预测服务,预测间隔: {prediction_interval}秒")
        
        while True:
            try:
                success = await self.safe_predict()
                
                if success:
                    self.logger.debug("预测执行成功")
                else:
                    self.logger.error("预测执行失败")
                
                # 等待下一次预测
                await asyncio.sleep(prediction_interval)
                
            except KeyboardInterrupt:
                self.logger.info("接收到中断信号,停止预测服务")
                break
            except Exception as e:
                self.logger.error(f"预测服务发生未处理的错误: {e}")
                await asyncio.sleep(prediction_interval)

# 使用示例
async def failure_handling_example():
    """故障处理示例"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 数据库配置
    dbconfig = {
        "web": {
            "server": "localhost",
            "database": "FailureTestDB",
            "username": "test_user",
            "password": "test_password"
        },
        "timeseries": {
            "server": "localhost",
            "database": "TimeSeriesDB",
            "username": "ts_user",
            "password": "ts_password"
        },
        "realtimepredict": {
            "server": "localhost",
            "database": "PredictDB",
            "username": "predict_user",
            "password": "predict_password"
        }
    }
    
    # 创建预测器
    predictor = AsyncClassicRealtimePredictor(
        project_name="failure_test",
        dbconfig=dbconfig,
        local_test_mode=True  # 使用本地测试模式
    )
    
    # 创建故障处理器
    failure_handler = PredictorFailureHandler(
        max_retries=3,
        retry_delay=2.0
    )
    
    # 创建具有故障恢复能力的预测器
    resilient_predictor = ResilientPredictor(predictor, failure_handler)
    
    try:
        print("=== 故障处理和恢复示例 ===")
        
        # 初始化预测器
        print("\n正在初始化预测器...")
        await predictor.initialize()
        
        # 手动设置测试配置(本地测试模式)
        predictor.input_name_list = ["temp", "pressure", "flow"]
        predictor.output_name_list = ["quality"]
        predictor.pred_length = 5
        
        print("预测器初始化完成")
        
        # 模拟一些故障场景
        print("\n=== 模拟故障场景 ===")
        
        # 场景1:模拟数据库连接故障
        print("\n1. 模拟数据库连接故障...")
        try:
            # 人为制造数据库连接错误
            original_client = predictor.web_db_client
            predictor.web_db_client = None
            
            success = await resilient_predictor.safe_predict()
            print(f"故障恢复结果: {'成功' if success else '失败'}")
            
            # 恢复连接
            predictor.web_db_client = original_client
            
        except Exception as e:
            print(f"数据库故障测试异常: {e}")
        
        # 场景2:模拟模型加载故障
        print("\n2. 模拟模型加载故障...")
        try:
            # 人为制造模型加载错误
            original_model = getattr(predictor, 'model', None)
            predictor.model = None
            
            # 模拟文件不存在错误
            class ModelLoadError(Exception):
                pass
            
            # 临时替换加载方法
            original_load_method = predictor.load_model_and_scaler
            
            async def failing_load():
                raise ModelLoadError("Model checkpoint file not found")
            
            predictor.load_model_and_scaler = failing_load
            
            success = await resilient_predictor.safe_predict()
            print(f"故障恢复结果: {'成功' if success else '失败'}")
            
            # 恢复原始方法
            predictor.load_model_and_scaler = original_load_method
            if original_model:
                predictor.model = original_model
            
        except Exception as e:
            print(f"模型加载故障测试异常: {e}")
        
        # 场景3:正常预测测试
        print("\n3. 正常预测测试...")
        for i in range(3):
            print(f"\n执行第 {i+1} 次预测...")
            success = await resilient_predictor.safe_predict()
            print(f"预测结果: {'成功' if success else '失败'}")
            await asyncio.sleep(1)
        
        # 显示故障统计
        print("\n=== 故障统计信息 ===")
        stats = failure_handler.get_failure_statistics(hours=1)
        
        if "message" in stats:
            print(stats["message"])
        else:
            print(f"时间范围: {stats['time_range']}")
            print(f"总故障数: {stats['total_failures']}")
            print(f"已解决故障数: {stats['resolved_failures']}")
            print(f"整体解决率: {stats['overall_resolution_rate']:.1f}%")
            
            if stats['failure_counts']:
                print("\n故障类型统计:")
                for failure_type, count in stats['failure_counts'].items():
                    resolution_rate = stats['resolution_rates'].get(failure_type, 0)
                    print(f"  {failure_type}: {count} 次 (解决率: {resolution_rate:.1f}%)")
            
            if stats['recent_failures']:
                print("\n最近的故障记录:")
                for failure in stats['recent_failures']:
                    status = "✅ 已解决" if failure['resolved'] else "❌ 未解决"
                    print(f"  [{failure['timestamp'][:19]}] {failure['type']}: {failure['message'][:50]}... {status}")
        
        print("\n故障处理示例完成!")
        
    except Exception as e:
        print(f"示例执行过程中发生错误: {e}")
        traceback.print_exc()
        
    finally:
        # 清理资源
        print("\n正在清理资源...")
        try:
            await predictor.cleanup()
            print("资源清理完成")
        except Exception as e:
            print(f"资源清理失败: {e}")

if __name__ == "__main__":
    asyncio.run(failure_handling_example())
```

## 📋 总结

本文档提供了异步预测器模块的全面使用示例,涵盖了从基础使用到生产环境部署的各个方面:

### 🎯 主要特性

1. **异步设计**: 所有操作都是异步的,提供高并发性能
2. **模型热重载**: 支持在不停止服务的情况下更新模型
3. **故障恢复**: 内置故障检测和自动恢复机制
4. **性能监控**: 提供详细的性能指标和告警功能
5. **类型安全**: 完整的类型注解和验证

### 🚀 使用场景

- **实时软测量**: 工业过程的实时质量预测
- **时间序列预测**: 设备状态、能耗等时序数据预测
- **批量预测**: 大规模数据的批量处理
- **生产环境部署**: 高可用性的预测服务

### 💡 最佳实践

1. **资源管理**: 始终使用 `try-finally` 确保资源清理
2. **错误处理**: 实现完善的异常处理和重试机制
3. **监控告警**: 部署性能监控和故障告警系统
4. **配置管理**: 使用配置文件管理数据库连接和模型参数
5. **日志记录**: 详细记录操作日志便于问题排查

### 🔗 相关文档

- [异步预测器概述](async_predictor_overview.md)
- [AsyncBaseRealtimePredictor 详细文档](async_basic_predictor.md)
- [AsyncClassicRealtimePredictor 详细文档](async_classic_predictor.md)
- [AsyncTimeSeriesRealtimePredictor 详细文档](async_time_series_predictor.md)

通过这些示例,您可以快速上手异步预测器模块,并根据具体需求进行定制和扩展。