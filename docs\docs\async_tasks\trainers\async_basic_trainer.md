# 异步基础训练器 (AsyncModelTrainer)

`AsyncModelTrainer` 是所有异步训练器的基类,提供了完整的异步模型训练基础设施。它继承自 `AsyncScheduledTask`,专门为解决训练任务阻塞线程问题而设计。

## 类定义

**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_basic_trainer.py`

```python
class AsyncModelTrainer(AsyncScheduledTask):
    """异步模型训练器基类"""
```

## 核心功能

### 1. 异步数据库连接管理
- **多数据库支持**: 支持时序数据库和输出数据库的异步连接
- **连接池管理**: 自动管理数据库连接池,提高连接效率
- **在线/离线模式**: 根据 `local_test_mode` 参数自动切换数据源
- **延迟初始化**: 数据库客户端采用延迟初始化策略,节省资源

### 2. 设备管理
- **自动设备检测**: 自动检测并配置 GPU/CPU 训练设备
- **设备信息记录**: 详细记录使用的设备信息
- **CUDA 支持**: 优先使用 GPU,自动回退到 CPU

### 3. 路径管理
- **结果目录**: 自动创建训练结果、测试结果等目录
- **路径配置**: 统一管理各种输出路径
- **目录结构**: 按项目名称组织目录结构

### 4. 工具集成
- **TensorBoard**: 集成 TensorBoard 进行训练监控
- **绘图工具**: 支持 Plotly 和 Matplotlib 两种绘图后端
- **日志系统**: 专用的异步训练日志记录

## 初始化参数

### 构造函数
```python
def __init__(
    self,
    project_name: str,
    dbconfig: Dict[str, Any],
    local_test_mode: bool = False,
    **kwargs,
) -> None:
```

### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `project_name` | `str` | 是 | 项目名称,用于标识训练任务和组织输出目录 |
| `dbconfig` | `Dict[str, Any]` | 是 | 数据库配置字典,包含连接信息 |
| `local_test_mode` | `bool` | 否 | 本地测试模式,True时使用离线模式,默认False |
| `**kwargs` | `Any` | 否 | 其他初始化参数,传递给父类 |

### 数据库配置示例
```python
dbconfig = {
    "server": "localhost",
    "database": "timeseries_db",
    "username": "user",
    "password": "password",
    "port": 1433,
    "driver": "ODBC Driver 17 for SQL Server"
}
```

## 核心属性

### 基础配置属性
```python
self.project_name: str              # 项目名称
self.dbconfig: Dict[str, Any]       # 数据库配置
self.local_test_mode: bool          # 本地测试模式
self.device: torch.device           # 训练设备
self.run_mode: str                  # 运行模式 ("online" 或 "offline")
```

### 路径属性
```python
self.train_result_path: Path        # 训练结果路径
self.test_train_result_path: Path   # 测试训练结果路径
self.test_result_path: Path         # 测试结果路径
self.train_val_result_path: Path    # 训练验证结果路径
```

### 工具属性
```python
self.writer: SummaryWriter          # TensorBoard 写入器
self.plotter: Any                   # Plotly 绘图器
self.plotter_matplotlib: Any       # Matplotlib 绘图器
self.logger_basic_trainer: Logger  # 专用日志器
```

### 训练相关属性
```python
self.model: Optional[torch.nn.Module]      # 训练模型
self.optimizer: Optional[torch.optim.Optimizer]  # 优化器
self.criterion: Optional[torch.nn.Module]  # 损失函数
self.epoch_scheduler: Optional[Any]        # 学习率调度器
self.data_input: Optional[Union[pl.DataFrame, List[pl.DataFrame]]]   # 输入数据
self.data_output: Optional[Union[pl.DataFrame, List[pl.DataFrame]]]  # 输出数据
self.scaler_x: Optional[Any]               # 输入数据缩放器
self.scaler_y: Optional[Any]               # 输出数据缩放器
```

### 数据库客户端属性
```python
self.ts_mssql_client: Optional[Any]        # 时序数据库客户端
self.out_db_client: Optional[Any]          # 输出数据库客户端
```

## 核心方法

### 1. 设备配置方法
```python
def _setup_device(self) -> None:
    """设置训练设备 (GPU/CPU)"""
```

**功能**:
- 自动检测 CUDA 可用性
- 优先使用 GPU,不可用时回退到 CPU
- 记录设备信息到日志

**实现逻辑**:
```python
if torch.cuda.is_available():
    self.device = torch.device("cuda")
    self.logger_basic_trainer.info(
        f"使用 GPU 设备: {torch.cuda.get_device_name()}"
    )
else:
    self.device = torch.device("cpu")
    self.logger_basic_trainer.info("使用 CPU 设备")
```

### 2. 路径配置方法
```python
def _setup_paths(self) -> None:
    """设置训练相关路径"""
```

**功能**:
- 创建训练结果、测试结果、验证结果等目录
- 按项目名称组织目录结构
- 确保目录存在,不存在则自动创建

**目录结构**:
```
resource/results/
├── train_results/{project_name}/          # 训练结果
├── test_train_results/{project_name}/     # 测试训练结果
├── test_results/{project_name}/           # 测试结果
└── train_val_results/{project_name}/      # 训练验证结果
```

### 3. 工具配置方法
```python
def _setup_tools(self) -> None:
    """设置训练工具"""
```

**功能**:
- 初始化 TensorBoard 写入器
- 配置 Plotly 和 Matplotlib 绘图器
- 设置运行模式标识

**工具配置**:
```python
# TensorBoard 配置
self.writer = SummaryWriter(log_dir=f"runs/{self.project_name}")

# 绘图器配置
self.plotter = plotter_builder(
    plotter_type="plotly", 
    project_name=self.project_name
)
self.plotter_matplotlib = plotter_builder(
    plotter_type="matplotlib", 
    project_name=self.project_name
)
```

### 4. 训练属性初始化方法
```python
def _init_training_attributes(self) -> None:
    """初始化训练相关属性"""
```

**功能**:
- 初始化模型、优化器、损失函数等为 None
- 初始化数据相关属性
- 初始化缩放器属性
- 为后续配置做准备

## 使用示例

### 1. 基本使用
```python
import asyncio
from industrytslib.core_aysnc.async_model_trainers import AsyncModelTrainer

# 数据库配置
dbconfig = {
    "server": "localhost",
    "database": "timeseries_db",
    "username": "user",
    "password": "password"
}

# 创建异步训练器实例
trainer = AsyncModelTrainer(
    project_name="my_async_project",
    dbconfig=dbconfig,
    local_test_mode=False
)

# 查看配置信息
print(f"项目名称: {trainer.project_name}")
print(f"训练设备: {trainer.device}")
print(f"运行模式: {trainer.run_mode}")
print(f"训练结果路径: {trainer.train_result_path}")
```

### 2. 本地测试模式
```python
# 本地测试模式(离线模式)
trainer = AsyncModelTrainer(
    project_name="local_test_project",
    dbconfig={},  # 本地模式可以传空配置
    local_test_mode=True
)

print(f"运行模式: {trainer.run_mode}")  # 输出: offline
```

### 3. 自定义配置
```python
# 带额外参数的初始化
trainer = AsyncModelTrainer(
    project_name="custom_project",
    dbconfig=dbconfig,
    local_test_mode=False,
    batch_size=64,
    learning_rate=0.001,
    num_epochs=100
)
```

## 日志系统

### 日志配置
```python
self.logger_basic_trainer = get_logger(
    logger_name=f"AsyncModelTrainer_{project_name}",
    logger_type="model_trainer_async",
    level="INFO"
)
```

### 日志级别
- **INFO**: 基本信息记录(默认)
- **DEBUG**: 详细调试信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息

### 日志内容
- 初始化完成信息
- 设备配置信息
- 运行模式信息
- 错误和异常信息

## 错误处理

### 1. 数据库配置验证
```python
if dbconfig is None:
    raise ValueError("数据库配置不能为空")
if not isinstance(dbconfig, dict):
    raise ValueError("数据库配置必须是字典类型")
```

### 2. 异常捕获和记录
```python
try:
    # 训练逻辑
    pass
except Exception as e:
    self.logger_basic_trainer.error(f"训练过程中发生错误: {e}")
    raise
```

## 扩展开发

### 继承 AsyncModelTrainer
```python
class CustomAsyncTrainer(AsyncModelTrainer):
    """自定义异步训练器"""
    
    def __init__(self, project_name, dbconfig, **kwargs):
        super().__init__(project_name, dbconfig, **kwargs)
        
        # 自定义初始化
        self.custom_attribute = kwargs.get('custom_attribute', 'default')
        
        # 自定义日志器
        self.logger_custom = get_logger(
            logger_name=f"{project_name}_CustomAsyncTrainer",
            logger_type="custom_trainer_async",
            level="DEBUG"
        )
    
    def _init_custom_attributes(self):
        """初始化自定义属性"""
        # 实现自定义属性初始化
        pass
    
    async def custom_method(self):
        """自定义异步方法"""
        # 实现自定义功能
        pass
```

### 重写基础方法
```python
class EnhancedAsyncTrainer(AsyncModelTrainer):
    """增强的异步训练器"""
    
    def _setup_device(self):
        """重写设备配置方法"""
        # 调用父类方法
        super()._setup_device()
        
        # 添加自定义设备配置
        if torch.cuda.is_available():
            torch.cuda.set_device(0)  # 指定GPU设备
            self.logger_basic_trainer.info("设置GPU设备为cuda:0")
    
    def _setup_paths(self):
        """重写路径配置方法"""
        # 调用父类方法
        super()._setup_paths()
        
        # 添加自定义路径
        self.custom_result_path = Path(
            f"resource/results/custom_results/{self.project_name}"
        )
        self.custom_result_path.mkdir(parents=True, exist_ok=True)
```

## 性能优化

### 1. 内存管理
```python
# 在训练完成后清理资源
def cleanup(self):
    """清理训练资源"""
    if hasattr(self, 'model') and self.model is not None:
        del self.model
    if hasattr(self, 'optimizer') and self.optimizer is not None:
        del self.optimizer
    torch.cuda.empty_cache()  # 清理GPU缓存
```

### 2. 异步优化
```python
# 使用异步上下文管理器
class AsyncTrainerContext:
    def __init__(self, trainer):
        self.trainer = trainer
    
    async def __aenter__(self):
        # 异步初始化资源
        await self.trainer.async_init()
        return self.trainer
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 异步清理资源
        await self.trainer.async_cleanup()
```

## 最佳实践

### 1. 资源管理
- 使用上下文管理器确保资源正确释放
- 及时清理不需要的模型和数据
- 监控内存使用情况

### 2. 错误处理
- 实现完整的异常处理机制
- 记录详细的错误信息
- 提供错误恢复策略

### 3. 日志记录
- 使用适当的日志级别
- 记录关键操作和状态变化
- 避免过度日志记录影响性能

### 4. 配置管理
- 验证输入参数的有效性
- 提供合理的默认值
- 支持配置的动态更新

## 相关文档

- [异步时序经典训练器](async_time_series_classic_trainer.md)
- [异步时序序列训练器](async_time_series_sequence_trainer.md)
- [异步训练管道](async_training_pipeline.md)
- [使用示例](../usage_examples.md)