# 异步时序经典训练器 (AsyncTimeSeriesClassicTrainer)

`AsyncTimeSeriesClassicTrainer` 是专门用于训练经典时序模型(如 LSTM、GRU 等)的异步训练器。它继承自 `AsyncModelTrainer`,提供了完整的异步时序模型训练流程,包括数据增强、早停策略、模型评估等功能。

## 类定义

**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_time_series_classic_trainer.py`

```python
class AsyncTimeSeriesClassicTrainer(AsyncModelTrainer):
    """异步时序经典训练器"""
```

## 核心特性

### 1. 异步时序模型支持
- **多模型支持**: 支持 LSTM、GRU、RNN 等经典时序模型
- **异步模型构建**: 非阻塞的模型构建和初始化
- **设备自适应**: 自动适配 GPU/CPU 设备
- **模型参数管理**: 完整的模型参数配置和管理

### 2. 并发数据增强
- **异步数据增强**: 支持多种数据增强技术的并发执行
- **噪声添加**: 高斯噪声、均匀噪声等
- **时间掩码**: 随机时间步掩码
- **缩放变换**: 数据缩放和变换
- **配置化增强**: 通过配置文件控制增强策略

### 3. 异步训练流程
- **非阻塞训练**: 完全异步的训练循环
- **异步数据加载**: 并发数据获取和预处理
- **异步验证**: 并发模型验证和评估
- **早停策略**: 智能训练停止机制
- **实时监控**: 训练过程实时状态监控

### 4. 异步模型评估
- **并发指标计算**: 多种评估指标的并发计算
- **异步测试**: 非阻塞的模型测试
- **结果可视化**: 异步生成训练和测试结果图表
- **模型保存**: 异步模型权重和配置保存

## 初始化参数

### 构造函数
```python
def __init__(
    self,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> None:
```

### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `project_name` | `str` | 是 | 项目名称,用于标识训练任务 |
| `dbconfig` | `Optional[Dict[str, Any]]` | 否 | 数据库配置,可选 |
| `local_test_mode` | `bool` | 否 | 本地测试模式,默认False |
| `**kwargs` | `Any` | 否 | 其他初始化参数 |

## 核心属性

### 时序训练专用属性
```python
self.task_type: str = "AsyncTimeSeriesClassicTrainer"  # 任务类型标识
self.algorithm_name: Optional[str] = None              # 算法名称
self.output_flag: Optional[str] = None                 # 输出标志
self.input_name_list: List[str] = []                   # 输入特征名称列表
self.output_name_list: List[str] = []                  # 输出特征名称列表
```

### 数据增强配置
```python
self.apply_aug: bool = False                           # 是否应用数据增强
self.aug_config: Dict[str, Any] = {}                   # 数据增强配置
```

### 早停策略配置
```python
self.early_stopping_config: Dict[str, Any] = {}       # 早停策略配置
self.patience: int = 2200                              # 早停耐心值
self.early_stopping_delta: float = 0.000001           # 早停阈值
```

### 路径配置
```python
self.trained_model_path: Optional[Path] = None        # 训练模型路径
self.history_path: Optional[Path] = None              # 训练历史路径
self.model_final_path: Optional[Path] = None          # 最终模型路径
self.scaler_y_path: Optional[Path] = None             # 输出缩放器路径
self.scaler_input_name_path: Optional[Path] = None    # 输入名称路径
self.scaler_output_name_path: Optional[Path] = None   # 输出名称路径
```

### 缩放器对象
```python
self.scaler_y: Optional[Any] = None                   # 输出数据缩放器
```

## 核心方法

### 1. 模型构建方法
```python
def build_model(self, model_parameter: Optional[Dict[str, Any]] = None) -> torch.nn.Module:
    """构建时序模型"""
```

**功能**:
- 根据算法名称和模型参数构建相应的时序模型
- 支持 LSTM、GRU、RNN 等经典时序模型
- 自动将模型移动到指定设备(GPU/CPU)
- 记录模型构建信息到日志

**参数**:
- `model_parameter`: 模型参数字典,如果为None则使用实例属性

**返回值**:
- `torch.nn.Module`: 构建的时序模型实例

**使用示例**:
```python
# 设置算法名称和模型参数
trainer.algorithm_name = "LSTM"
trainer.model_parameter = {
    "input_size": 10,
    "hidden_size": 64,
    "num_layers": 2,
    "output_size": 1,
    "dropout": 0.1
}

# 构建模型
model = trainer.build_model()
print(f"模型类型: {type(model)}")
print(f"模型设备: {next(model.parameters()).device}")
```

### 2. 异步数据获取方法
```python
async def _get_data(
    self, flag: Literal["train", "val", "test"]
) -> Tuple[Dataset, DataLoader]:
    """异步获取数据集和数据加载器"""
```

**功能**:
- 异步获取训练、验证或测试数据
- 支持在线数据库和离线文件数据源
- 并发数据预处理和增强
- 返回数据集和数据加载器

**参数**:
- `flag`: 数据类型标志 ("train", "val", "test")

**返回值**:
- `Tuple[Dataset, DataLoader]`: 数据集和数据加载器

**实现特点**:
- 异步数据库查询,避免阻塞
- 并发数据预处理,提高效率
- 智能缓存机制,减少重复加载
- 错误处理和重试机制

### 3. 数据增强方法
```python
async def _apply_data_augmentation(self, data: torch.Tensor) -> torch.Tensor:
    """异步应用数据增强"""
```

**功能**:
- 异步应用多种数据增强技术
- 支持噪声添加、时间掩码、缩放变换等
- 并发处理多个增强策略
- 配置化增强参数

**数据增强类型**:
1. **高斯噪声**: 添加高斯分布噪声
2. **均匀噪声**: 添加均匀分布噪声
3. **时间掩码**: 随机掩码时间步
4. **缩放变换**: 数据缩放和变换
5. **时间偏移**: 时间序列偏移
6. **频域变换**: 频域数据增强

**配置示例**:
```python
aug_config = {
    "gaussian_noise": {
        "enabled": True,
        "std": 0.01,
        "probability": 0.5
    },
    "time_mask": {
        "enabled": True,
        "mask_ratio": 0.1,
        "probability": 0.3
    },
    "scaling": {
        "enabled": True,
        "scale_range": [0.8, 1.2],
        "probability": 0.4
    }
}
```

### 4. 异步训练方法
```python
async def train(self) -> float:
    """异步训练方法"""
```

**功能**:
- 完整的异步训练循环
- 并发数据加载和模型训练
- 实时损失计算和记录
- 异步梯度更新和优化

**训练流程**:
1. 异步获取训练数据
2. 并发数据增强处理
3. 前向传播计算损失
4. 反向传播更新梯度
5. 异步记录训练指标
6. 实时监控训练状态

**返回值**:
- `float`: 平均训练损失

### 5. 异步验证方法
```python
async def vali(self) -> float:
    """异步验证方法"""
```

**功能**:
- 异步模型验证和评估
- 并发验证数据处理
- 多种评估指标计算
- 验证结果记录和可视化

**验证流程**:
1. 异步获取验证数据
2. 模型推理和预测
3. 并发指标计算
4. 结果记录和可视化
5. 早停策略判断

**返回值**:
- `float`: 平均验证损失

### 6. 异步测试方法
```python
async def test(self) -> Dict[str, float]:
    """异步测试方法"""
```

**功能**:
- 完整的异步模型测试
- 并发测试数据处理
- 多种评估指标计算
- 测试结果保存和可视化

**测试指标**:
- **MAE**: 平均绝对误差
- **MSE**: 均方误差
- **RMSE**: 均方根误差
- **MAPE**: 平均绝对百分比误差
- **R²**: 决定系数

**返回值**:
- `Dict[str, float]`: 测试指标字典

### 7. 主训练流程方法
```python
async def main(self) -> None:
    """主训练流程"""
```

**功能**:
- 完整的异步训练和测试流程
- 自动配置和初始化
- 训练过程监控和记录
- 模型保存和结果输出

**流程步骤**:
1. 异步初始化配置
2. 构建模型和优化器
3. 异步训练循环
4. 异步验证和早停
5. 异步测试和评估
6. 保存模型和结果

## 使用示例

### 1. 基本使用
```python
import asyncio
from industrytslib.core_aysnc.async_model_trainers import AsyncTimeSeriesClassicTrainer

async def main():
    # 创建异步时序经典训练器
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="lstm_forecast",
        dbconfig={
            "server": "localhost",
            "database": "timeseries_db",
            "username": "user",
            "password": "password"
        },
        local_test_mode=False
    )
    
    # 配置模型参数
    trainer.algorithm_name = "LSTM"
    trainer.model_parameter = {
        "input_size": 10,
        "hidden_size": 64,
        "num_layers": 2,
        "output_size": 1,
        "dropout": 0.1
    }
    
    # 配置训练参数
    trainer.batch_size = 32
    trainer.learning_rate = 0.001
    trainer.num_epochs = 100
    
    # 异步执行训练
    await trainer.main()

# 运行异步训练
asyncio.run(main())
```

### 2. 数据增强配置
```python
async def main_with_augmentation():
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="lstm_with_aug",
        local_test_mode=True
    )
    
    # 启用数据增强
    trainer.apply_aug = True
    trainer.aug_config = {
        "gaussian_noise": {
            "enabled": True,
            "std": 0.01,
            "probability": 0.5
        },
        "time_mask": {
            "enabled": True,
            "mask_ratio": 0.1,
            "probability": 0.3
        }
    }
    
    # 配置模型和训练参数
    trainer.algorithm_name = "GRU"
    trainer.model_parameter = {
        "input_size": 15,
        "hidden_size": 128,
        "num_layers": 3,
        "output_size": 1
    }
    
    await trainer.main()

asyncio.run(main_with_augmentation())
```

### 3. 早停策略配置
```python
async def main_with_early_stopping():
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="lstm_early_stop",
        local_test_mode=True
    )
    
    # 配置早停策略
    trainer.patience = 50  # 50个epoch无改善则停止
    trainer.early_stopping_delta = 0.0001  # 最小改善阈值
    trainer.early_stopping_config = {
        "monitor": "val_loss",
        "mode": "min",
        "restore_best_weights": True
    }
    
    # 配置模型参数
    trainer.algorithm_name = "LSTM"
    trainer.model_parameter = {
        "input_size": 20,
        "hidden_size": 256,
        "num_layers": 4,
        "output_size": 3,
        "dropout": 0.2
    }
    
    await trainer.main()

asyncio.run(main_with_early_stopping())
```

### 4. 并发训练多个模型
```python
async def concurrent_training():
    """并发训练多个模型"""
    
    # 定义多个训练配置
    configs = [
        {
            "project_name": "lstm_model_1",
            "algorithm_name": "LSTM",
            "hidden_size": 64
        },
        {
            "project_name": "gru_model_1",
            "algorithm_name": "GRU",
            "hidden_size": 128
        },
        {
            "project_name": "rnn_model_1",
            "algorithm_name": "RNN",
            "hidden_size": 32
        }
    ]
    
    # 创建训练任务
    tasks = []
    for config in configs:
        trainer = AsyncTimeSeriesClassicTrainer(
            project_name=config["project_name"],
            local_test_mode=True
        )
        
        trainer.algorithm_name = config["algorithm_name"]
        trainer.model_parameter = {
            "input_size": 10,
            "hidden_size": config["hidden_size"],
            "num_layers": 2,
            "output_size": 1
        }
        
        tasks.append(trainer.main())
    
    # 并发执行所有训练任务
    await asyncio.gather(*tasks)
    print("所有模型训练完成")

asyncio.run(concurrent_training())
```

## 性能优化

### 1. 异步数据加载优化
```python
# 使用数据预取和缓存
class OptimizedAsyncTrainer(AsyncTimeSeriesClassicTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_cache = {}
        self.prefetch_size = 2
    
    async def _get_data_optimized(self, flag):
        # 检查缓存
        if flag in self.data_cache:
            return self.data_cache[flag]
        
        # 异步加载数据
        dataset, dataloader = await self._get_data(flag)
        
        # 缓存数据
        self.data_cache[flag] = (dataset, dataloader)
        
        return dataset, dataloader
```

### 2. 内存管理优化
```python
# 定期清理内存
async def train_with_memory_management(self):
    for epoch in range(self.num_epochs):
        # 训练一个epoch
        train_loss = await self.train()
        
        # 每10个epoch清理一次内存
        if epoch % 10 == 0:
            torch.cuda.empty_cache()
            import gc
            gc.collect()
        
        # 验证
        val_loss = await self.vali()
        
        # 记录指标
        self.writer.add_scalar('Loss/Train', train_loss, epoch)
        self.writer.add_scalar('Loss/Validation', val_loss, epoch)
```

### 3. 并发处理优化
```python
# 使用信号量控制并发数量
import asyncio

class ConcurrentAsyncTrainer(AsyncTimeSeriesClassicTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.semaphore = asyncio.Semaphore(3)  # 最多3个并发任务
    
    async def train_batch_concurrent(self, batches):
        async def process_batch(batch):
            async with self.semaphore:
                return await self._process_batch(batch)
        
        # 并发处理批次
        tasks = [process_batch(batch) for batch in batches]
        results = await asyncio.gather(*tasks)
        return results
```

## 监控和调试

### 1. 训练监控
```python
# 实时监控训练状态
async def monitor_training(trainer):
    while trainer.is_training:
        status = await trainer.get_training_status()
        print(f"Epoch: {status['epoch']}, Loss: {status['loss']:.4f}")
        await asyncio.sleep(1)

# 在训练过程中启动监控
async def main_with_monitoring():
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="monitored_training",
        local_test_mode=True
    )
    
    # 启动监控任务
    monitor_task = asyncio.create_task(monitor_training(trainer))
    
    # 启动训练
    await trainer.main()
    
    # 停止监控
    monitor_task.cancel()
```

### 2. 性能分析
```python
# 性能分析装饰器
import time
import functools

def async_timer(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 耗时: {end_time - start_time:.2f} 秒")
        return result
    return wrapper

# 应用到训练方法
class ProfiledAsyncTrainer(AsyncTimeSeriesClassicTrainer):
    @async_timer
    async def train(self):
        return await super().train()
    
    @async_timer
    async def vali(self):
        return await super().vali()
    
    @async_timer
    async def test(self):
        return await super().test()
```

## 最佳实践

### 1. 错误处理
```python
async def robust_training():
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="robust_training",
        local_test_mode=True
    )
    
    try:
        await trainer.main()
    except Exception as e:
        # 记录错误
        trainer.logger_tsclassic_trainer.error(f"训练失败: {e}")
        
        # 保存中间结果
        if hasattr(trainer, 'model') and trainer.model is not None:
            torch.save(trainer.model.state_dict(), 'emergency_save.pth')
        
        # 清理资源
        await trainer.cleanup()
        
        raise
```

### 2. 配置管理
```python
# 使用配置文件
import yaml

async def training_from_config(config_path):
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name=config['project_name'],
        dbconfig=config.get('dbconfig'),
        local_test_mode=config.get('local_test_mode', False)
    )
    
    # 应用配置
    for key, value in config.get('trainer_config', {}).items():
        setattr(trainer, key, value)
    
    await trainer.main()
```

### 3. 结果管理
```python
# 自动结果备份
async def training_with_backup():
    trainer = AsyncTimeSeriesClassicTrainer(
        project_name="backup_training",
        local_test_mode=True
    )
    
    # 设置自动备份
    trainer.auto_backup = True
    trainer.backup_interval = 10  # 每10个epoch备份一次
    
    await trainer.main()
    
    # 创建结果摘要
    summary = {
        "project_name": trainer.project_name,
        "algorithm_name": trainer.algorithm_name,
        "final_loss": trainer.final_loss,
        "best_epoch": trainer.best_epoch,
        "training_time": trainer.training_time
    }
    
    with open(f"{trainer.project_name}_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
```

## 相关文档

- [异步基础训练器](async_basic_trainer.md)
- [异步时序序列训练器](async_time_series_sequence_trainer.md)
- [异步训练管道](async_training_pipeline.md)
- [使用示例](../usage_examples.md)