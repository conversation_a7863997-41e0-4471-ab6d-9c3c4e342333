# 异步时序序列训练器 (AsyncTimeSeriesSequenceTrainer)

`AsyncTimeSeriesSequenceTrainer` 是专门用于训练基于 Transformer 架构及其变体的异步时序序列训练器。它继承自 `AsyncModelTrainer`,提供了完整的异步序列到序列模型训练流程,支持多变量时序预测、注意力机制、位置编码等高级功能。

## 类定义

**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_time_series_trainer.py`

```python
class AsyncTimeSeriesSequenceTrainer(AsyncModelTrainer):
    """异步时序序列训练器"""
```

## 核心特性

### 1. 异步 Transformer 模型支持
- **多架构支持**: 支持 Transformer、Informer、Autoformer 等序列模型
- **异步模型构建**: 非阻塞的复杂模型构建和初始化
- **注意力机制**: 支持多头注意力、稀疏注意力等机制
- **位置编码**: 时间位置编码和学习位置编码
- **设备自适应**: 自动适配 GPU/CPU 设备和分布式训练

### 2. 序列到序列训练
- **编码器-解码器架构**: 完整的 Seq2Seq 训练流程
- **异步序列处理**: 并发处理输入和输出序列
- **多变量预测**: 支持多元时序数据的并发预测
- **长序列支持**: 高效处理长时序数据
- **动态序列长度**: 支持变长序列的批处理

### 3. 异步训练优化
- **梯度累积**: 异步梯度累积和更新
- **学习率调度**: 智能学习率调整策略
- **混合精度训练**: 自动混合精度训练支持
- **内存优化**: 动态内存管理和优化
- **检查点机制**: 自动训练状态保存和恢复

### 4. 高级训练功能
- **教师强制**: 训练时的教师强制策略
- **束搜索**: 推理时的束搜索解码
- **注意力可视化**: 注意力权重的可视化分析
- **模型解释**: 模型决策的可解释性分析
- **多任务学习**: 支持多任务联合训练

## 初始化参数

### 构造函数
```python
def __init__(
    self,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> None:
```

### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `project_name` | `str` | 是 | 项目名称,用于标识训练任务 |
| `dbconfig` | `Optional[Dict[str, Any]]` | 否 | 数据库配置,可选 |
| `local_test_mode` | `bool` | 否 | 本地测试模式,默认False |
| `**kwargs` | `Any` | 否 | 其他初始化参数 |

## 核心属性

### 序列训练专用属性
```python
self.task_type: str = "AsyncTimeSeriesSequenceTrainer"  # 任务类型标识
self.algorithm_name: Optional[str] = None               # 算法名称
self.output_flag: Optional[str] = None                  # 输出标志
self.input_name_list: List[str] = []                    # 输入特征名称列表
self.output_name_list: List[str] = []                   # 输出特征名称列表
```

### 序列配置参数
```python
self.seq_len: int = 96                                  # 输入序列长度
self.label_len: int = 48                                # 标签序列长度
self.pred_len: int = 24                                 # 预测序列长度
self.enc_in: int = 7                                    # 编码器输入维度
self.dec_in: int = 7                                    # 解码器输入维度
self.c_out: int = 7                                     # 输出维度
```

### Transformer 配置参数
```python
self.d_model: int = 512                                 # 模型维度
self.n_heads: int = 8                                   # 注意力头数
self.e_layers: int = 2                                  # 编码器层数
self.d_layers: int = 1                                  # 解码器层数
self.d_ff: int = 2048                                   # 前馈网络维度
self.dropout: float = 0.1                              # Dropout 率
self.activation: str = 'gelu'                          # 激活函数
```

### 训练策略配置
```python
self.teacher_forcing_ratio: float = 0.5                # 教师强制比例
self.use_amp: bool = False                              # 是否使用混合精度
self.gradient_clip_val: float = 1.0                    # 梯度裁剪值
self.accumulate_grad_batches: int = 1                   # 梯度累积批次
```

### 注意力配置
```python
self.factor: int = 1                                    # 注意力因子
self.attention_type: str = 'full'                      # 注意力类型
self.distil: bool = True                                # 是否使用蒸馏
self.embed: str = 'timeF'                              # 嵌入类型
self.freq: str = 'h'                                    # 时间频率
```

### 路径配置
```python
self.trained_model_path: Optional[Path] = None         # 训练模型路径
self.history_path: Optional[Path] = None               # 训练历史路径
self.model_final_path: Optional[Path] = None           # 最终模型路径
self.attention_path: Optional[Path] = None             # 注意力权重路径
self.checkpoint_path: Optional[Path] = None            # 检查点路径
```

## 核心方法

### 1. 模型构建方法
```python
def build_model(self, model_parameter: Optional[Dict[str, Any]] = None) -> torch.nn.Module:
    """构建序列到序列模型"""
```

**功能**:
- 根据算法名称和模型参数构建相应的序列模型
- 支持 Transformer、Informer、Autoformer 等架构
- 自动配置编码器-解码器结构
- 初始化注意力机制和位置编码
- 自动将模型移动到指定设备

**支持的模型架构**:
1. **Transformer**: 标准 Transformer 架构
2. **Informer**: 稀疏注意力 Transformer
3. **Autoformer**: 自相关机制 Transformer
4. **FEDformer**: 频域增强 Transformer
5. **Pyraformer**: 金字塔注意力 Transformer

**参数**:
- `model_parameter`: 模型参数字典,如果为None则使用实例属性

**返回值**:
- `torch.nn.Module`: 构建的序列模型实例

**使用示例**:
```python
# 设置 Transformer 模型参数
trainer.algorithm_name = "Transformer"
trainer.model_parameter = {
    "seq_len": 96,
    "label_len": 48,
    "pred_len": 24,
    "enc_in": 7,
    "dec_in": 7,
    "c_out": 7,
    "d_model": 512,
    "n_heads": 8,
    "e_layers": 2,
    "d_layers": 1,
    "d_ff": 2048,
    "dropout": 0.1,
    "activation": 'gelu'
}

# 构建模型
model = trainer.build_model()
print(f"模型类型: {type(model)}")
print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
```

### 2. 异步数据获取方法
```python
async def _get_data(
    self, flag: Literal["train", "val", "test"]
) -> Tuple[Dataset, DataLoader]:
    """异步获取序列数据集和数据加载器"""
```

**功能**:
- 异步获取时序序列数据
- 支持多变量时序数据处理
- 并发序列预处理和特征工程
- 动态序列长度处理
- 时间特征编码和嵌入

**序列数据处理**:
1. **序列切分**: 将长时序切分为训练序列
2. **标签构建**: 构建编码器-解码器标签
3. **时间特征**: 提取时间相关特征
4. **归一化**: 序列数据标准化处理
5. **填充**: 变长序列的填充处理

**参数**:
- `flag`: 数据类型标志 ("train", "val", "test")

**返回值**:
- `Tuple[Dataset, DataLoader]`: 序列数据集和数据加载器

### 3. 异步训练方法
```python
async def train(self) -> float:
    """异步序列训练方法"""
```

**功能**:
- 完整的异步序列到序列训练
- 教师强制策略实现
- 并发梯度计算和更新
- 混合精度训练支持
- 梯度累积和裁剪

**训练流程**:
1. 异步获取训练批次
2. 编码器前向传播
3. 解码器教师强制训练
4. 损失计算和反向传播
5. 梯度累积和更新
6. 学习率调度更新

**教师强制策略**:
```python
# 动态教师强制比例
if random.random() < self.teacher_forcing_ratio:
    # 使用真实标签作为解码器输入
    decoder_input = target_sequence
else:
    # 使用模型预测作为解码器输入
    decoder_input = predicted_sequence
```

**返回值**:
- `float`: 平均训练损失

### 4. 异步验证方法
```python
async def vali(self) -> float:
    """异步序列验证方法"""
```

**功能**:
- 异步序列模型验证
- 并发验证数据处理
- 多步预测评估
- 注意力权重分析
- 验证结果可视化

**验证流程**:
1. 异步获取验证数据
2. 模型推理和序列预测
3. 多步预测误差计算
4. 注意力权重提取
5. 验证指标记录

**验证指标**:
- **序列级损失**: 整个序列的预测损失
- **步骤级损失**: 每个时间步的预测损失
- **注意力熵**: 注意力分布的熵值
- **预测一致性**: 多步预测的一致性

**返回值**:
- `float`: 平均验证损失

### 5. 异步测试方法
```python
async def test(self) -> Dict[str, float]:
    """异步序列测试方法"""
```

**功能**:
- 完整的异步序列模型测试
- 多变量预测评估
- 长期预测性能分析
- 注意力可视化生成
- 预测结果保存

**测试评估**:
1. **短期预测**: 1-24步预测评估
2. **中期预测**: 24-96步预测评估
3. **长期预测**: 96+步预测评估
4. **多变量评估**: 每个变量的独立评估
5. **整体性能**: 综合预测性能

**测试指标**:
- **MAE**: 平均绝对误差
- **MSE**: 均方误差
- **RMSE**: 均方根误差
- **MAPE**: 平均绝对百分比误差
- **SMAPE**: 对称平均绝对百分比误差
- **QuantileLoss**: 分位数损失

**返回值**:
- `Dict[str, float]`: 详细测试指标字典

### 6. 注意力可视化方法
```python
async def visualize_attention(
    self, 
    input_sequence: torch.Tensor,
    target_sequence: torch.Tensor
) -> Dict[str, Any]:
    """异步注意力可视化"""
```

**功能**:
- 提取和可视化注意力权重
- 生成注意力热力图
- 分析时序依赖关系
- 保存注意力分析结果

**可视化内容**:
1. **编码器自注意力**: 输入序列内部依赖
2. **解码器自注意力**: 输出序列内部依赖
3. **交叉注意力**: 编码器-解码器依赖
4. **多头注意力**: 不同注意力头的模式
5. **层级注意力**: 不同层的注意力模式

### 7. 主训练流程方法
```python
async def main(self) -> None:
    """主序列训练流程"""
```

**功能**:
- 完整的异步序列训练和测试流程
- 自动配置和初始化
- 训练过程监控和记录
- 模型保存和结果输出
- 注意力分析和可视化

**流程步骤**:
1. 异步初始化配置
2. 构建序列模型和优化器
3. 异步训练循环
4. 异步验证和早停
5. 异步测试和评估
6. 注意力分析和可视化
7. 保存模型和结果

## 使用示例

### 1. 基本 Transformer 训练
```python
import asyncio
from industrytslib.core_aysnc.async_model_trainers import AsyncTimeSeriesSequenceTrainer

async def main():
    # 创建异步时序序列训练器
    trainer = AsyncTimeSeriesSequenceTrainer(
        project_name="transformer_forecast",
        dbconfig={
            "server": "localhost",
            "database": "timeseries_db",
            "username": "user",
            "password": "password"
        },
        local_test_mode=False
    )
    
    # 配置 Transformer 模型参数
    trainer.algorithm_name = "Transformer"
    trainer.model_parameter = {
        "seq_len": 96,      # 输入序列长度
        "label_len": 48,    # 标签序列长度
        "pred_len": 24,     # 预测序列长度
        "enc_in": 7,        # 编码器输入维度
        "dec_in": 7,        # 解码器输入维度
        "c_out": 7,         # 输出维度
        "d_model": 512,     # 模型维度
        "n_heads": 8,       # 注意力头数
        "e_layers": 2,      # 编码器层数
        "d_layers": 1,      # 解码器层数
        "d_ff": 2048,       # 前馈网络维度
        "dropout": 0.1,     # Dropout 率
        "activation": 'gelu' # 激活函数
    }
    
    # 配置训练参数
    trainer.batch_size = 32
    trainer.learning_rate = 0.0001
    trainer.num_epochs = 100
    trainer.teacher_forcing_ratio = 0.5
    
    # 异步执行训练
    await trainer.main()

# 运行异步训练
asyncio.run(main())
```

### 2. Informer 模型训练
```python
async def main_informer():
    trainer = AsyncTimeSeriesSequenceTrainer(
        project_name="informer_forecast",
        local_test_mode=True
    )
    
    # 配置 Informer 模型参数
    trainer.algorithm_name = "Informer"
    trainer.model_parameter = {
        "seq_len": 96,
        "label_len": 48,
        "pred_len": 24,
        "enc_in": 7,
        "dec_in": 7,
        "c_out": 7,
        "d_model": 512,
        "n_heads": 8,
        "e_layers": 2,
        "d_layers": 1,
        "d_ff": 2048,
        "dropout": 0.1,
        "factor": 1,        # ProbSparse 注意力因子
        "distil": True,     # 使用蒸馏
        "activation": 'gelu'
    }
    
    # 启用混合精度训练
    trainer.use_amp = True
    trainer.gradient_clip_val = 1.0
    
    await trainer.main()

asyncio.run(main_informer())
```

### 3. 多变量长期预测
```python
async def main_multivariate_long_term():
    trainer = AsyncTimeSeriesSequenceTrainer(
        project_name="multivariate_long_forecast",
        local_test_mode=True
    )
    
    # 配置长期预测参数
    trainer.algorithm_name = "Autoformer"
    trainer.model_parameter = {
        "seq_len": 336,     # 更长的输入序列
        "label_len": 168,   # 更长的标签序列
        "pred_len": 96,     # 长期预测
        "enc_in": 21,       # 更多输入变量
        "dec_in": 21,
        "c_out": 21,
        "d_model": 512,
        "n_heads": 8,
        "e_layers": 2,
        "d_layers": 1,
        "d_ff": 2048,
        "dropout": 0.1,
        "moving_avg": 25,   # Autoformer 移动平均窗口
        "activation": 'gelu'
    }
    
    # 配置输入输出变量
    trainer.input_name_list = [
        'temperature', 'humidity', 'pressure', 'wind_speed',
        'solar_radiation', 'precipitation', 'cloud_cover',
        'visibility', 'uv_index', 'air_quality',
        'electricity_demand', 'gas_consumption', 'water_usage',
        'traffic_flow', 'population_density', 'economic_index',
        'stock_price', 'exchange_rate', 'commodity_price',
        'social_media_sentiment', 'news_sentiment'
    ]
    
    trainer.output_name_list = [
        'electricity_demand', 'gas_consumption', 'water_usage',
        'traffic_flow', 'air_quality', 'stock_price',
        'exchange_rate', 'commodity_price', 'economic_index',
        'temperature', 'humidity', 'pressure',
        'wind_speed', 'solar_radiation', 'precipitation',
        'cloud_cover', 'visibility', 'uv_index',
        'population_density', 'social_media_sentiment', 'news_sentiment'
    ]
    
    await trainer.main()

asyncio.run(main_multivariate_long_term())
```

### 4. 注意力分析和可视化
```python
async def main_with_attention_analysis():
    trainer = AsyncTimeSeriesSequenceTrainer(
        project_name="attention_analysis",
        local_test_mode=True
    )
    
    # 配置模型参数
    trainer.algorithm_name = "Transformer"
    trainer.model_parameter = {
        "seq_len": 96,
        "label_len": 48,
        "pred_len": 24,
        "enc_in": 7,
        "dec_in": 7,
        "c_out": 7,
        "d_model": 512,
        "n_heads": 8,
        "e_layers": 2,
        "d_layers": 1,
        "d_ff": 2048,
        "dropout": 0.1
    }
    
    # 启用注意力分析
    trainer.save_attention = True
    trainer.visualize_attention_weights = True
    
    await trainer.main()
    
    # 训练完成后进行注意力分析
    test_data = await trainer._get_data("test")
    sample_batch = next(iter(test_data[1]))
    
    attention_analysis = await trainer.visualize_attention(
        sample_batch['input'],
        sample_batch['target']
    )
    
    print("注意力分析完成:")
    print(f"- 编码器注意力形状: {attention_analysis['encoder_attention'].shape}")
    print(f"- 解码器注意力形状: {attention_analysis['decoder_attention'].shape}")
    print(f"- 交叉注意力形状: {attention_analysis['cross_attention'].shape}")

asyncio.run(main_with_attention_analysis())
```

### 5. 并发多模型训练
```python
async def concurrent_model_training():
    """并发训练多个序列模型"""
    
    # 定义多个模型配置
    model_configs = [
        {
            "project_name": "transformer_short",
            "algorithm_name": "Transformer",
            "pred_len": 24,
            "d_model": 256
        },
        {
            "project_name": "informer_medium",
            "algorithm_name": "Informer",
            "pred_len": 48,
            "d_model": 512
        },
        {
            "project_name": "autoformer_long",
            "algorithm_name": "Autoformer",
            "pred_len": 96,
            "d_model": 512
        }
    ]
    
    # 创建训练任务
    tasks = []
    for config in model_configs:
        trainer = AsyncTimeSeriesSequenceTrainer(
            project_name=config["project_name"],
            local_test_mode=True
        )
        
        trainer.algorithm_name = config["algorithm_name"]
        trainer.model_parameter = {
            "seq_len": 96,
            "label_len": 48,
            "pred_len": config["pred_len"],
            "enc_in": 7,
            "dec_in": 7,
            "c_out": 7,
            "d_model": config["d_model"],
            "n_heads": 8,
            "e_layers": 2,
            "d_layers": 1,
            "d_ff": config["d_model"] * 4,
            "dropout": 0.1
        }
        
        tasks.append(trainer.main())
    
    # 并发执行所有训练任务
    await asyncio.gather(*tasks)
    print("所有序列模型训练完成")

asyncio.run(concurrent_model_training())
```

## 性能优化

### 1. 内存优化
```python
# 梯度检查点和内存优化
class MemoryOptimizedAsyncTrainer(AsyncTimeSeriesSequenceTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.gradient_checkpointing = True
        self.max_memory_usage = 0.8  # 最大内存使用率
    
    async def train_with_memory_management(self):
        for epoch in range(self.num_epochs):
            # 监控内存使用
            if torch.cuda.is_available():
                memory_usage = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
                if memory_usage > self.max_memory_usage:
                    torch.cuda.empty_cache()
            
            # 训练一个epoch
            train_loss = await self.train()
            
            # 验证
            val_loss = await self.vali()
            
            # 记录指标
            self.writer.add_scalar('Loss/Train', train_loss, epoch)
            self.writer.add_scalar('Loss/Validation', val_loss, epoch)
            self.writer.add_scalar('Memory/Usage', memory_usage, epoch)
```

### 2. 分布式训练优化
```python
# 分布式异步训练
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

class DistributedAsyncTrainer(AsyncTimeSeriesSequenceTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.world_size = int(os.environ.get('WORLD_SIZE', 1))
        self.rank = int(os.environ.get('RANK', 0))
        self.local_rank = int(os.environ.get('LOCAL_RANK', 0))
    
    async def setup_distributed(self):
        if self.world_size > 1:
            dist.init_process_group(backend='nccl')
            torch.cuda.set_device(self.local_rank)
            self.device = torch.device(f'cuda:{self.local_rank}')
            
            # 包装模型为分布式模型
            self.model = DDP(self.model, device_ids=[self.local_rank])
    
    async def train_distributed(self):
        await self.setup_distributed()
        
        # 分布式训练循环
        for epoch in range(self.num_epochs):
            if self.world_size > 1:
                self.train_sampler.set_epoch(epoch)
            
            train_loss = await self.train()
            
            # 同步所有进程的损失
            if self.world_size > 1:
                dist.all_reduce(train_loss, op=dist.ReduceOp.AVG)
            
            if self.rank == 0:
                print(f"Epoch {epoch}, Loss: {train_loss:.4f}")
```

### 3. 动态批处理优化
```python
# 动态批处理大小调整
class DynamicBatchAsyncTrainer(AsyncTimeSeriesSequenceTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.initial_batch_size = self.batch_size
        self.max_batch_size = self.batch_size * 4
        self.min_batch_size = self.batch_size // 2
    
    async def adjust_batch_size(self, memory_usage, loss_trend):
        """根据内存使用和损失趋势动态调整批处理大小"""
        if memory_usage > 0.9 and self.batch_size > self.min_batch_size:
            # 内存不足,减小批处理大小
            self.batch_size = max(self.batch_size // 2, self.min_batch_size)
            print(f"减小批处理大小到: {self.batch_size}")
        elif memory_usage < 0.6 and loss_trend < 0 and self.batch_size < self.max_batch_size:
            # 内存充足且损失下降,增大批处理大小
            self.batch_size = min(self.batch_size * 2, self.max_batch_size)
            print(f"增大批处理大小到: {self.batch_size}")
    
    async def train_with_dynamic_batch(self):
        loss_history = []
        
        for epoch in range(self.num_epochs):
            # 监控内存使用
            memory_usage = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
            
            # 计算损失趋势
            loss_trend = 0
            if len(loss_history) >= 2:
                loss_trend = loss_history[-1] - loss_history[-2]
            
            # 调整批处理大小
            await self.adjust_batch_size(memory_usage, loss_trend)
            
            # 重新创建数据加载器
            train_dataset, train_loader = await self._get_data("train")
            
            # 训练
            train_loss = await self.train()
            loss_history.append(train_loss)
            
            # 验证
            val_loss = await self.vali()
```

## 监控和调试

### 1. 实时训练监控
```python
# 实时监控训练状态
async def monitor_sequence_training(trainer):
    """监控序列训练状态"""
    while trainer.is_training:
        status = await trainer.get_training_status()
        
        print(f"Epoch: {status['epoch']}")
        print(f"Train Loss: {status['train_loss']:.4f}")
        print(f"Val Loss: {status['val_loss']:.4f}")
        print(f"Learning Rate: {status['learning_rate']:.6f}")
        print(f"Teacher Forcing Ratio: {status['teacher_forcing_ratio']:.2f}")
        print(f"Attention Entropy: {status['attention_entropy']:.4f}")
        print("-" * 50)
        
        await asyncio.sleep(5)

# 在训练过程中启动监控
async def main_with_monitoring():
    trainer = AsyncTimeSeriesSequenceTrainer(
        project_name="monitored_sequence_training",
        local_test_mode=True
    )
    
    # 启动监控任务
    monitor_task = asyncio.create_task(monitor_sequence_training(trainer))
    
    # 启动训练
    await trainer.main()
    
    # 停止监控
    monitor_task.cancel()
```

### 2. 注意力权重分析
```python
# 注意力权重分析工具
class AttentionAnalyzer:
    def __init__(self, trainer):
        self.trainer = trainer
    
    async def analyze_attention_patterns(self, data_loader):
        """分析注意力模式"""
        attention_stats = {
            'encoder_entropy': [],
            'decoder_entropy': [],
            'cross_entropy': [],
            'head_diversity': [],
            'layer_consistency': []
        }
        
        for batch in data_loader:
            with torch.no_grad():
                outputs = self.trainer.model(batch['input'], batch['target'])
                attentions = outputs['attentions']
                
                # 计算注意力熵
                for layer_idx, layer_attention in enumerate(attentions['encoder']):
                    entropy = self._calculate_attention_entropy(layer_attention)
                    attention_stats['encoder_entropy'].append(entropy)
                
                # 计算注意力头多样性
                diversity = self._calculate_head_diversity(attentions['encoder'][0])
                attention_stats['head_diversity'].append(diversity)
        
        return attention_stats
    
    def _calculate_attention_entropy(self, attention_weights):
        """计算注意力权重的熵"""
        # attention_weights: [batch_size, num_heads, seq_len, seq_len]
        entropy = -torch.sum(attention_weights * torch.log(attention_weights + 1e-8), dim=-1)
        return entropy.mean().item()
    
    def _calculate_head_diversity(self, attention_weights):
        """计算注意力头之间的多样性"""
        # 计算不同注意力头之间的相似性
        num_heads = attention_weights.size(1)
        similarities = []
        
        for i in range(num_heads):
            for j in range(i+1, num_heads):
                sim = torch.cosine_similarity(
                    attention_weights[:, i].flatten(),
                    attention_weights[:, j].flatten(),
                    dim=0
                )
                similarities.append(sim.item())
        
        # 多样性 = 1 - 平均相似性
        diversity = 1 - np.mean(similarities)
        return diversity
```

### 3. 性能分析和优化
```python
# 性能分析工具
import cProfile
import pstats
from torch.profiler import profile, record_function, ProfilerActivity

class PerformanceProfiler:
    def __init__(self, trainer):
        self.trainer = trainer
    
    async def profile_training_step(self):
        """分析单个训练步骤的性能"""
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            with record_function("training_step"):
                train_loss = await self.trainer.train()
        
        # 输出性能分析结果
        print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=10))
        
        # 保存性能分析结果
        prof.export_chrome_trace(f"{self.trainer.project_name}_trace.json")
        
        return train_loss
    
    async def memory_profiling(self):
        """内存使用分析"""
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            
            # 执行一个训练步骤
            await self.trainer.train()
            
            # 获取内存统计
            memory_stats = {
                'allocated': torch.cuda.memory_allocated(),
                'cached': torch.cuda.memory_reserved(),
                'max_allocated': torch.cuda.max_memory_allocated(),
                'max_cached': torch.cuda.max_memory_reserved()
            }
            
            print("内存使用统计:")
            for key, value in memory_stats.items():
                print(f"{key}: {value / 1024**3:.2f} GB")
            
            return memory_stats
```

## 最佳实践

### 1. 序列长度优化
```python
# 动态序列长度调整
class AdaptiveSequenceTrainer(AsyncTimeSeriesSequenceTrainer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.adaptive_seq_len = True
        self.min_seq_len = 24
        self.max_seq_len = 336
    
    async def optimize_sequence_length(self, data_characteristics):
        """根据数据特征优化序列长度"""
        # 分析数据的周期性和趋势
        seasonality = data_characteristics.get('seasonality', 24)
        trend_strength = data_characteristics.get('trend_strength', 0.5)
        noise_level = data_characteristics.get('noise_level', 0.1)
        
        # 根据数据特征调整序列长度
        if seasonality > 0:
            optimal_seq_len = max(seasonality * 2, self.min_seq_len)
        else:
            optimal_seq_len = self.min_seq_len
        
        # 考虑趋势强度
        if trend_strength > 0.7:
            optimal_seq_len = min(optimal_seq_len * 2, self.max_seq_len)
        
        # 考虑噪声水平
        if noise_level > 0.3:
            optimal_seq_len = max(optimal_seq_len // 2, self.min_seq_len)
        
        self.seq_len = optimal_seq_len
        self.label_len = optimal_seq_len // 2
        
        print(f"优化后的序列长度: {self.seq_len}")
```

### 2. 学习率调度优化
```python
# 智能学习率调度
class SmartLRScheduler:
    def __init__(self, optimizer, patience=10, factor=0.5, min_lr=1e-7):
        self.optimizer = optimizer
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.best_loss = float('inf')
        self.wait = 0
        self.lr_history = []
    
    def step(self, val_loss):
        """根据验证损失调整学习率"""
        current_lr = self.optimizer.param_groups[0]['lr']
        self.lr_history.append(current_lr)
        
        if val_loss < self.best_loss:
            self.best_loss = val_loss
            self.wait = 0
        else:
            self.wait += 1
            
            if self.wait >= self.patience:
                new_lr = max(current_lr * self.factor, self.min_lr)
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = new_lr
                
                print(f"学习率调整: {current_lr:.6f} -> {new_lr:.6f}")
                self.wait = 0
    
    def get_lr_trend(self):
        """获取学习率变化趋势"""
        if len(self.lr_history) < 2:
            return 0
        return self.lr_history[-1] - self.lr_history[-2]
```

### 3. 模型集成和投票
```python
# 模型集成训练
class EnsembleAsyncTrainer:
    def __init__(self, model_configs):
        self.trainers = []
        for config in model_configs:
            trainer = AsyncTimeSeriesSequenceTrainer(**config)
            self.trainers.append(trainer)
    
    async def train_ensemble(self):
        """并发训练多个模型"""
        tasks = [trainer.main() for trainer in self.trainers]
        await asyncio.gather(*tasks)
    
    async def ensemble_predict(self, input_data):
        """集成预测"""
        predictions = []
        
        for trainer in self.trainers:
            pred = await trainer.predict(input_data)
            predictions.append(pred)
        
        # 简单平均集成
        ensemble_pred = torch.stack(predictions).mean(dim=0)
        
        # 加权平均集成(根据验证性能)
        weights = [trainer.val_performance for trainer in self.trainers]
        weights = torch.softmax(torch.tensor(weights), dim=0)
        
        weighted_pred = sum(w * p for w, p in zip(weights, predictions))
        
        return {
            'simple_average': ensemble_pred,
            'weighted_average': weighted_pred,
            'individual_predictions': predictions
        }
```

## 相关文档

- [异步基础训练器](async_basic_trainer.md)
- [异步时序经典训练器](async_time_series_classic_trainer.md)
- [异步训练管道](async_training_pipeline.md)
- [使用示例](../usage_examples.md)