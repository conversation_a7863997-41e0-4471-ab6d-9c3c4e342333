# 异步模型训练器概述

异步模型训练器是 `industrytslib` 库中专门为解决训练任务阻塞问题而设计的核心组件。它们基于 `AsyncScheduledTask` 基类,提供完整的异步模型训练流程支持。

## 设计理念

### 异步优先
异步训练器采用 `async/await` 语法,确保所有 I/O 密集型操作(数据库查询、文件读写、网络请求)都是非阻塞的,从而提高系统整体性能和响应性。

### 继承层次
```
AsyncScheduledTask (基础异步任务)
└── AsyncModelTrainer (异步模型训练器基类)
    ├── AsyncTimeSeriesClassicTrainer (异步时序经典训练器)
    └── AsyncTimeSeriesSequenceTrainer (异步时序序列训练器)
```

## 核心特性

### 1. 异步数据库连接
- **连接池管理**: 自动管理异步数据库连接池
- **在线/离线模式**: 支持在线数据库和离线文件数据源
- **连接复用**: 高效的连接资源管理

### 2. 并发数据处理
- **异步数据获取**: 非阻塞的数据查询和加载
- **并发预处理**: 多任务并行数据预处理
- **流式处理**: 支持大数据集的流式处理

### 3. 智能资源管理
- **GPU/CPU 自动检测**: 智能设备选择和配置
- **内存优化**: 异步内存管理和垃圾回收
- **资源清理**: 自动资源释放和清理

### 4. 实时监控和可视化
- **TensorBoard 集成**: 实时训练指标监控
- **异步日志记录**: 非阻塞的日志写入
- **进度跟踪**: 实时训练进度反馈

## 训练器类型

### AsyncModelTrainer (基础训练器)
**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_basic_trainer.py`

基础异步训练器提供所有异步训练器的通用功能:

- **项目配置管理**: 项目名称、数据库配置初始化
- **异步数据库连接**: 支持多种数据库的异步连接
- **设备管理**: GPU/CPU 设备自动检测和配置
- **路径管理**: 训练结果、模型保存路径管理
- **工具初始化**: TensorBoard、绘图器等工具配置
- **属性初始化**: 模型、优化器、数据等属性初始化

**主要方法**:
- `_setup_device()`: 设备配置
- `_setup_paths()`: 路径配置
- `_setup_tools()`: 工具初始化
- `_init_training_attributes()`: 训练属性初始化

### AsyncTimeSeriesClassicTrainer (异步时序经典训练器)
**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_time_series_classic_trainer.py`

专门用于训练经典时序模型(LSTM、GRU 等)的异步训练器:

**核心功能**:
- **异步模型构建**: 支持多种经典时序模型
- **并发数据增强**: 异步数据增强处理
- **异步训练循环**: 非阻塞的训练过程
- **异步模型评估**: 并发指标计算和评估
- **早停策略**: 智能训练停止机制

**特色功能**:
- 支持数据增强配置
- 异步 scaler 处理
- 多输出模型支持
- 训练历史记录

### AsyncTimeSeriesSequenceTrainer (异步时序序列训练器)
**文件路径**: `src/industrytslib/core_aysnc/async_model_trainers/async_time_series_trainer.py`

专门用于训练序列到序列模型(Transformer 等)的异步训练器:

**核心功能**:
- **异步序列模型构建**: 支持 Transformer 架构及变体
- **异步序列数据处理**: 专门的序列数据处理流程
- **异步批次处理**: 优化的批次数据处理
- **异步模型评估**: 多种序列评估指标
- **学习率调度**: 动态学习率调整

**特色功能**:
- MOE 模型支持
- 注意力输出模型支持
- 多变量时序预测
- 序列特化指标计算

## 使用模式

### 1. 直接实例化
```python
# 创建异步时序经典训练器
trainer = AsyncTimeSeriesClassicTrainer(
    project_name="my_project",
    dbconfig=db_config,
    local_test_mode=False
)

# 异步执行训练
await trainer.main()
```

### 2. 通过训练管道创建
```python
# 使用异步训练管道
trainer = await create_async_trainer(
    project_name="my_project",
    model_type="async_time_series_classic",
    dbconfig=db_config,
    batch_size=32,
    learning_rate=0.001
)

# 异步执行训练
await trainer.main()
```

### 3. 建造者模式
```python
# 使用建造者模式配置训练器
trainer = await AsyncTrainerBuilder(
    project_name="my_project",
    model_type="async_time_series_sequence"
).with_db_config(db_config)\
 .with_batch_size(64)\
 .with_learning_rate(0.0001)\
 .with_epochs(100)\
 .with_device("cuda")\
 .build()

# 异步执行训练
await trainer.main()
```

## 性能优势

### 1. 并发处理能力
- **多任务并行**: 同时处理多个训练任务
- **I/O 并发**: 数据库查询和文件操作并发执行
- **计算与 I/O 重叠**: 计算和数据加载同时进行

### 2. 资源利用效率
- **内存优化**: 异步内存管理减少峰值内存使用
- **CPU 利用率**: 更好的 CPU 资源分配
- **GPU 利用率**: 优化的 GPU 内存管理

### 3. 响应性提升
- **非阻塞执行**: 不阻塞主线程或事件循环
- **实时反馈**: 训练过程实时状态更新
- **可中断性**: 支持优雅的训练中断和恢复

## 最佳实践

### 1. 错误处理
```python
try:
    trainer = await create_async_trainer(
        project_name="my_project",
        model_type="async_time_series_classic",
        dbconfig=db_config
    )
    await trainer.main()
except Exception as e:
    logger.error(f"训练失败: {e}")
    # 清理资源
    await trainer.cleanup()
```

### 2. 资源管理
```python
# 使用上下文管理器确保资源清理
async with AsyncTrainerContext(
    project_name="my_project",
    model_type="async_time_series_classic"
) as trainer:
    await trainer.main()
# 自动清理资源
```

### 3. 并发控制
```python
# 限制并发训练任务数量
semaphore = asyncio.Semaphore(3)  # 最多3个并发任务

async def train_with_limit(project_config):
    async with semaphore:
        trainer = await create_async_trainer(**project_config)
        await trainer.main()

# 批量训练
tasks = [train_with_limit(config) for config in project_configs]
await asyncio.gather(*tasks)
```

## 监控和调试

### 1. 日志配置
```python
# 配置异步训练器日志
trainer = AsyncTimeSeriesClassicTrainer(
    project_name="my_project",
    dbconfig=db_config,
    log_level="DEBUG",
    console_level="INFO"
)
```

### 2. 性能监控
```python
# 启用性能监控
import asyncio
import time

start_time = time.time()
trainer = await create_async_trainer(
    project_name="my_project",
    model_type="async_time_series_classic"
)
await trainer.main()
end_time = time.time()

print(f"训练耗时: {end_time - start_time:.2f} 秒")
```

### 3. 内存监控
```python
# 监控内存使用
import psutil
import asyncio

async def monitor_memory():
    while True:
        memory = psutil.virtual_memory()
        print(f"内存使用率: {memory.percent}%")
        await asyncio.sleep(10)

# 在训练过程中监控内存
async def train_with_monitoring():
    monitor_task = asyncio.create_task(monitor_memory())
    trainer = await create_async_trainer(
        project_name="my_project",
        model_type="async_time_series_classic"
    )
    await trainer.main()
    monitor_task.cancel()
```

## 扩展开发

### 自定义异步训练器
```python
class CustomAsyncTrainer(AsyncModelTrainer):
    """自定义异步训练器"""
    
    def __init__(self, project_name, dbconfig, **kwargs):
        super().__init__(project_name, dbconfig, **kwargs)
        self.task_type = "CustomAsyncTrainer"
    
    async def build_model(self, model_parameter):
        """异步构建自定义模型"""
        # 实现自定义模型构建逻辑
        pass
    
    async def train(self):
        """异步训练方法"""
        # 实现自定义训练逻辑
        pass
    
    async def evaluate(self):
        """异步评估方法"""
        # 实现自定义评估逻辑
        pass
```

## 相关文档

- [异步基础训练器详细文档](async_basic_trainer.md)
- [异步时序经典训练器详细文档](async_time_series_classic_trainer.md)
- [异步时序序列训练器详细文档](async_time_series_sequence_trainer.md)
- [异步训练管道文档](async_training_pipeline.md)
- [使用示例](../usage_examples.md)