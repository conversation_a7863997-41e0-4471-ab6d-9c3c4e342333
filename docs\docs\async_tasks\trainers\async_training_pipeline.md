# 异步训练管道 (Async Training Pipeline)

异步训练管道是 `industrytslib` 库中用于构建和管理异步模型训练器的核心组件。它提供了统一的接口来创建、配置和管理不同类型的异步训练器,支持类型安全的训练器实例化和灵活的配置管理。

## 文件位置

**文件路径**: `src/industrytslib/core_aysnc/async_training_pipeline.py`

## 核心组件

### 1. 训练器类型映射 (ASYNC_TRAINER_CLASS_MAP)

```python
ASYNC_TRAINER_CLASS_MAP: Dict[str, Type[AsyncModelTrainer]] = {
    "async_time_series_sequence": AsyncTimeSeriesSequenceTrainer,
    "async_time_series_classic": AsyncTimeSeriesClassicTrainer,
}
```

**功能**:
- 定义了可用的异步训练器类型
- 提供训练器名称到类的映射关系
- 支持动态扩展新的训练器类型
- 确保类型安全的训练器创建

**支持的训练器类型**:

| 训练器类型 | 对应类 | 适用场景 |
|------------|--------|----------|
| `async_time_series_sequence` | `AsyncTimeSeriesSequenceTrainer` | Transformer、Informer 等序列模型 |
| `async_time_series_classic` | `AsyncTimeSeriesClassicTrainer` | LSTM、GRU 等经典时序模型 |

### 2. 异步训练器建造者 (AsyncTrainerBuilder)

```python
class AsyncTrainerBuilder:
    """异步训练器建造者类"""
```

**设计模式**: 建造者模式 (Builder Pattern)

**核心功能**:
- 提供流式接口配置训练器参数
- 支持方法链式调用
- 异步构建训练器实例
- 参数验证和错误处理
- 灵活的配置管理

#### 初始化方法

```python
def __init__(self, trainer_type: str) -> None:
    """初始化异步训练器建造者
    
    Args:
        trainer_type: 训练器类型,必须在 ASYNC_TRAINER_CLASS_MAP 中定义
    
    Raises:
        ValueError: 当训练器类型不支持时抛出
    """
```

**参数说明**:
- `trainer_type`: 训练器类型字符串,必须是 `ASYNC_TRAINER_CLASS_MAP` 中的有效键

**使用示例**:
```python
# 创建时序序列训练器建造者
builder = AsyncTrainerBuilder("async_time_series_sequence")

# 创建时序经典训练器建造者
builder = AsyncTrainerBuilder("async_time_series_classic")
```

#### 配置方法

##### 项目配置
```python
def with_project_name(self, project_name: str) -> "AsyncTrainerBuilder":
    """设置项目名称"""
```

##### 数据库配置
```python
def with_database_config(self, dbconfig: Dict[str, Any]) -> "AsyncTrainerBuilder":
    """设置数据库配置"""
```

##### 测试模式配置
```python
def with_local_test_mode(self, local_test_mode: bool = True) -> "AsyncTrainerBuilder":
    """设置本地测试模式"""
```

##### 模型参数配置
```python
def with_model_parameters(self, **model_params) -> "AsyncTrainerBuilder":
    """设置模型参数"""
```

##### 训练参数配置
```python
def with_training_parameters(self, **training_params) -> "AsyncTrainerBuilder":
    """设置训练参数"""
```

##### 自定义配置
```python
def with_custom_config(self, **custom_params) -> "AsyncTrainerBuilder":
    """设置自定义配置参数"""
```

#### 构建方法

```python
async def build(self) -> AsyncModelTrainer:
    """异步构建训练器实例
    
    Returns:
        AsyncModelTrainer: 配置好的异步训练器实例
    
    Raises:
        ValueError: 当训练器类型未知时抛出
        Exception: 当训练器构建失败时抛出
    """
```

**功能**:
- 异步创建训练器实例
- 应用所有配置参数
- 验证配置的有效性
- 处理构建过程中的错误
- 返回完全配置的训练器实例

**实现流程**:
1. 验证训练器类型的有效性
2. 从映射表获取对应的训练器类
3. 异步创建训练器实例
4. 应用所有配置参数
5. 验证配置的完整性
6. 返回配置好的训练器

### 3. 便捷创建函数 (create_async_trainer)

#### 函数重载定义

```python
# 重载1: 时序序列训练器
@overload
async def create_async_trainer(
    trainer_type: Literal["async_time_series_sequence"],
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> AsyncTimeSeriesSequenceTrainer: ...

# 重载2: 时序经典训练器
@overload
async def create_async_trainer(
    trainer_type: Literal["async_time_series_classic"],
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> AsyncTimeSeriesClassicTrainer: ...

# 重载3: 通用训练器
@overload
async def create_async_trainer(
    trainer_type: str,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> AsyncModelTrainer: ...
```

#### 实现函数

```python
async def create_async_trainer(
    trainer_type: str,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> AsyncModelTrainer:
    """异步创建训练器实例
    
    这是一个便捷函数,提供了简化的异步训练器创建接口。
    支持类型安全的训练器实例化,并提供完整的参数验证。
    
    Args:
        trainer_type: 训练器类型,支持的类型见 ASYNC_TRAINER_CLASS_MAP
        project_name: 项目名称,用于标识和管理训练任务
        dbconfig: 数据库配置字典,包含连接参数
        local_test_mode: 是否启用本地测试模式
        **kwargs: 其他配置参数,将传递给训练器
    
    Returns:
        AsyncModelTrainer: 配置好的异步训练器实例
    
    Raises:
        ValueError: 当参数无效时抛出
        TypeError: 当参数类型错误时抛出
        Exception: 当创建失败时抛出
    
    Example:
        >>> # 创建时序序列训练器
        >>> trainer = await create_async_trainer(
        ...     trainer_type="async_time_series_sequence",
        ...     project_name="transformer_forecast",
        ...     dbconfig={"server": "localhost", "database": "timeseries"},
        ...     algorithm_name="Transformer",
        ...     seq_len=96,
        ...     pred_len=24
        ... )
        
        >>> # 创建时序经典训练器
        >>> trainer = await create_async_trainer(
        ...     trainer_type="async_time_series_classic",
        ...     project_name="lstm_forecast",
        ...     local_test_mode=True,
        ...     algorithm_name="LSTM",
        ...     hidden_size=64
        ... )
    """
```

**功能特点**:
- **类型安全**: 通过函数重载提供类型安全的接口
- **参数验证**: 自动验证输入参数的有效性
- **错误处理**: 提供详细的错误信息和异常处理
- **灵活配置**: 支持通过 kwargs 传递任意配置参数
- **异步创建**: 完全异步的训练器创建过程

## 使用示例

### 1. 使用建造者模式创建训练器

```python
import asyncio
from industrytslib.core_aysnc.async_training_pipeline import AsyncTrainerBuilder

async def create_trainer_with_builder():
    """使用建造者模式创建异步训练器"""
    
    # 创建时序序列训练器
    sequence_trainer = await (
        AsyncTrainerBuilder("async_time_series_sequence")
        .with_project_name("transformer_forecast")
        .with_database_config({
            "server": "localhost",
            "database": "timeseries_db",
            "username": "user",
            "password": "password"
        })
        .with_model_parameters(
            algorithm_name="Transformer",
            seq_len=96,
            label_len=48,
            pred_len=24,
            enc_in=7,
            dec_in=7,
            c_out=7,
            d_model=512,
            n_heads=8,
            e_layers=2,
            d_layers=1
        )
        .with_training_parameters(
            batch_size=32,
            learning_rate=0.0001,
            num_epochs=100,
            teacher_forcing_ratio=0.5
        )
        .with_custom_config(
            use_amp=True,
            gradient_clip_val=1.0,
            save_attention=True
        )
        .build()
    )
    
    print(f"创建的训练器类型: {type(sequence_trainer)}")
    print(f"项目名称: {sequence_trainer.project_name}")
    print(f"算法名称: {sequence_trainer.algorithm_name}")
    
    return sequence_trainer

# 运行示例
trainer = asyncio.run(create_trainer_with_builder())
```

### 2. 使用便捷函数创建训练器

```python
from industrytslib.core_aysnc.async_training_pipeline import create_async_trainer

async def create_trainer_with_function():
    """使用便捷函数创建异步训练器"""
    
    # 创建时序经典训练器
    classic_trainer = await create_async_trainer(
        trainer_type="async_time_series_classic",
        project_name="lstm_forecast",
        dbconfig={
            "server": "localhost",
            "database": "timeseries_db",
            "username": "user",
            "password": "password"
        },
        local_test_mode=False,
        # 模型参数
        algorithm_name="LSTM",
        model_parameter={
            "input_size": 10,
            "hidden_size": 64,
            "num_layers": 2,
            "output_size": 1,
            "dropout": 0.1
        },
        # 训练参数
        batch_size=32,
        learning_rate=0.001,
        num_epochs=100,
        # 数据增强参数
        apply_aug=True,
        aug_config={
            "gaussian_noise": {
                "enabled": True,
                "std": 0.01,
                "probability": 0.5
            }
        },
        # 早停参数
        patience=50,
        early_stopping_delta=0.0001
    )
    
    print(f"创建的训练器类型: {type(classic_trainer)}")
    print(f"项目名称: {classic_trainer.project_name}")
    print(f"算法名称: {classic_trainer.algorithm_name}")
    
    return classic_trainer

# 运行示例
trainer = asyncio.run(create_trainer_with_function())
```

### 3. 批量创建多个训练器

```python
async def create_multiple_trainers():
    """批量创建多个异步训练器"""
    
    # 定义训练器配置
    trainer_configs = [
        {
            "trainer_type": "async_time_series_sequence",
            "project_name": "transformer_short_term",
            "algorithm_name": "Transformer",
            "pred_len": 24,
            "d_model": 256
        },
        {
            "trainer_type": "async_time_series_sequence",
            "project_name": "informer_medium_term",
            "algorithm_name": "Informer",
            "pred_len": 48,
            "d_model": 512
        },
        {
            "trainer_type": "async_time_series_classic",
            "project_name": "lstm_long_term",
            "algorithm_name": "LSTM",
            "hidden_size": 128
        },
        {
            "trainer_type": "async_time_series_classic",
            "project_name": "gru_ensemble",
            "algorithm_name": "GRU",
            "hidden_size": 64
        }
    ]
    
    # 并发创建所有训练器
    trainers = await asyncio.gather(*[
        create_async_trainer(
            trainer_type=config["trainer_type"],
            project_name=config["project_name"],
            local_test_mode=True,
            **{k: v for k, v in config.items() if k not in ["trainer_type", "project_name"]}
        )
        for config in trainer_configs
    ])
    
    print(f"成功创建 {len(trainers)} 个训练器:")
    for trainer in trainers:
        print(f"- {trainer.project_name}: {type(trainer).__name__}")
    
    return trainers

# 运行示例
trainers = asyncio.run(create_multiple_trainers())
```

### 4. 动态配置和条件创建

```python
async def create_trainer_dynamically(data_characteristics):
    """根据数据特征动态创建训练器"""
    
    # 分析数据特征
    data_size = data_characteristics.get('size', 1000)
    seasonality = data_characteristics.get('seasonality', 24)
    trend_strength = data_characteristics.get('trend_strength', 0.5)
    noise_level = data_characteristics.get('noise_level', 0.1)
    num_variables = data_characteristics.get('num_variables', 1)
    
    # 根据数据特征选择训练器类型
    if num_variables > 10 and seasonality > 24:
        # 多变量长周期数据,使用 Transformer
        trainer_type = "async_time_series_sequence"
        algorithm_name = "Transformer"
        model_params = {
            "seq_len": max(seasonality * 2, 96),
            "pred_len": seasonality,
            "enc_in": num_variables,
            "dec_in": num_variables,
            "c_out": num_variables,
            "d_model": 512,
            "n_heads": 8
        }
    elif trend_strength > 0.7:
        # 强趋势数据,使用 LSTM
        trainer_type = "async_time_series_classic"
        algorithm_name = "LSTM"
        model_params = {
            "input_size": num_variables,
            "hidden_size": 128,
            "num_layers": 3,
            "output_size": num_variables
        }
    else:
        # 一般情况,使用 GRU
        trainer_type = "async_time_series_classic"
        algorithm_name = "GRU"
        model_params = {
            "input_size": num_variables,
            "hidden_size": 64,
            "num_layers": 2,
            "output_size": num_variables
        }
    
    # 根据数据大小调整训练参数
    if data_size < 1000:
        batch_size = 16
        num_epochs = 200
    elif data_size < 10000:
        batch_size = 32
        num_epochs = 100
    else:
        batch_size = 64
        num_epochs = 50
    
    # 根据噪声水平配置数据增强
    apply_aug = noise_level < 0.2
    aug_config = {
        "gaussian_noise": {
            "enabled": apply_aug,
            "std": min(noise_level * 0.5, 0.01),
            "probability": 0.3
        }
    } if apply_aug else {}
    
    # 创建训练器
    trainer = await create_async_trainer(
        trainer_type=trainer_type,
        project_name=f"adaptive_{algorithm_name.lower()}_forecast",
        local_test_mode=True,
        algorithm_name=algorithm_name,
        model_parameter=model_params,
        batch_size=batch_size,
        num_epochs=num_epochs,
        apply_aug=apply_aug,
        aug_config=aug_config
    )
    
    print(f"根据数据特征创建的训练器:")
    print(f"- 训练器类型: {trainer_type}")
    print(f"- 算法名称: {algorithm_name}")
    print(f"- 批次大小: {batch_size}")
    print(f"- 训练轮数: {num_epochs}")
    print(f"- 数据增强: {apply_aug}")
    
    return trainer

# 使用示例
data_chars = {
    'size': 5000,
    'seasonality': 48,
    'trend_strength': 0.8,
    'noise_level': 0.15,
    'num_variables': 15
}

trainer = asyncio.run(create_trainer_dynamically(data_chars))
```

### 5. 配置文件驱动的训练器创建

```python
import yaml
import json
from pathlib import Path

async def create_trainer_from_config(config_path: str):
    """从配置文件创建训练器"""
    
    # 读取配置文件
    config_path = Path(config_path)
    if config_path.suffix == '.yaml' or config_path.suffix == '.yml':
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    elif config_path.suffix == '.json':
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
    
    # 提取基本配置
    trainer_type = config['trainer_type']
    project_name = config['project_name']
    dbconfig = config.get('database', {})
    local_test_mode = config.get('local_test_mode', False)
    
    # 提取其他配置
    model_config = config.get('model', {})
    training_config = config.get('training', {})
    augmentation_config = config.get('augmentation', {})
    
    # 合并所有配置
    all_config = {
        **model_config,
        **training_config,
        **augmentation_config
    }
    
    # 创建训练器
    trainer = await create_async_trainer(
        trainer_type=trainer_type,
        project_name=project_name,
        dbconfig=dbconfig if dbconfig else None,
        local_test_mode=local_test_mode,
        **all_config
    )
    
    print(f"从配置文件 {config_path} 创建训练器成功")
    print(f"- 项目名称: {trainer.project_name}")
    print(f"- 训练器类型: {type(trainer).__name__}")
    
    return trainer

# 配置文件示例 (config.yaml)
"""
trainer_type: "async_time_series_sequence"
project_name: "transformer_config_driven"
local_test_mode: true

database:
  server: "localhost"
  database: "timeseries_db"
  username: "user"
  password: "password"

model:
  algorithm_name: "Transformer"
  seq_len: 96
  label_len: 48
  pred_len: 24
  enc_in: 7
  dec_in: 7
  c_out: 7
  d_model: 512
  n_heads: 8
  e_layers: 2
  d_layers: 1
  d_ff: 2048
  dropout: 0.1
  activation: "gelu"

training:
  batch_size: 32
  learning_rate: 0.0001
  num_epochs: 100
  teacher_forcing_ratio: 0.5
  use_amp: true
  gradient_clip_val: 1.0

augmentation:
  apply_aug: false
"""

# 使用示例
# trainer = asyncio.run(create_trainer_from_config("config.yaml"))
```

## 高级功能

### 1. 训练器工厂类

```python
class AsyncTrainerFactory:
    """异步训练器工厂类"""
    
    def __init__(self):
        self.registered_trainers = ASYNC_TRAINER_CLASS_MAP.copy()
        self.default_configs = {}
    
    def register_trainer(self, name: str, trainer_class: Type[AsyncModelTrainer]):
        """注册新的训练器类型"""
        self.registered_trainers[name] = trainer_class
        print(f"注册新训练器类型: {name}")
    
    def set_default_config(self, trainer_type: str, config: Dict[str, Any]):
        """设置默认配置"""
        self.default_configs[trainer_type] = config
    
    async def create_trainer(
        self, 
        trainer_type: str, 
        project_name: str,
        override_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> AsyncModelTrainer:
        """创建训练器实例"""
        
        if trainer_type not in self.registered_trainers:
            raise ValueError(f"未知的训练器类型: {trainer_type}")
        
        # 合并默认配置和覆盖配置
        config = self.default_configs.get(trainer_type, {}).copy()
        if override_config:
            config.update(override_config)
        config.update(kwargs)
        
        # 创建训练器
        trainer_class = self.registered_trainers[trainer_type]
        trainer = trainer_class(project_name=project_name, **config)
        
        return trainer
    
    def list_available_trainers(self) -> List[str]:
        """列出可用的训练器类型"""
        return list(self.registered_trainers.keys())

# 使用示例
factory = AsyncTrainerFactory()

# 设置默认配置
factory.set_default_config("async_time_series_sequence", {
    "batch_size": 32,
    "learning_rate": 0.0001,
    "num_epochs": 100,
    "d_model": 512,
    "n_heads": 8
})

# 创建训练器
trainer = await factory.create_trainer(
    trainer_type="async_time_series_sequence",
    project_name="factory_created_trainer",
    override_config={"d_model": 256},  # 覆盖默认配置
    algorithm_name="Transformer"
)
```

### 2. 训练器配置验证器

```python
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

@dataclass
class ConfigValidationRule:
    """配置验证规则"""
    field_name: str
    required: bool = False
    data_type: Optional[type] = None
    min_value: Optional[Any] = None
    max_value: Optional[Any] = None
    allowed_values: Optional[List[Any]] = None
    custom_validator: Optional[callable] = None

class AsyncTrainerConfigValidator:
    """异步训练器配置验证器"""
    
    def __init__(self):
        self.validation_rules = {
            "async_time_series_sequence": [
                ConfigValidationRule("algorithm_name", required=True, data_type=str,
                                   allowed_values=["Transformer", "Informer", "Autoformer"]),
                ConfigValidationRule("seq_len", required=True, data_type=int, min_value=1, max_value=1000),
                ConfigValidationRule("pred_len", required=True, data_type=int, min_value=1, max_value=500),
                ConfigValidationRule("d_model", required=True, data_type=int, min_value=64, max_value=2048),
                ConfigValidationRule("n_heads", required=True, data_type=int, min_value=1, max_value=32),
                ConfigValidationRule("learning_rate", data_type=float, min_value=1e-6, max_value=1.0),
                ConfigValidationRule("batch_size", data_type=int, min_value=1, max_value=1024),
            ],
            "async_time_series_classic": [
                ConfigValidationRule("algorithm_name", required=True, data_type=str,
                                   allowed_values=["LSTM", "GRU", "RNN"]),
                ConfigValidationRule("hidden_size", required=True, data_type=int, min_value=8, max_value=1024),
                ConfigValidationRule("num_layers", required=True, data_type=int, min_value=1, max_value=10),
                ConfigValidationRule("learning_rate", data_type=float, min_value=1e-6, max_value=1.0),
                ConfigValidationRule("batch_size", data_type=int, min_value=1, max_value=1024),
                ConfigValidationRule("dropout", data_type=float, min_value=0.0, max_value=0.9),
            ]
        }
    
    def validate_config(self, trainer_type: str, config: Dict[str, Any]) -> List[str]:
        """验证配置"""
        errors = []
        
        if trainer_type not in self.validation_rules:
            errors.append(f"未知的训练器类型: {trainer_type}")
            return errors
        
        rules = self.validation_rules[trainer_type]
        
        for rule in rules:
            field_name = rule.field_name
            field_value = config.get(field_name)
            
            # 检查必需字段
            if rule.required and field_value is None:
                errors.append(f"缺少必需字段: {field_name}")
                continue
            
            if field_value is None:
                continue
            
            # 检查数据类型
            if rule.data_type and not isinstance(field_value, rule.data_type):
                errors.append(f"字段 {field_name} 类型错误,期望 {rule.data_type.__name__},实际 {type(field_value).__name__}")
                continue
            
            # 检查数值范围
            if rule.min_value is not None and field_value < rule.min_value:
                errors.append(f"字段 {field_name} 值 {field_value} 小于最小值 {rule.min_value}")
            
            if rule.max_value is not None and field_value > rule.max_value:
                errors.append(f"字段 {field_name} 值 {field_value} 大于最大值 {rule.max_value}")
            
            # 检查允许的值
            if rule.allowed_values and field_value not in rule.allowed_values:
                errors.append(f"字段 {field_name} 值 {field_value} 不在允许的值列表中: {rule.allowed_values}")
            
            # 自定义验证
            if rule.custom_validator:
                try:
                    if not rule.custom_validator(field_value):
                        errors.append(f"字段 {field_name} 自定义验证失败")
                except Exception as e:
                    errors.append(f"字段 {field_name} 自定义验证出错: {e}")
        
        return errors
    
    async def create_validated_trainer(
        self, 
        trainer_type: str, 
        project_name: str, 
        config: Dict[str, Any]
    ) -> AsyncModelTrainer:
        """创建经过验证的训练器"""
        
        # 验证配置
        errors = self.validate_config(trainer_type, config)
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(f"- {error}" for error in errors)
            raise ValueError(error_msg)
        
        # 创建训练器
        trainer = await create_async_trainer(
            trainer_type=trainer_type,
            project_name=project_name,
            **config
        )
        
        print(f"配置验证通过,成功创建训练器: {project_name}")
        return trainer

# 使用示例
validator = AsyncTrainerConfigValidator()

# 验证配置
config = {
    "algorithm_name": "Transformer",
    "seq_len": 96,
    "pred_len": 24,
    "d_model": 512,
    "n_heads": 8,
    "learning_rate": 0.0001,
    "batch_size": 32
}

errors = validator.validate_config("async_time_series_sequence", config)
if errors:
    print("配置错误:")
    for error in errors:
        print(f"- {error}")
else:
    print("配置验证通过")
    trainer = await validator.create_validated_trainer(
        "async_time_series_sequence", 
        "validated_trainer", 
        config
    )
```

### 3. 训练器生命周期管理

```python
import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

class AsyncTrainerManager:
    """异步训练器生命周期管理器"""
    
    def __init__(self):
        self.active_trainers: Dict[str, AsyncModelTrainer] = {}
        self.trainer_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_and_register_trainer(
        self, 
        trainer_id: str,
        trainer_type: str, 
        project_name: str, 
        **config
    ) -> AsyncModelTrainer:
        """创建并注册训练器"""
        
        if trainer_id in self.active_trainers:
            raise ValueError(f"训练器 ID {trainer_id} 已存在")
        
        trainer = await create_async_trainer(
            trainer_type=trainer_type,
            project_name=project_name,
            **config
        )
        
        self.active_trainers[trainer_id] = trainer
        print(f"注册训练器: {trainer_id}")
        
        return trainer
    
    async def start_training(self, trainer_id: str) -> asyncio.Task:
        """启动训练任务"""
        
        if trainer_id not in self.active_trainers:
            raise ValueError(f"训练器 ID {trainer_id} 不存在")
        
        if trainer_id in self.trainer_tasks:
            raise ValueError(f"训练器 {trainer_id} 已在运行")
        
        trainer = self.active_trainers[trainer_id]
        task = asyncio.create_task(trainer.main())
        self.trainer_tasks[trainer_id] = task
        
        print(f"启动训练任务: {trainer_id}")
        return task
    
    async def stop_training(self, trainer_id: str) -> None:
        """停止训练任务"""
        
        if trainer_id not in self.trainer_tasks:
            print(f"训练器 {trainer_id} 未在运行")
            return
        
        task = self.trainer_tasks[trainer_id]
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            print(f"训练任务 {trainer_id} 已取消")
        
        del self.trainer_tasks[trainer_id]
    
    async def get_training_status(self, trainer_id: str) -> Dict[str, Any]:
        """获取训练状态"""
        
        if trainer_id not in self.active_trainers:
            raise ValueError(f"训练器 ID {trainer_id} 不存在")
        
        trainer = self.active_trainers[trainer_id]
        task = self.trainer_tasks.get(trainer_id)
        
        status = {
            "trainer_id": trainer_id,
            "project_name": trainer.project_name,
            "trainer_type": type(trainer).__name__,
            "is_running": task is not None and not task.done(),
            "is_completed": task is not None and task.done(),
        }
        
        if hasattr(trainer, 'current_epoch'):
            status["current_epoch"] = trainer.current_epoch
        
        if hasattr(trainer, 'best_loss'):
            status["best_loss"] = trainer.best_loss
        
        return status
    
    async def cleanup_trainer(self, trainer_id: str) -> None:
        """清理训练器资源"""
        
        # 停止训练任务
        await self.stop_training(trainer_id)
        
        # 清理训练器
        if trainer_id in self.active_trainers:
            trainer = self.active_trainers[trainer_id]
            if hasattr(trainer, 'cleanup'):
                await trainer.cleanup()
            del self.active_trainers[trainer_id]
            print(f"清理训练器: {trainer_id}")
    
    async def cleanup_all(self) -> None:
        """清理所有训练器"""
        trainer_ids = list(self.active_trainers.keys())
        for trainer_id in trainer_ids:
            await self.cleanup_trainer(trainer_id)
    
    @asynccontextmanager
    async def managed_trainer(
        self, 
        trainer_id: str,
        trainer_type: str, 
        project_name: str, 
        **config
    ) -> AsyncGenerator[AsyncModelTrainer, None]:
        """上下文管理器,自动管理训练器生命周期"""
        
        trainer = await self.create_and_register_trainer(
            trainer_id, trainer_type, project_name, **config
        )
        
        try:
            yield trainer
        finally:
            await self.cleanup_trainer(trainer_id)

# 使用示例
manager = AsyncTrainerManager()

# 使用上下文管理器
async def managed_training_example():
    async with manager.managed_trainer(
        trainer_id="managed_transformer",
        trainer_type="async_time_series_sequence",
        project_name="managed_training",
        algorithm_name="Transformer",
        seq_len=96,
        pred_len=24,
        local_test_mode=True
    ) as trainer:
        # 启动训练
        task = await manager.start_training("managed_transformer")
        
        # 监控训练状态
        while not task.done():
            status = await manager.get_training_status("managed_transformer")
            print(f"训练状态: {status}")
            await asyncio.sleep(10)
        
        print("训练完成")

# 运行示例
# asyncio.run(managed_training_example())
```

## 性能优化

### 1. 训练器池管理

```python
class AsyncTrainerPool:
    """异步训练器池"""
    
    def __init__(self, max_concurrent_trainers: int = 3):
        self.max_concurrent = max_concurrent_trainers
        self.semaphore = asyncio.Semaphore(max_concurrent_trainers)
        self.active_trainers = {}
        self.trainer_queue = asyncio.Queue()
    
    async def submit_training_job(
        self, 
        job_id: str,
        trainer_type: str, 
        project_name: str, 
        **config
    ) -> None:
        """提交训练任务到队列"""
        
        job = {
            "job_id": job_id,
            "trainer_type": trainer_type,
            "project_name": project_name,
            "config": config
        }
        
        await self.trainer_queue.put(job)
        print(f"训练任务 {job_id} 已提交到队列")
    
    async def process_training_jobs(self) -> None:
        """处理训练任务队列"""
        
        while True:
            try:
                # 获取训练任务
                job = await self.trainer_queue.get()
                
                # 等待可用的训练器槽位
                async with self.semaphore:
                    await self._execute_training_job(job)
                
                # 标记任务完成
                self.trainer_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"处理训练任务时出错: {e}")
    
    async def _execute_training_job(self, job: Dict[str, Any]) -> None:
        """执行单个训练任务"""
        
        job_id = job["job_id"]
        trainer_type = job["trainer_type"]
        project_name = job["project_name"]
        config = job["config"]
        
        try:
            print(f"开始执行训练任务: {job_id}")
            
            # 创建训练器
            trainer = await create_async_trainer(
                trainer_type=trainer_type,
                project_name=project_name,
                **config
            )
            
            self.active_trainers[job_id] = trainer
            
            # 执行训练
            await trainer.main()
            
            print(f"训练任务 {job_id} 完成")
            
        except Exception as e:
            print(f"训练任务 {job_id} 失败: {e}")
        
        finally:
            # 清理资源
            if job_id in self.active_trainers:
                del self.active_trainers[job_id]

# 使用示例
pool = AsyncTrainerPool(max_concurrent_trainers=2)

async def pool_training_example():
    # 启动任务处理器
    processor_task = asyncio.create_task(pool.process_training_jobs())
    
    # 提交多个训练任务
    training_jobs = [
        {
            "job_id": "job_1",
            "trainer_type": "async_time_series_sequence",
            "project_name": "transformer_job_1",
            "algorithm_name": "Transformer",
            "local_test_mode": True
        },
        {
            "job_id": "job_2",
            "trainer_type": "async_time_series_classic",
            "project_name": "lstm_job_2",
            "algorithm_name": "LSTM",
            "local_test_mode": True
        },
        {
            "job_id": "job_3",
            "trainer_type": "async_time_series_sequence",
            "project_name": "informer_job_3",
            "algorithm_name": "Informer",
            "local_test_mode": True
        }
    ]
    
    # 提交所有任务
    for job in training_jobs:
        await pool.submit_training_job(**job)
    
    # 等待所有任务完成
    await pool.trainer_queue.join()
    
    # 停止处理器
    processor_task.cancel()
    
    print("所有训练任务完成")

# 运行示例
# asyncio.run(pool_training_example())
```

## 错误处理和调试

### 1. 详细错误处理

```python
class AsyncTrainerError(Exception):
    """异步训练器基础异常"""
    pass

class TrainerTypeError(AsyncTrainerError):
    """训练器类型错误"""
    pass

class TrainerConfigError(AsyncTrainerError):
    """训练器配置错误"""
    pass

class TrainerBuildError(AsyncTrainerError):
    """训练器构建错误"""
    pass

async def create_async_trainer_with_error_handling(
    trainer_type: str,
    project_name: str,
    dbconfig: Optional[Dict[str, Any]] = None,
    local_test_mode: bool = False,
    **kwargs,
) -> AsyncModelTrainer:
    """带详细错误处理的异步训练器创建函数"""
    
    try:
        # 验证训练器类型
        if trainer_type not in ASYNC_TRAINER_CLASS_MAP:
            available_types = list(ASYNC_TRAINER_CLASS_MAP.keys())
            raise TrainerTypeError(
                f"不支持的训练器类型: {trainer_type}。"
                f"可用类型: {available_types}"
            )
        
        # 验证项目名称
        if not project_name or not isinstance(project_name, str):
            raise TrainerConfigError("项目名称必须是非空字符串")
        
        # 验证数据库配置
        if dbconfig is not None and not isinstance(dbconfig, dict):
            raise TrainerConfigError("数据库配置必须是字典类型")
        
        # 获取训练器类
        trainer_class = ASYNC_TRAINER_CLASS_MAP[trainer_type]
        
        # 准备初始化参数
        init_params = {
            "project_name": project_name,
            "local_test_mode": local_test_mode
        }
        
        if dbconfig is not None:
            init_params["dbconfig"] = dbconfig
        
        # 创建训练器实例
        trainer = trainer_class(**init_params)
        
        # 应用额外配置
        for key, value in kwargs.items():
            if hasattr(trainer, key):
                setattr(trainer, key, value)
            else:
                print(f"警告: 训练器不支持配置项 {key},已忽略")
        
        return trainer
        
    except TrainerTypeError:
        raise
    except TrainerConfigError:
        raise
    except Exception as e:
        raise TrainerBuildError(f"创建训练器时发生未知错误: {e}") from e
```

### 2. 调试工具

```python
class AsyncTrainerDebugger:
    """异步训练器调试工具"""
    
    def __init__(self, trainer: AsyncModelTrainer):
        self.trainer = trainer
        self.debug_info = {
            "creation_time": asyncio.get_event_loop().time(),
            "config_history": [],
            "method_calls": [],
            "performance_metrics": {}
        }
    
    def log_config_change(self, key: str, old_value: Any, new_value: Any):
        """记录配置变更"""
        self.debug_info["config_history"].append({
            "timestamp": asyncio.get_event_loop().time(),
            "key": key,
            "old_value": old_value,
            "new_value": new_value
        })
    
    def log_method_call(self, method_name: str, args: tuple, kwargs: dict):
        """记录方法调用"""
        self.debug_info["method_calls"].append({
            "timestamp": asyncio.get_event_loop().time(),
            "method": method_name,
            "args": str(args),
            "kwargs": str(kwargs)
        })
    
    async def profile_method(self, method_name: str, *args, **kwargs):
        """性能分析方法调用"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            method = getattr(self.trainer, method_name)
            if asyncio.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                result = method(*args, **kwargs)
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            self.debug_info["performance_metrics"][method_name] = {
                "execution_time": execution_time,
                "timestamp": start_time
            }
            
            return result
            
        except Exception as e:
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            self.debug_info["performance_metrics"][method_name] = {
                "execution_time": execution_time,
                "timestamp": start_time,
                "error": str(e)
            }
            
            raise
    
    def get_debug_report(self) -> Dict[str, Any]:
        """获取调试报告"""
        current_time = asyncio.get_event_loop().time()
        
        return {
            "trainer_info": {
                "type": type(self.trainer).__name__,
                "project_name": getattr(self.trainer, 'project_name', 'Unknown'),
                "creation_time": self.debug_info["creation_time"],
                "lifetime": current_time - self.debug_info["creation_time"]
            },
            "config_history": self.debug_info["config_history"],
            "method_calls": self.debug_info["method_calls"],
            "performance_metrics": self.debug_info["performance_metrics"]
        }
```

## 最佳实践

### 1. 配置管理最佳实践

```python
# 推荐的配置结构
RECOMMENDED_CONFIG_STRUCTURE = {
    "project": {
        "name": "string",
        "description": "string",
        "version": "string"
    },
    "trainer": {
        "type": "string",
        "algorithm_name": "string"
    },
    "model": {
        # 模型特定参数
    },
    "training": {
        "batch_size": "int",
        "learning_rate": "float",
        "num_epochs": "int"
    },
    "data": {
        "database": "dict",
        "local_test_mode": "bool"
    },
    "optimization": {
        "use_amp": "bool",
        "gradient_clip_val": "float"
    },
    "monitoring": {
        "log_interval": "int",
        "save_interval": "int"
    }
}

# 配置模板
CONFIG_TEMPLATES = {
    "transformer_default": {
        "trainer": {
            "type": "async_time_series_sequence",
            "algorithm_name": "Transformer"
        },
        "model": {
            "seq_len": 96,
            "label_len": 48,
            "pred_len": 24,
            "d_model": 512,
            "n_heads": 8,
            "e_layers": 2,
            "d_layers": 1
        },
        "training": {
            "batch_size": 32,
            "learning_rate": 0.0001,
            "num_epochs": 100
        }
    },
    "lstm_default": {
        "trainer": {
            "type": "async_time_series_classic",
            "algorithm_name": "LSTM"
        },
        "model": {
            "hidden_size": 64,
            "num_layers": 2,
            "dropout": 0.1
        },
        "training": {
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 100
        }
    }
}
```

### 2. 错误恢复策略

```python
async def create_trainer_with_retry(
    trainer_type: str,
    project_name: str,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    **kwargs
) -> AsyncModelTrainer:
    """带重试机制的训练器创建"""
    
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            trainer = await create_async_trainer(
                trainer_type=trainer_type,
                project_name=project_name,
                **kwargs
            )
            
            if attempt > 0:
                print(f"第 {attempt + 1} 次尝试成功创建训练器")
            
            return trainer
            
        except Exception as e:
            last_exception = e
            
            if attempt < max_retries:
                print(f"第 {attempt + 1} 次尝试失败: {e},{retry_delay} 秒后重试")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                print(f"所有 {max_retries + 1} 次尝试都失败")
    
    raise last_exception
```

### 3. 资源管理最佳实践

```python
@asynccontextmanager
async def managed_async_trainer(
    trainer_type: str,
    project_name: str,
    **kwargs
) -> AsyncGenerator[AsyncModelTrainer, None]:
    """管理异步训练器资源的上下文管理器"""
    
    trainer = None
    try:
        # 创建训练器
        trainer = await create_async_trainer(
            trainer_type=trainer_type,
            project_name=project_name,
            **kwargs
        )
        
        print(f"创建训练器: {project_name}")
        yield trainer
        
    except Exception as e:
        print(f"训练器使用过程中出错: {e}")
        raise
    
    finally:
        # 清理资源
        if trainer is not None:
            try:
                if hasattr(trainer, 'cleanup'):
                    await trainer.cleanup()
                print(f"清理训练器资源: {project_name}")
            except Exception as e:
                print(f"清理资源时出错: {e}")

# 使用示例
async def resource_managed_training():
    async with managed_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="managed_training",
        algorithm_name="Transformer",
        local_test_mode=True
    ) as trainer:
        await trainer.main()
```

## 相关文档

- [异步基础训练器](async_basic_trainer.md)
- [异步时序经典训练器](async_time_series_classic_trainer.md)
- [异步时序序列训练器](async_time_series_sequence_trainer.md)
- [使用示例](../usage_examples.md)