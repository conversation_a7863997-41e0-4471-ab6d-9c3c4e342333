# 异步训练使用示例

本文档提供了 `industrytslib` 异步训练功能的详细使用示例,涵盖从基础使用到高级应用场景的完整指南。

## 目录



## 基础使用示例

### 1. 简单的 Transformer 时序预测

```python
import asyncio
from industrytslib.core_aysnc.async_training_pipeline import create_async_trainer

async def basic_transformer_example():
    """基础 Transformer 时序预测示例"""
    
    # 创建异步时序序列训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="basic_transformer_forecast",
        local_test_mode=True,  # 使用本地测试模式
        
        # 模型配置
        algorithm_name="Transformer",
        seq_len=96,      # 输入序列长度
        label_len=48,    # 标签序列长度
        pred_len=24,     # 预测序列长度
        enc_in=7,        # 编码器输入维度
        dec_in=7,        # 解码器输入维度
        c_out=7,         # 输出维度
        d_model=512,     # 模型维度
        n_heads=8,       # 注意力头数
        e_layers=2,      # 编码器层数
        d_layers=1,      # 解码器层数
        
        # 训练配置
        batch_size=32,
        learning_rate=0.0001,
        num_epochs=50,
        teacher_forcing_ratio=0.5
    )
    
    print(f"创建训练器: {trainer.project_name}")
    print(f"算法类型: {trainer.algorithm_name}")
    print(f"模型维度: {trainer.d_model}")
    
    # 执行训练
    await trainer.main()
    
    print("训练完成！")

# 运行示例
if __name__ == "__main__":
    asyncio.run(basic_transformer_example())
```

### 2. 简单的 LSTM 时序预测

```python
async def basic_lstm_example():
    """基础 LSTM 时序预测示例"""
    
    # 创建异步时序经典训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_classic",
        project_name="basic_lstm_forecast",
        local_test_mode=True,
        
        # 模型配置
        algorithm_name="LSTM",
        model_parameter={
            "input_size": 10,
            "hidden_size": 64,
            "num_layers": 2,
            "output_size": 1,
            "dropout": 0.1
        },
        
        # 训练配置
        batch_size=32,
        learning_rate=0.001,
        num_epochs=100,
        
        # 早停配置
        patience=20,
        early_stopping_delta=0.0001
    )
    
    print(f"创建训练器: {trainer.project_name}")
    print(f"算法类型: {trainer.algorithm_name}")
    print(f"隐藏层大小: {trainer.model_parameter['hidden_size']}")
    
    # 执行训练
    await trainer.main()
    
    print("训练完成！")

# 运行示例
asyncio.run(basic_lstm_example())
```

### 3. 使用建造者模式创建训练器

```python
from industrytslib.core_aysnc.async_training_pipeline import AsyncTrainerBuilder

async def builder_pattern_example():
    """使用建造者模式创建训练器示例"""
    
    # 使用建造者模式创建 Informer 训练器
    trainer = await (
        AsyncTrainerBuilder("async_time_series_sequence")
        .with_project_name("informer_builder_example")
        .with_local_test_mode(True)
        .with_model_parameters(
            algorithm_name="Informer",
            seq_len=96,
            label_len=48,
            pred_len=24,
            enc_in=7,
            dec_in=7,
            c_out=7,
            d_model=512,
            n_heads=8,
            e_layers=2,
            d_layers=1,
            factor=1,        # ProbSparse 注意力因子
            distil=True      # 使用蒸馏
        )
        .with_training_parameters(
            batch_size=32,
            learning_rate=0.0001,
            num_epochs=80,
            teacher_forcing_ratio=0.5
        )
        .with_custom_config(
            use_amp=True,           # 混合精度训练
            gradient_clip_val=1.0,  # 梯度裁剪
            save_attention=True     # 保存注意力权重
        )
        .build()
    )
    
    print(f"使用建造者模式创建训练器: {trainer.project_name}")
    print(f"算法类型: {trainer.algorithm_name}")
    print(f"使用混合精度: {trainer.use_amp}")
    
    # 执行训练
    await trainer.main()
    
    print("训练完成！")

# 运行示例
asyncio.run(builder_pattern_example())
```

## 高级配置示例

### 1. 数据库连接配置

```python
async def database_config_example():
    """数据库连接配置示例"""
    
    # 数据库配置
    db_config = {
        "server": "localhost",
        "database": "timeseries_production",
        "username": "ts_user",
        "password": "secure_password",
        "port": 1433,
        "driver": "ODBC Driver 17 for SQL Server",
        "connection_timeout": 30,
        "command_timeout": 300
    }
    
    # 创建连接数据库的训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="database_connected_training",
        dbconfig=db_config,
        local_test_mode=False,  # 使用真实数据库
        
        # 模型配置
        algorithm_name="Transformer",
        seq_len=168,     # 一周的小时数据
        label_len=84,    # 3.5天
        pred_len=24,     # 预测未来24小时
        enc_in=15,       # 15个输入特征
        dec_in=15,
        c_out=15,
        d_model=512,
        n_heads=8,
        e_layers=3,
        d_layers=2,
        
        # 训练配置
        batch_size=64,
        learning_rate=0.0001,
        num_epochs=200,
        
        # 数据配置
        input_name_list=[
            'temperature', 'humidity', 'pressure', 'wind_speed',
            'solar_radiation', 'electricity_demand', 'gas_consumption',
            'water_usage', 'traffic_flow', 'air_quality',
            'stock_price', 'exchange_rate', 'commodity_price',
            'social_sentiment', 'news_sentiment'
        ],
        output_name_list=[
            'electricity_demand', 'gas_consumption', 'water_usage',
            'traffic_flow', 'air_quality', 'stock_price',
            'exchange_rate', 'commodity_price', 'temperature',
            'humidity', 'pressure', 'wind_speed',
            'solar_radiation', 'social_sentiment', 'news_sentiment'
        ]
    )
    
    print(f"创建数据库连接训练器: {trainer.project_name}")
    print(f"输入特征数量: {len(trainer.input_name_list)}")
    print(f"输出特征数量: {len(trainer.output_name_list)}")
    
    # 执行训练
    await trainer.main()
    
    print("数据库连接训练完成！")

# 运行示例
# asyncio.run(database_config_example())
```

### 2. 数据增强配置

```python
async def data_augmentation_example():
    """数据增强配置示例"""
    
    # 复杂的数据增强配置
    augmentation_config = {
        "gaussian_noise": {
            "enabled": True,
            "std": 0.01,
            "probability": 0.5
        },
        "uniform_noise": {
            "enabled": True,
            "range": [-0.02, 0.02],
            "probability": 0.3
        },
        "time_mask": {
            "enabled": True,
            "mask_ratio": 0.1,
            "probability": 0.4
        },
        "scaling": {
            "enabled": True,
            "scale_range": [0.8, 1.2],
            "probability": 0.6
        },
        "time_shift": {
            "enabled": True,
            "shift_range": [-5, 5],
            "probability": 0.3
        },
        "frequency_mask": {
            "enabled": True,
            "mask_ratio": 0.05,
            "probability": 0.2
        }
    }
    
    # 创建带数据增强的训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_classic",
        project_name="augmented_gru_training",
        local_test_mode=True,
        
        # 模型配置
        algorithm_name="GRU",
        model_parameter={
            "input_size": 20,
            "hidden_size": 128,
            "num_layers": 3,
            "output_size": 5,
            "dropout": 0.2
        },
        
        # 训练配置
        batch_size=32,
        learning_rate=0.001,
        num_epochs=150,
        
        # 数据增强配置
        apply_aug=True,
        aug_config=augmentation_config,
        
        # 早停配置
        patience=30,
        early_stopping_delta=0.00001,
        early_stopping_config={
            "monitor": "val_loss",
            "mode": "min",
            "restore_best_weights": True
        }
    )
    
    print(f"创建数据增强训练器: {trainer.project_name}")
    print(f"数据增强策略数量: {len([k for k, v in augmentation_config.items() if v.get('enabled', False)])}")
    print(f"模型隐藏层大小: {trainer.model_parameter['hidden_size']}")
    
    # 执行训练
    await trainer.main()
    
    print("数据增强训练完成！")

# 运行示例
asyncio.run(data_augmentation_example())
```

### 3. 长期预测配置

```python
async def long_term_forecasting_example():
    """长期预测配置示例"""
    
    # 创建长期预测训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="long_term_autoformer",
        local_test_mode=True,
        
        # 长期预测模型配置
        algorithm_name="Autoformer",
        seq_len=336,     # 14天的小时数据
        label_len=168,   # 7天的标签
        pred_len=96,     # 预测未来4天
        enc_in=21,       # 21个输入特征
        dec_in=21,
        c_out=21,
        d_model=512,
        n_heads=8,
        e_layers=2,
        d_layers=1,
        d_ff=2048,
        dropout=0.1,
        moving_avg=25,   # Autoformer 移动平均窗口
        activation='gelu',
        
        # 训练配置
        batch_size=16,   # 长序列使用较小批次
        learning_rate=0.0001,
        num_epochs=100,
        teacher_forcing_ratio=0.5,
        
        # 优化配置
        use_amp=True,              # 混合精度训练
        gradient_clip_val=1.0,     # 梯度裁剪
        accumulate_grad_batches=2, # 梯度累积
        
        # 学习率调度
        lr_scheduler="cosine",
        warmup_epochs=10,
        
        # 输入输出特征
        input_name_list=[
            'temperature', 'humidity', 'pressure', 'wind_speed', 'solar_radiation',
            'electricity_demand', 'gas_consumption', 'water_usage', 'traffic_flow',
            'air_quality', 'stock_price', 'exchange_rate', 'commodity_price',
            'gdp_growth', 'inflation_rate', 'unemployment_rate', 'interest_rate',
            'social_sentiment', 'news_sentiment', 'search_trends', 'mobility_index'
        ],
        output_name_list=[
            'electricity_demand', 'gas_consumption', 'water_usage', 'traffic_flow',
            'air_quality', 'stock_price', 'exchange_rate', 'commodity_price',
            'temperature', 'humidity', 'pressure', 'wind_speed', 'solar_radiation',
            'gdp_growth', 'inflation_rate', 'unemployment_rate', 'interest_rate',
            'social_sentiment', 'news_sentiment', 'search_trends', 'mobility_index'
        ]
    )
    
    print(f"创建长期预测训练器: {trainer.project_name}")
    print(f"输入序列长度: {trainer.seq_len}")
    print(f"预测序列长度: {trainer.pred_len}")
    print(f"特征数量: {len(trainer.input_name_list)}")
    
    # 执行训练
    await trainer.main()
    
    print("长期预测训练完成！")

# 运行示例
asyncio.run(long_term_forecasting_example())
```

## 并发训练示例

### 1. 多模型并发训练

```python
async def concurrent_multi_model_training():
    """多模型并发训练示例"""
    
    # 定义多个模型配置
    model_configs = [
        {
            "project_name": "transformer_short_term",
            "trainer_type": "async_time_series_sequence",
            "algorithm_name": "Transformer",
            "pred_len": 24,
            "d_model": 256,
            "description": "短期预测 Transformer"
        },
        {
            "project_name": "informer_medium_term",
            "trainer_type": "async_time_series_sequence",
            "algorithm_name": "Informer",
            "pred_len": 48,
            "d_model": 512,
            "description": "中期预测 Informer"
        },
        {
            "project_name": "autoformer_long_term",
            "trainer_type": "async_time_series_sequence",
            "algorithm_name": "Autoformer",
            "pred_len": 96,
            "d_model": 512,
            "description": "长期预测 Autoformer"
        },
        {
            "project_name": "lstm_baseline",
            "trainer_type": "async_time_series_classic",
            "algorithm_name": "LSTM",
            "hidden_size": 128,
            "description": "LSTM 基线模型"
        },
        {
            "project_name": "gru_ensemble",
            "trainer_type": "async_time_series_classic",
            "algorithm_name": "GRU",
            "hidden_size": 64,
            "description": "GRU 集成模型"
        }
    ]
    
    async def create_and_train_model(config):
        """创建并训练单个模型"""
        try:
            print(f"开始训练: {config['description']}")
            
            # 基础配置
            base_config = {
                "local_test_mode": True,
                "batch_size": 32,
                "learning_rate": 0.0001,
                "num_epochs": 50
            }
            
            # 根据训练器类型添加特定配置
            if config["trainer_type"] == "async_time_series_sequence":
                base_config.update({
                    "seq_len": 96,
                    "label_len": 48,
                    "enc_in": 7,
                    "dec_in": 7,
                    "c_out": 7,
                    "n_heads": 8,
                    "e_layers": 2,
                    "d_layers": 1
                })
            else:
                base_config.update({
                    "model_parameter": {
                        "input_size": 7,
                        "hidden_size": config.get("hidden_size", 64),
                        "num_layers": 2,
                        "output_size": 1,
                        "dropout": 0.1
                    }
                })
            
            # 合并配置
            final_config = {**base_config, **config}
            
            # 创建训练器
            trainer = await create_async_trainer(**final_config)
            
            # 执行训练
            await trainer.main()
            
            print(f"完成训练: {config['description']}")
            return {
                "project_name": config["project_name"],
                "status": "success",
                "description": config["description"]
            }
            
        except Exception as e:
            print(f"训练失败: {config['description']} - {e}")
            return {
                "project_name": config["project_name"],
                "status": "failed",
                "error": str(e),
                "description": config["description"]
            }
    
    print(f"开始并发训练 {len(model_configs)} 个模型...")
    start_time = asyncio.get_event_loop().time()
    
    # 并发执行所有训练任务
    results = await asyncio.gather(
        *[create_and_train_model(config) for config in model_configs],
        return_exceptions=True
    )
    
    end_time = asyncio.get_event_loop().time()
    total_time = end_time - start_time
    
    # 统计结果
    successful = sum(1 for r in results if isinstance(r, dict) and r.get("status") == "success")
    failed = len(results) - successful
    
    print(f"\n并发训练完成！")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"成功: {successful} 个模型")
    print(f"失败: {failed} 个模型")
    
    # 详细结果
    print("\n详细结果:")
    for result in results:
        if isinstance(result, dict):
            status = "✅" if result["status"] == "success" else "❌"
            print(f"{status} {result['description']} ({result['project_name']})")
            if result["status"] == "failed":
                print(f"   错误: {result['error']}")
        else:
            print(f"❌ 未知错误: {result}")
    
    return results

# 运行示例
results = asyncio.run(concurrent_multi_model_training())
```

### 2. 参数网格搜索

```python
async def hyperparameter_grid_search():
    """超参数网格搜索示例"""
    
    # 定义超参数网格
    param_grid = {
        "d_model": [256, 512],
        "n_heads": [4, 8],
        "e_layers": [2, 3],
        "learning_rate": [0.0001, 0.0005],
        "batch_size": [16, 32]
    }
    
    # 生成所有参数组合
    import itertools
    
    param_names = list(param_grid.keys())
    param_values = list(param_grid.values())
    param_combinations = list(itertools.product(*param_values))
    
    print(f"总共 {len(param_combinations)} 个参数组合")
    
    async def train_with_params(param_combo, combo_id):
        """使用特定参数组合训练模型"""
        
        # 构建参数字典
        params = dict(zip(param_names, param_combo))
        
        project_name = f"grid_search_{combo_id:03d}"
        
        try:
            print(f"开始训练组合 {combo_id}: {params}")
            
            trainer = await create_async_trainer(
                trainer_type="async_time_series_sequence",
                project_name=project_name,
                local_test_mode=True,
                
                # 模型配置
                algorithm_name="Transformer",
                seq_len=96,
                label_len=48,
                pred_len=24,
                enc_in=7,
                dec_in=7,
                c_out=7,
                d_model=params["d_model"],
                n_heads=params["n_heads"],
                e_layers=params["e_layers"],
                d_layers=1,
                
                # 训练配置
                batch_size=params["batch_size"],
                learning_rate=params["learning_rate"],
                num_epochs=30,  # 较少的轮数用于快速搜索
                
                # 早停配置
                patience=10,
                early_stopping_delta=0.0001
            )
            
            # 执行训练
            await trainer.main()
            
            # 获取最终性能(这里假设训练器有这些属性)
            final_loss = getattr(trainer, 'best_loss', float('inf'))
            final_epoch = getattr(trainer, 'best_epoch', 0)
            
            result = {
                "combo_id": combo_id,
                "params": params,
                "final_loss": final_loss,
                "final_epoch": final_epoch,
                "status": "success"
            }
            
            print(f"完成组合 {combo_id}: 损失 {final_loss:.6f}")
            return result
            
        except Exception as e:
            print(f"组合 {combo_id} 失败: {e}")
            return {
                "combo_id": combo_id,
                "params": params,
                "error": str(e),
                "status": "failed"
            }
    
    # 限制并发数量以避免资源耗尽
    semaphore = asyncio.Semaphore(3)  # 最多3个并发训练
    
    async def train_with_semaphore(param_combo, combo_id):
        async with semaphore:
            return await train_with_params(param_combo, combo_id)
    
    print("开始网格搜索...")
    start_time = asyncio.get_event_loop().time()
    
    # 并发执行所有参数组合
    results = await asyncio.gather(
        *[train_with_semaphore(combo, i) for i, combo in enumerate(param_combinations)],
        return_exceptions=True
    )
    
    end_time = asyncio.get_event_loop().time()
    total_time = end_time - start_time
    
    # 分析结果
    successful_results = [r for r in results if isinstance(r, dict) and r.get("status") == "success"]
    
    if successful_results:
        # 找到最佳参数
        best_result = min(successful_results, key=lambda x: x["final_loss"])
        
        print(f"\n网格搜索完成！")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"成功组合: {len(successful_results)}/{len(param_combinations)}")
        print(f"\n最佳参数组合:")
        print(f"参数: {best_result['params']}")
        print(f"最佳损失: {best_result['final_loss']:.6f}")
        print(f"最佳轮数: {best_result['final_epoch']}")
        
        # 显示前5个最佳结果
        print(f"\n前5个最佳结果:")
        top_5 = sorted(successful_results, key=lambda x: x["final_loss"])[:5]
        for i, result in enumerate(top_5, 1):
            print(f"{i}. 损失: {result['final_loss']:.6f}, 参数: {result['params']}")
    
    else:
        print("所有参数组合都失败了！")
    
    return results

# 运行示例
# results = asyncio.run(hyperparameter_grid_search())
```

## 生产环境示例

### 1. 生产环境配置

```python
import os
import logging
from pathlib import Path

async def production_environment_example():
    """生产环境配置示例"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('production_training.log'),
            logging.StreamHandler()
        ]
    )
    
    # 从环境变量读取配置
    db_config = {
        "server": os.getenv("DB_SERVER", "production-db-server"),
        "database": os.getenv("DB_NAME", "timeseries_production"),
        "username": os.getenv("DB_USER", "ts_user"),
        "password": os.getenv("DB_PASSWORD", "secure_password"),
        "port": int(os.getenv("DB_PORT", "1433")),
        "connection_timeout": 60,
        "command_timeout": 600
    }
    
    # 生产环境路径配置
    base_path = Path(os.getenv("MODEL_BASE_PATH", "/opt/models"))
    model_path = base_path / "transformer_production"
    model_path.mkdir(parents=True, exist_ok=True)
    
    # 创建生产环境训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="production_transformer_v1",
        dbconfig=db_config,
        local_test_mode=False,  # 生产模式
        
        # 模型配置 - 生产级别
        algorithm_name="Transformer",
        seq_len=168,     # 一周数据
        label_len=84,    # 3.5天标签
        pred_len=24,     # 预测24小时
        enc_in=25,       # 25个输入特征
        dec_in=25,
        c_out=25,
        d_model=768,     # 更大的模型
        n_heads=12,
        e_layers=6,      # 更深的网络
        d_layers=3,
        d_ff=3072,
        dropout=0.1,
        
        # 训练配置 - 生产级别
        batch_size=64,
        learning_rate=0.00005,  # 更小的学习率
        num_epochs=500,         # 更多轮数
        
        # 优化配置
        use_amp=True,              # 混合精度
        gradient_clip_val=1.0,     # 梯度裁剪
        accumulate_grad_batches=4, # 梯度累积
        
        # 早停配置
        patience=50,
        early_stopping_delta=0.000001,
        
        # 检查点配置
        save_checkpoint_every=10,
        max_checkpoints=5,
        
        # 监控配置
        log_every_n_steps=100,
        val_check_interval=0.25,
        
        # 路径配置
        model_save_path=str(model_path),
        log_dir=str(model_path / "logs"),
        checkpoint_dir=str(model_path / "checkpoints"),
        
        # 输入输出特征(生产环境的完整特征集)
        input_name_list=[
            # 气象特征
            'temperature', 'humidity', 'pressure', 'wind_speed', 'solar_radiation',
            'precipitation', 'cloud_cover', 'visibility', 'uv_index',
            # 能源特征
            'electricity_demand', 'gas_consumption', 'water_usage', 'renewable_generation',
            # 经济特征
            'stock_index', 'exchange_rate', 'commodity_price', 'gdp_indicator',
            'inflation_rate', 'interest_rate',
            # 社会特征
            'traffic_flow', 'population_density', 'social_sentiment', 'news_sentiment',
            # 环境特征
            'air_quality', 'noise_level'
        ],
        output_name_list=[
            'electricity_demand', 'gas_consumption', 'water_usage', 'renewable_generation',
            'traffic_flow', 'air_quality', 'stock_index', 'exchange_rate',
            'commodity_price', 'temperature', 'humidity', 'pressure', 'wind_speed',
            'solar_radiation', 'precipitation', 'cloud_cover', 'visibility',
            'uv_index', 'gdp_indicator', 'inflation_rate', 'interest_rate',
            'population_density', 'social_sentiment', 'news_sentiment', 'noise_level'
        ]
    )
    
    logging.info(f"创建生产环境训练器: {trainer.project_name}")
    logging.info(f"模型保存路径: {model_path}")
    logging.info(f"输入特征数量: {len(trainer.input_name_list)}")
    logging.info(f"输出特征数量: {len(trainer.output_name_list)}")
    
    try:
        # 执行训练
        await trainer.main()
        logging.info("生产环境训练完成！")
        
        # 保存模型元数据
        metadata = {
            "model_version": "v1.0",
            "training_date": asyncio.get_event_loop().time(),
            "model_config": {
                "algorithm_name": trainer.algorithm_name,
                "seq_len": trainer.seq_len,
                "pred_len": trainer.pred_len,
                "d_model": trainer.d_model,
                "n_heads": trainer.n_heads
            },
            "performance_metrics": {
                "final_loss": getattr(trainer, 'best_loss', None),
                "best_epoch": getattr(trainer, 'best_epoch', None)
            }
        }
        
        import json
        with open(model_path / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logging.info("模型元数据已保存")
        
    except Exception as e:
        logging.error(f"生产环境训练失败: {e}")
        raise

# 运行示例(需要适当的环境变量)
# asyncio.run(production_environment_example())
```

### 2. 模型版本管理

```python
import json
import shutil
from datetime import datetime
from pathlib import Path

class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.versions_file = self.base_path / "versions.json"
        self.load_versions()
    
    def load_versions(self):
        """加载版本信息"""
        if self.versions_file.exists():
            with open(self.versions_file, 'r') as f:
                self.versions = json.load(f)
        else:
            self.versions = {"models": {}, "latest_version": 0}
    
    def save_versions(self):
        """保存版本信息"""
        with open(self.versions_file, 'w') as f:
            json.dump(self.versions, f, indent=2)
    
    def create_new_version(self, model_name: str, config: dict) -> str:
        """创建新版本"""
        version_id = self.versions["latest_version"] + 1
        version_name = f"v{version_id}"
        
        version_info = {
            "version_id": version_id,
            "version_name": version_name,
            "model_name": model_name,
            "created_at": datetime.now().isoformat(),
            "config": config,
            "status": "training",
            "path": str(self.base_path / model_name / version_name)
        }
        
        if model_name not in self.versions["models"]:
            self.versions["models"][model_name] = []
        
        self.versions["models"][model_name].append(version_info)
        self.versions["latest_version"] = version_id
        
        # 创建版本目录
        version_path = Path(version_info["path"])
        version_path.mkdir(parents=True, exist_ok=True)
        
        self.save_versions()
        return version_name
    
    def update_version_status(self, model_name: str, version_name: str, status: str, metrics: dict = None):
        """更新版本状态"""
        for version in self.versions["models"].get(model_name, []):
            if version["version_name"] == version_name:
                version["status"] = status
                version["updated_at"] = datetime.now().isoformat()
                if metrics:
                    version["metrics"] = metrics
                break
        
        self.save_versions()
    
    def get_version_path(self, model_name: str, version_name: str) -> Path:
        """获取版本路径"""
        for version in self.versions["models"].get(model_name, []):
            if version["version_name"] == version_name:
                return Path(version["path"])
        raise ValueError(f"版本 {version_name} 不存在")

async def versioned_training_example():
    """版本化训练示例"""
    
    # 创建版本管理器
    version_manager = ModelVersionManager("/opt/models")
    
    # 模型配置
    model_config = {
        "trainer_type": "async_time_series_sequence",
        "algorithm_name": "Transformer",
        "seq_len": 96,
        "pred_len": 24,
        "d_model": 512,
        "n_heads": 8,
        "batch_size": 32,
        "learning_rate": 0.0001,
        "num_epochs": 100
    }
    
    # 创建新版本
    model_name = "production_transformer"
    version_name = version_manager.create_new_version(model_name, model_config)
    version_path = version_manager.get_version_path(model_name, version_name)
    
    print(f"创建新版本: {model_name} {version_name}")
    print(f"版本路径: {version_path}")
    
    try:
        # 创建训练器
        trainer = await create_async_trainer(
            project_name=f"{model_name}_{version_name}",
            local_test_mode=True,
            model_save_path=str(version_path),
            **model_config
        )
        
        # 更新状态为训练中
        version_manager.update_version_status(model_name, version_name, "training")
        
        # 执行训练
        await trainer.main()
        
        # 获取训练指标
        metrics = {
            "final_loss": getattr(trainer, 'best_loss', None),
            "best_epoch": getattr(trainer, 'best_epoch', None),
            "training_time": getattr(trainer, 'training_time', None)
        }
        
        # 更新状态为完成
        version_manager.update_version_status(model_name, version_name, "completed", metrics)
        
        print(f"版本 {version_name} 训练完成")
        print(f"最终指标: {metrics}")
        
    except Exception as e:
        # 更新状态为失败
        version_manager.update_version_status(model_name, version_name, "failed", {"error": str(e)})
        print(f"版本 {version_name} 训练失败: {e}")
        raise

# 运行示例
# asyncio.run(versioned_training_example())
```

## 性能优化示例

### 1. 内存优化训练

```python
import torch
import gc
from torch.cuda.amp import GradScaler, autocast

async def memory_optimized_training():
    """内存优化训练示例"""
    
    # 内存优化配置
    memory_config = {
        "use_amp": True,                    # 混合精度训练
        "gradient_checkpointing": True,    # 梯度检查点
        "max_memory_usage": 0.85,          # 最大内存使用率
        "memory_cleanup_interval": 10,     # 内存清理间隔
        "batch_size_auto_scale": True,     # 自动批次大小调整
        "pin_memory": True,                # 固定内存
        "num_workers": 4                   # 数据加载工作进程数
    }
    
    class MemoryOptimizedTrainer:
        """内存优化训练器包装类"""
        
        def __init__(self, base_trainer):
            self.trainer = base_trainer
            self.scaler = GradScaler() if memory_config["use_amp"] else None
            self.memory_stats = []
        
        async def train_with_memory_management(self):
            """带内存管理的训练"""
            
            for epoch in range(self.trainer.num_epochs):
                # 监控内存使用
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated()
                    memory_reserved = torch.cuda.memory_reserved()
                    max_memory = torch.cuda.max_memory_allocated()
                    
                    memory_usage = memory_allocated / max_memory if max_memory > 0 else 0
                    
                    self.memory_stats.append({
                        "epoch": epoch,
                        "memory_allocated": memory_allocated,
                        "memory_reserved": memory_reserved,
                        "memory_usage": memory_usage
                    })
                    
                    # 内存使用过高时清理
                    if memory_usage > memory_config["max_memory_usage"]:
                        print(f"内存使用率 {memory_usage:.2%},执行清理...")
                        torch.cuda.empty_cache()
                        gc.collect()
                
                # 训练一个epoch
                if memory_config["use_amp"]:
                    train_loss = await self.train_epoch_with_amp()
                else:
                    train_loss = await self.trainer.train()
                
                # 验证
                val_loss = await self.trainer.vali()
                
                # 定期内存清理
                if epoch % memory_config["memory_cleanup_interval"] == 0:
                    torch.cuda.empty_cache()
                    gc.collect()
                
                print(f"Epoch {epoch}: Train Loss {train_loss:.6f}, Val Loss {val_loss:.6f}")
                
                # 早停检查
                if hasattr(self.trainer, 'early_stopping_check'):
                    if self.trainer.early_stopping_check(val_loss):
                        print(f"早停于第 {epoch} 轮")
                        break
        
        async def train_epoch_with_amp(self):
            """使用混合精度的训练轮次"""
            total_loss = 0
            num_batches = 0
            
            # 这里应该是实际的训练循环
            # 为了示例,我们调用原始训练方法
            return await self.trainer.train()
        
        def get_memory_report(self):
            """获取内存使用报告"""
            if not self.memory_stats:
                return "无内存统计数据"
            
            avg_usage = sum(s["memory_usage"] for s in self.memory_stats) / len(self.memory_stats)
            max_usage = max(s["memory_usage"] for s in self.memory_stats)
            
            return {
                "average_memory_usage": avg_usage,
                "max_memory_usage": max_usage,
                "total_epochs": len(self.memory_stats)
            }
    
    # 创建基础训练器
    base_trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="memory_optimized_training",
        local_test_mode=True,
        
        # 模型配置
        algorithm_name="Transformer",
        seq_len=96,
        pred_len=24,
        enc_in=7,
        dec_in=7,
        c_out=7,
        d_model=512,
        n_heads=8,
        e_layers=2,
        d_layers=1,
        
        # 内存优化的训练配置
        batch_size=16,  # 较小的批次大小
        learning_rate=0.0001,
        num_epochs=100,
        
        # 启用内存优化选项
        **memory_config
    )
    
    # 创建内存优化训练器
    memory_trainer = MemoryOptimizedTrainer(base_trainer)
    
    print("开始内存优化训练...")
    
    # 执行内存优化训练
    await memory_trainer.train_with_memory_management()
    
    # 获取内存使用报告
    memory_report = memory_trainer.get_memory_report()
    print(f"\n内存使用报告: {memory_report}")
    
    print("内存优化训练完成！")

# 运行示例
# asyncio.run(memory_optimized_training())
```

### 2. 分布式训练示例

```python
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP

def setup_distributed(rank, world_size):
    """设置分布式训练环境"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    
    # 初始化进程组
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    
    # 设置当前进程的GPU
    torch.cuda.set_device(rank)

def cleanup_distributed():
    """清理分布式训练环境"""
    dist.destroy_process_group()

async def distributed_training_worker(rank, world_size):
    """分布式训练工作进程"""
    
    print(f"启动分布式训练进程 {rank}/{world_size}")
    
    # 设置分布式环境
    setup_distributed(rank, world_size)
    
    try:
        # 创建训练器
        trainer = await create_async_trainer(
            trainer_type="async_time_series_sequence",
            project_name=f"distributed_training_rank_{rank}",
            local_test_mode=True,
            
            # 模型配置
            algorithm_name="Transformer",
            seq_len=96,
            pred_len=24,
            enc_in=7,
            dec_in=7,
            c_out=7,
            d_model=512,
            n_heads=8,
            e_layers=2,
            d_layers=1,
            
            # 分布式训练配置
            batch_size=32 // world_size,  # 每个进程的批次大小
            learning_rate=0.0001 * world_size,  # 线性缩放学习率
            num_epochs=100,
            
            # 分布式特定配置
            distributed=True,
            rank=rank,
            world_size=world_size
        )
        
        # 包装模型为分布式模型
        if hasattr(trainer, 'model'):
            trainer.model = DDP(trainer.model, device_ids=[rank])
        
        # 执行训练
        await trainer.main()
        
        if rank == 0:
            print("分布式训练完成！")
    
    finally:
        # 清理分布式环境
        cleanup_distributed()

def run_distributed_training(world_size=2):
    """运行分布式训练"""
    
    # 检查GPU数量
    if not torch.cuda.is_available():
        print("分布式训练需要GPU支持")
        return
    
    if torch.cuda.device_count() < world_size:
        print(f"需要至少 {world_size} 个GPU,当前只有 {torch.cuda.device_count()} 个")
        return
    
    print(f"启动 {world_size} 个进程的分布式训练")
    
    # 启动多进程
    mp.spawn(
        distributed_training_worker,
        args=(world_size,),
        nprocs=world_size,
        join=True
    )

# 运行示例(需要多GPU环境)
# run_distributed_training(world_size=2)
```

## 错误处理示例

### 1. 健壮的错误处理

```python
import traceback
from typing import Optional, Dict, Any

class TrainingError(Exception):
    """训练错误基类"""
    pass

class ModelConfigError(TrainingError):
    """模型配置错误"""
    pass

class DataError(TrainingError):
    """数据错误"""
    pass

class ResourceError(TrainingError):
    """资源错误"""
    pass

async def robust_training_with_error_handling():
    """健壮的错误处理训练示例"""
    
    async def create_trainer_with_validation(config: Dict[str, Any]) -> Optional[Any]:
        """创建训练器并验证配置"""
        
        try:
            # 验证必需的配置项
            required_fields = ['trainer_type', 'project_name', 'algorithm_name']
            for field in required_fields:
                if field not in config:
                    raise ModelConfigError(f"缺少必需配置项: {field}")
            
            # 验证训练器类型
            valid_trainer_types = ['async_time_series_sequence', 'async_time_series_classic']
            if config['trainer_type'] not in valid_trainer_types:
                raise ModelConfigError(f"无效的训练器类型: {config['trainer_type']}")
            
            # 验证算法名称
            if config['trainer_type'] == 'async_time_series_sequence':
                valid_algorithms = ['Transformer', 'Informer', 'Autoformer']
            else:
                valid_algorithms = ['LSTM', 'GRU', 'RNN']
            
            if config['algorithm_name'] not in valid_algorithms:
                raise ModelConfigError(f"算法 {config['algorithm_name']} 不支持训练器类型 {config['trainer_type']}")
            
            # 验证数值配置
            numeric_configs = {
                'batch_size': (1, 1024),
                'learning_rate': (1e-6, 1.0),
                'num_epochs': (1, 1000)
            }
            
            for key, (min_val, max_val) in numeric_configs.items():
                if key in config:
                    value = config[key]
                    if not isinstance(value, (int, float)) or not (min_val <= value <= max_val):
                        raise ModelConfigError(f"{key} 必须在 {min_val} 到 {max_val} 之间")
            
            # 创建训练器
            trainer = await create_async_trainer(**config)
            return trainer
            
        except ModelConfigError:
            raise
        except Exception as e:
            raise ModelConfigError(f"创建训练器时发生未知错误: {e}") from e
    
    async def execute_training_with_retry(trainer, max_retries: int = 3) -> bool:
        """执行训练并重试"""
        
        for attempt in range(max_retries):
            try:
                print(f"开始训练尝试 {attempt + 1}/{max_retries}")
                
                # 检查资源
                if torch.cuda.is_available():
                    memory_free = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()
                    if memory_free < 1e9:  # 少于1GB可用内存
                        raise ResourceError("GPU内存不足")
                
                # 执行训练
                await trainer.main()
                
                print(f"训练成功完成于第 {attempt + 1} 次尝试")
                return True
                
            except ResourceError as e:
                print(f"资源错误: {e}")
                if attempt < max_retries - 1:
                    print("清理资源后重试...")
                    torch.cuda.empty_cache()
                    await asyncio.sleep(5)
                else:
                    print("资源错误,所有重试都失败")
                    raise
            
            except DataError as e:
                print(f"数据错误: {e}")
                if attempt < max_retries - 1:
                    print("重新加载数据后重试...")
                    # 这里可以添加数据重新加载逻辑
                    await asyncio.sleep(2)
                else:
                    print("数据错误,所有重试都失败")
                    raise
            
            except Exception as e:
                print(f"训练尝试 {attempt + 1} 失败: {e}")
                print(f"错误详情: {traceback.format_exc()}")
                
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print("所有训练尝试都失败")
                    raise
        
        return False
    
    # 定义多个训练配置
    training_configs = [
        {
            "trainer_type": "async_time_series_sequence",
            "project_name": "robust_transformer",
            "algorithm_name": "Transformer",
            "local_test_mode": True,
            "seq_len": 96,
            "pred_len": 24,
            "enc_in": 7,
            "dec_in": 7,
            "c_out": 7,
            "d_model": 256,
            "n_heads": 4,
            "e_layers": 2,
            "d_layers": 1,
            "batch_size": 32,
            "learning_rate": 0.0001,
            "num_epochs": 50
        },
        {
            "trainer_type": "async_time_series_classic",
            "project_name": "robust_lstm",
            "algorithm_name": "LSTM",
            "local_test_mode": True,
            "model_parameter": {
                "input_size": 7,
                "hidden_size": 64,
                "num_layers": 2,
                "output_size": 1,
                "dropout": 0.1
            },
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 50
        }
    ]
    
    # 执行所有训练配置
    results = []
    
    for i, config in enumerate(training_configs):
        try:
            print(f"\n处理配置 {i+1}/{len(training_configs)}: {config['project_name']}")
            
            # 创建训练器并验证配置
            trainer = await create_trainer_with_validation(config)
            
            # 执行训练并重试
            success = await execute_training_with_retry(trainer, max_retries=3)
            
            results.append({
                "config_id": i,
                "project_name": config['project_name'],
                "status": "success" if success else "failed",
                "trainer_type": config['trainer_type'],
                "algorithm_name": config['algorithm_name']
            })
            
        except ModelConfigError as e:
            print(f"配置错误: {e}")
            results.append({
                "config_id": i,
                "project_name": config.get('project_name', 'unknown'),
                "status": "config_error",
                "error": str(e)
            })
        
        except Exception as e:
            print(f"未知错误: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            results.append({
                "config_id": i,
                "project_name": config.get('project_name', 'unknown'),
                "status": "unknown_error",
                "error": str(e)
            })
    
    # 汇总结果
    successful = sum(1 for r in results if r["status"] == "success")
    failed = len(results) - successful
    
    print(f"\n训练结果汇总:")
    print(f"成功: {successful}/{len(results)}")
    print(f"失败: {failed}/{len(results)}")
    
    for result in results:
        status_icon = "✅" if result["status"] == "success" else "❌"
        print(f"{status_icon} {result['project_name']} - {result['status']}")
        if "error" in result:
            print(f"   错误: {result['error']}")
    
    return results

# 运行示例
# results = asyncio.run(robust_training_with_error_handling())
```

### 2. 自动恢复训练

```python
import pickle
from pathlib import Path

class TrainingCheckpoint:
    """训练检查点管理器"""
    
    def __init__(self, checkpoint_dir: str):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    def save_checkpoint(self, trainer, epoch: int, loss: float, additional_data: dict = None):
        """保存训练检查点"""
        checkpoint_data = {
            "epoch": epoch,
            "loss": loss,
            "project_name": trainer.project_name,
            "algorithm_name": trainer.algorithm_name,
            "model_state": getattr(trainer, 'model_state_dict', None),
            "optimizer_state": getattr(trainer, 'optimizer_state_dict', None),
            "config": getattr(trainer, 'config', {}),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        if additional_data:
            checkpoint_data.update(additional_data)
        
        checkpoint_file = self.checkpoint_dir / f"{trainer.project_name}_epoch_{epoch}.pkl"
        
        with open(checkpoint_file, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        print(f"保存检查点: {checkpoint_file}")
        return checkpoint_file
    
    def load_latest_checkpoint(self, project_name: str) -> Optional[dict]:
        """加载最新的检查点"""
        checkpoint_files = list(self.checkpoint_dir.glob(f"{project_name}_epoch_*.pkl"))
        
        if not checkpoint_files:
            return None
        
        # 找到最新的检查点
        latest_file = max(checkpoint_files, key=lambda f: f.stat().st_mtime)
        
        with open(latest_file, 'rb') as f:
            checkpoint_data = pickle.load(f)
        
        print(f"加载检查点: {latest_file}")
        return checkpoint_data
    
    def cleanup_old_checkpoints(self, project_name: str, keep_last: int = 5):
        """清理旧的检查点"""
        checkpoint_files = list(self.checkpoint_dir.glob(f"{project_name}_epoch_*.pkl"))
        
        if len(checkpoint_files) <= keep_last:
            return
        
        # 按修改时间排序,删除旧的
        checkpoint_files.sort(key=lambda f: f.stat().st_mtime)
        files_to_delete = checkpoint_files[:-keep_last]
        
        for file_path in files_to_delete:
            file_path.unlink()
            print(f"删除旧检查点: {file_path}")

async def auto_recovery_training():
    """自动恢复训练示例"""
    
    checkpoint_manager = TrainingCheckpoint("/tmp/training_checkpoints")
    
    project_name = "auto_recovery_transformer"
    
    # 尝试加载现有检查点
    checkpoint = checkpoint_manager.load_latest_checkpoint(project_name)
    
    if checkpoint:
        print(f"发现检查点,从第 {checkpoint['epoch']} 轮恢复训练")
        print(f"上次损失: {checkpoint['loss']:.6f}")
        
        # 从检查点恢复配置
        config = checkpoint['config']
        start_epoch = checkpoint['epoch'] + 1
    else:
        print("未发现检查点,从头开始训练")
        
        # 新训练配置
        config = {
            "trainer_type": "async_time_series_sequence",
            "project_name": project_name,
            "algorithm_name": "Transformer",
            "local_test_mode": True,
            "seq_len": 96,
            "pred_len": 24,
            "enc_in": 7,
            "dec_in": 7,
            "c_out": 7,
            "d_model": 512,
            "n_heads": 8,
            "e_layers": 2,
            "d_layers": 1,
            "batch_size": 32,
            "learning_rate": 0.0001,
            "num_epochs": 100
        }
        start_epoch = 0
    
    try:
        # 创建训练器
        trainer = await create_async_trainer(**config)
        
        # 如果有检查点,恢复模型状态
        if checkpoint and checkpoint.get('model_state'):
            # 这里应该恢复模型和优化器状态
            # trainer.model.load_state_dict(checkpoint['model_state'])
            # trainer.optimizer.load_state_dict(checkpoint['optimizer_state'])
            print("恢复模型和优化器状态")
        
        # 模拟训练循环(实际应该调用 trainer.main())
        for epoch in range(start_epoch, config['num_epochs']):
            try:
                # 模拟训练
                print(f"训练第 {epoch} 轮...")
                
                # 这里应该是实际的训练代码
                # train_loss = await trainer.train_epoch()
                # val_loss = await trainer.validate_epoch()
                
                # 模拟损失值
                import random
                train_loss = random.uniform(0.1, 1.0)
                val_loss = random.uniform(0.1, 1.0)
                
                print(f"第 {epoch} 轮: 训练损失 {train_loss:.6f}, 验证损失 {val_loss:.6f}")
                
                # 每5轮保存一次检查点
                if epoch % 5 == 0:
                    checkpoint_manager.save_checkpoint(
                        trainer, epoch, val_loss,
                        {"train_loss": train_loss, "val_loss": val_loss}
                    )
                    
                    # 清理旧检查点
                    checkpoint_manager.cleanup_old_checkpoints(project_name, keep_last=3)
                
                # 模拟随机失败
                if random.random() < 0.1:  # 10% 概率失败
                    raise RuntimeError(f"模拟训练失败于第 {epoch} 轮")
            
            except Exception as e:
                print(f"第 {epoch} 轮训练失败: {e}")
                
                # 保存失败时的检查点
                checkpoint_manager.save_checkpoint(
                    trainer, epoch, float('inf'),
                    {"error": str(e), "failed_epoch": epoch}
                )
                
                raise
        
        print("训练成功完成！")
        
        # 保存最终检查点
        checkpoint_manager.save_checkpoint(
            trainer, config['num_epochs'] - 1, 0.0,
            {"status": "completed"}
        )
    
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        print("检查点已保存,可以稍后恢复训练")
        raise

# 运行示例
# asyncio.run(auto_recovery_training())
```

## 监控和调试示例

### 1. 实时监控训练

```python
import time
import json
from collections import deque
from typing import List, Dict

class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.metrics_history = deque(maxlen=window_size)
        self.start_time = None
        self.alerts = []
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        print("开始训练监控...")
    
    def log_metrics(self, epoch: int, metrics: Dict[str, float]):
        """记录指标"""
        timestamp = time.time()
        
        metric_entry = {
            "epoch": epoch,
            "timestamp": timestamp,
            "elapsed_time": timestamp - self.start_time if self.start_time else 0,
            **metrics
        }
        
        self.metrics_history.append(metric_entry)
        
        # 检查异常
        self.check_anomalies(metric_entry)
        
        # 打印实时状态
        self.print_status(metric_entry)
    
    def check_anomalies(self, current_metrics: Dict[str, float]):
        """检查异常情况"""
        
        if len(self.metrics_history) < 10:
            return
        
        # 检查损失是否停止下降
        recent_losses = [m.get('train_loss', float('inf')) for m in list(self.metrics_history)[-10:]]
        if len(set(recent_losses)) == 1 and recent_losses[0] != float('inf'):
            self.add_alert("警告: 训练损失在最近10轮中没有变化")
        
        # 检查损失是否爆炸
        current_loss = current_metrics.get('train_loss', 0)
        if current_loss > 100:
            self.add_alert(f"警告: 训练损失过高 ({current_loss:.2f}),可能发生梯度爆炸")
        
        # 检查学习率是否过高
        if len(self.metrics_history) >= 5:
            recent_losses = [m.get('train_loss', float('inf')) for m in list(self.metrics_history)[-5:]]
            if all(recent_losses[i] > recent_losses[i-1] for i in range(1, len(recent_losses))):
                self.add_alert("警告: 训练损失持续上升,学习率可能过高")
        
        # 检查内存使用
        memory_usage = current_metrics.get('memory_usage', 0)
        if memory_usage > 0.9:
            self.add_alert(f"警告: 内存使用率过高 ({memory_usage:.1%})")
    
    def add_alert(self, message: str):
        """添加警报"""
        alert = {
            "timestamp": time.time(),
            "message": message
        }
        self.alerts.append(alert)
        print(f"🚨 {message}")
    
    def print_status(self, metrics: Dict[str, float]):
        """打印状态"""
        epoch = metrics['epoch']
        elapsed = metrics['elapsed_time']
        
        status_line = f"Epoch {epoch:3d} | 耗时: {elapsed:6.1f}s"
        
        if 'train_loss' in metrics:
            status_line += f" | 训练损失: {metrics['train_loss']:.6f}"
        
        if 'val_loss' in metrics:
            status_line += f" | 验证损失: {metrics['val_loss']:.6f}"
        
        if 'learning_rate' in metrics:
            status_line += f" | 学习率: {metrics['learning_rate']:.2e}"
        
        if 'memory_usage' in metrics:
            status_line += f" | 内存: {metrics['memory_usage']:.1%}"
        
        print(status_line)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        # 计算统计信息
        train_losses = [m.get('train_loss') for m in self.metrics_history if 'train_loss' in m]
        val_losses = [m.get('val_loss') for m in self.metrics_history if 'val_loss' in m]
        
        summary = {
            "total_epochs": len(self.metrics_history),
            "total_time": self.metrics_history[-1]['elapsed_time'] if self.metrics_history else 0,
            "alerts_count": len(self.alerts),
            "recent_alerts": self.alerts[-5:] if self.alerts else []
        }
        
        if train_losses:
            summary.update({
                "best_train_loss": min(train_losses),
                "latest_train_loss": train_losses[-1],
                "train_loss_improvement": train_losses[0] - train_losses[-1] if len(train_losses) > 1 else 0
            })
        
        if val_losses:
            summary.update({
                "best_val_loss": min(val_losses),
                "latest_val_loss": val_losses[-1],
                "val_loss_improvement": val_losses[0] - val_losses[-1] if len(val_losses) > 1 else 0
            })
        
        return summary

async def monitored_training_example():
    """带监控的训练示例"""
    
    # 创建监控器
    monitor = TrainingMonitor(window_size=200)
    
    # 创建训练器
    trainer = await create_async_trainer(
        trainer_type="async_time_series_sequence",
        project_name="monitored_training",
        local_test_mode=True,
        
        algorithm_name="Transformer",
        seq_len=96,
        pred_len=24,
        enc_in=7,
        dec_in=7,
        c_out=7,
        d_model=256,
        n_heads=4,
        e_layers=2,
        d_layers=1,
        batch_size=32,
        learning_rate=0.001,
        num_epochs=100
    )
    
    # 开始监控
    monitor.start_monitoring()
    
    try:
        # 模拟训练循环
        for epoch in range(trainer.num_epochs):
            # 模拟训练指标
            import random
            
            # 模拟逐渐改善的损失
            base_loss = 1.0 * (0.95 ** epoch) + random.uniform(-0.1, 0.1)
            train_loss = max(0.01, base_loss + random.uniform(-0.05, 0.05))
            val_loss = max(0.01, base_loss + random.uniform(-0.03, 0.08))
            
            # 模拟其他指标
            learning_rate = trainer.learning_rate * (0.99 ** epoch)
            memory_usage = random.uniform(0.3, 0.8)
            
            # 记录指标
            metrics = {
                "train_loss": train_loss,
                "val_loss": val_loss,
                "learning_rate": learning_rate,
                "memory_usage": memory_usage
            }
            
            monitor.log_metrics(epoch, metrics)
            
            # 模拟训练时间
            await asyncio.sleep(0.1)
            
            # 模拟早停
            if epoch > 20 and val_loss < 0.05:
                print(f"早停于第 {epoch} 轮")
                break
        
        print("\n训练完成！")
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        monitor.add_alert(f"训练失败: {e}")
    
    finally:
        # 打印监控摘要
        summary = monitor.get_summary()
        print("\n=== 训练监控摘要 ===")
        print(f"总轮数: {summary.get('total_epochs', 0)}")
        print(f"总耗时: {summary.get('total_time', 0):.1f} 秒")
        print(f"警报数量: {summary.get('alerts_count', 0)}")
        
        if 'best_train_loss' in summary:
            print(f"最佳训练损失: {summary['best_train_loss']:.6f}")
            print(f"最终训练损失: {summary['latest_train_loss']:.6f}")
            print(f"训练损失改善: {summary['train_loss_improvement']:.6f}")
        
        if 'best_val_loss' in summary:
            print(f"最佳验证损失: {summary['best_val_loss']:.6f}")
            print(f"最终验证损失: {summary['latest_val_loss']:.6f}")
            print(f"验证损失改善: {summary['val_loss_improvement']:.6f}")
        
        if summary.get('recent_alerts'):
            print("\n最近的警报:")
            for alert in summary['recent_alerts']:
                print(f"  - {alert['message']}")

# 运行示例
# asyncio.run(monitored_training_example())
```

### 2. 调试工具

```python
import inspect
import sys
from contextlib import asynccontextmanager

class AsyncTrainingDebugger:
    """异步训练调试器"""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.call_stack = []
        self.performance_stats = {}
        self.memory_snapshots = []
    
    @asynccontextmanager
    async def debug_context(self, operation_name: str):
        """调试上下文管理器"""
        start_time = time.time()
        
        if torch.cuda.is_available():
            start_memory = torch.cuda.memory_allocated()
        else:
            start_memory = 0
        
        self.call_stack.append(operation_name)
        
        if self.verbose:
            indent = "  " * (len(self.call_stack) - 1)
            print(f"{indent}🔍 开始: {operation_name}")
        
        try:
            yield self
        except Exception as e:
            if self.verbose:
                indent = "  " * (len(self.call_stack) - 1)
                print(f"{indent}❌ 错误: {operation_name} - {e}")
            raise
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            if torch.cuda.is_available():
                end_memory = torch.cuda.memory_allocated()
                memory_delta = end_memory - start_memory
            else:
                memory_delta = 0
            
            # 记录性能统计
            if operation_name not in self.performance_stats:
                self.performance_stats[operation_name] = []
            
            self.performance_stats[operation_name].append({
                "duration": duration,
                "memory_delta": memory_delta,
                "timestamp": end_time
            })
            
            if self.verbose:
                indent = "  " * (len(self.call_stack) - 1)
                print(f"{indent}✅ 完成: {operation_name} ({duration:.3f}s, {memory_delta/1024/1024:.1f}MB)")
            
            self.call_stack.pop()
    
    def log_tensor_info(self, tensor_name: str, tensor):
        """记录张量信息"""
        if hasattr(tensor, 'shape') and hasattr(tensor, 'dtype'):
            info = f"张量 {tensor_name}: shape={tensor.shape}, dtype={tensor.dtype}"
            if hasattr(tensor, 'device'):
                info += f", device={tensor.device}"
            if hasattr(tensor, 'requires_grad'):
                info += f", requires_grad={tensor.requires_grad}"
            
            if self.verbose:
                print(f"📊 {info}")
    
    def log_model_info(self, model):
        """记录模型信息"""
        if hasattr(model, 'parameters'):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            info = f"模型参数: 总计={total_params:,}, 可训练={trainable_params:,}"
            
            if self.verbose:
                print(f"🏗️ {info}")
    
    def take_memory_snapshot(self, label: str):
        """拍摄内存快照"""
        if torch.cuda.is_available():
            snapshot = {
                "label": label,
                "timestamp": time.time(),
                "allocated": torch.cuda.memory_allocated(),
                "reserved": torch.cuda.memory_reserved(),
                "max_allocated": torch.cuda.max_memory_allocated()
            }
            
            self.memory_snapshots.append(snapshot)
            
            if self.verbose:
                print(f"📸 内存快照 {label}: "
                      f"已分配={snapshot['allocated']/1024/1024:.1f}MB, "
                      f"已保留={snapshot['reserved']/1024/1024:.1f}MB")
    
    def get_performance_report(self) -> str:
        """获取性能报告"""
        report = ["\n=== 性能调试报告 ==="]
        
        for operation, stats in self.performance_stats.items():
            if not stats:
                continue
            
            durations = [s['duration'] for s in stats]
            memory_deltas = [s['memory_delta'] for s in stats]
            
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            total_duration = sum(durations)
            
            avg_memory = sum(memory_deltas) / len(memory_deltas) / 1024 / 1024
            max_memory = max(memory_deltas) / 1024 / 1024
            
            report.append(f"\n操作: {operation}")
            report.append(f"  调用次数: {len(stats)}")
            report.append(f"  平均耗时: {avg_duration:.3f}s")
            report.append(f"  最大耗时: {max_duration:.3f}s")
            report.append(f"  总耗时: {total_duration:.3f}s")
            report.append(f"  平均内存变化: {avg_memory:.1f}MB")
            report.append(f"  最大内存变化: {max_memory:.1f}MB")
        
        if self.memory_snapshots:
            report.append("\n=== 内存快照 ===")
            for snapshot in self.memory_snapshots:
                report.append(f"{snapshot['label']}: {snapshot['allocated']/1024/1024:.1f}MB")
        
        return "\n".join(report)

async def debugging_training_example():
    """调试训练示例"""
    
    # 创建调试器
    debugger = AsyncTrainingDebugger(verbose=True)
    
    async with debugger.debug_context("整体训练流程"):
        
        async with debugger.debug_context("创建训练器"):
            trainer = await create_async_trainer(
                trainer_type="async_time_series_sequence",
                project_name="debugging_training",
                local_test_mode=True,
                
                algorithm_name="Transformer",
                seq_len=96,
                pred_len=24,
                enc_in=7,
                dec_in=7,
                c_out=7,
                d_model=256,
                n_heads=4,
                e_layers=2,
                d_layers=1,
                batch_size=16,  # 较小的批次用于调试
                learning_rate=0.001,
                num_epochs=10   # 较少的轮数用于调试
            )
        
        # 记录模型信息
        if hasattr(trainer, 'model'):
            debugger.log_model_info(trainer.model)
        
        # 拍摄初始内存快照
        debugger.take_memory_snapshot("训练开始前")
        
        async with debugger.debug_context("模拟训练循环"):
            for epoch in range(5):  # 只训练5轮用于演示
                
                async with debugger.debug_context(f"第{epoch}轮训练"):
                    
                    # 模拟前向传播
                    async with debugger.debug_context("前向传播"):
                        # 这里应该是实际的前向传播代码
                        await asyncio.sleep(0.1)  # 模拟计算时间
                    
                    # 模拟反向传播
                    async with debugger.debug_context("反向传播"):
                        # 这里应该是实际的反向传播代码
                        await asyncio.sleep(0.05)  # 模拟计算时间
                    
                    # 模拟参数更新
                    async with debugger.debug_context("参数更新"):
                        # 这里应该是实际的参数更新代码
                        await asyncio.sleep(0.02)  # 模拟计算时间
                    
                    # 拍摄内存快照
                    debugger.take_memory_snapshot(f"第{epoch}轮结束")
                
                # 模拟验证
                async with debugger.debug_context(f"第{epoch}轮验证"):
                    await asyncio.sleep(0.05)  # 模拟验证时间
        
        # 拍摄最终内存快照
        debugger.take_memory_snapshot("训练结束")
    
    # 打印性能报告
    print(debugger.get_performance_report())

# 运行示例
# asyncio.run(debugging_training_example())
```

## 集成应用示例

### 1. Web API 集成

```python
from fastapi import FastAPI, BackgroundTasks, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import uuid

app = FastAPI(title="异步训练 API")

# 全局训练任务存储
training_tasks: Dict[str, dict] = {}

class TrainingRequest(BaseModel):
    """训练请求模型"""
    trainer_type: str
    project_name: str
    algorithm_name: str
    model_config: Dict
    training_config: Dict
    local_test_mode: bool = True

class TrainingStatus(BaseModel):
    """训练状态模型"""
    task_id: str
    status: str  # pending, running, completed, failed
    progress: Optional[float] = None
    current_epoch: Optional[int] = None
    total_epochs: Optional[int] = None
    metrics: Optional[Dict] = None
    error_message: Optional[str] = None
    created_at: float
    updated_at: float

async def execute_training_task(task_id: str, request: TrainingRequest):
    """执行训练任务"""
    try:
        # 更新任务状态
        training_tasks[task_id]["status"] = "running"
        training_tasks[task_id]["updated_at"] = time.time()
        
        # 合并配置
        config = {
            "trainer_type": request.trainer_type,
            "project_name": f"{request.project_name}_{task_id}",
            "algorithm_name": request.algorithm_name,
            "local_test_mode": request.local_test_mode,
            **request.model_config,
            **request.training_config
        }
        
        # 创建训练器
        trainer = await create_async_trainer(**config)
        
        # 模拟训练进度更新
        total_epochs = config.get("num_epochs", 100)
        
        for epoch in range(total_epochs):
            # 模拟训练
            await asyncio.sleep(0.1)  # 实际应该是 trainer.train_epoch()
            
            # 更新进度
            progress = (epoch + 1) / total_epochs
            training_tasks[task_id].update({
                "progress": progress,
                "current_epoch": epoch + 1,
                "total_epochs": total_epochs,
                "updated_at": time.time()
            })
            
            # 模拟指标
            if epoch % 10 == 0:
                training_tasks[task_id]["metrics"] = {
                    "train_loss": 1.0 * (0.95 ** epoch),
                    "val_loss": 1.1 * (0.95 ** epoch)
                }
        
        # 训练完成
        training_tasks[task_id].update({
            "status": "completed",
            "progress": 1.0,
            "updated_at": time.time()
        })
        
    except Exception as e:
        # 训练失败
        training_tasks[task_id].update({
            "status": "failed",
            "error_message": str(e),
            "updated_at": time.time()
        })

@app.post("/training/start", response_model=Dict[str, str])
async def start_training(request: TrainingRequest, background_tasks: BackgroundTasks):
    """启动训练任务"""
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 创建任务记录
    training_tasks[task_id] = {
        "task_id": task_id,
        "status": "pending",
        "progress": 0.0,
        "current_epoch": 0,
        "total_epochs": request.training_config.get("num_epochs", 100),
        "created_at": time.time(),
        "updated_at": time.time(),
        "request": request.dict()
    }
    
    # 添加后台任务
    background_tasks.add_task(execute_training_task, task_id, request)
    
    return {"task_id": task_id, "status": "started"}

@app.get("/training/{task_id}/status", response_model=TrainingStatus)
async def get_training_status(task_id: str):
    """获取训练状态"""
    
    if task_id not in training_tasks:
        raise HTTPException(status_code=404, detail="训练任务不存在")
    
    task_data = training_tasks[task_id]
    
    return TrainingStatus(
        task_id=task_data["task_id"],
        status=task_data["status"],
        progress=task_data.get("progress"),
        current_epoch=task_data.get("current_epoch"),
        total_epochs=task_data.get("total_epochs"),
        metrics=task_data.get("metrics"),
        error_message=task_data.get("error_message"),
        created_at=task_data["created_at"],
        updated_at=task_data["updated_at"]
    )

@app.get("/training/list", response_model=List[TrainingStatus])
async def list_training_tasks():
    """列出所有训练任务"""
    
    tasks = []
    for task_data in training_tasks.values():
        tasks.append(TrainingStatus(
            task_id=task_data["task_id"],
            status=task_data["status"],
            progress=task_data.get("progress"),
            current_epoch=task_data.get("current_epoch"),
            total_epochs=task_data.get("total_epochs"),
            metrics=task_data.get("metrics"),
            error_message=task_data.get("error_message"),
            created_at=task_data["created_at"],
            updated_at=task_data["updated_at"]
        ))
    
    return sorted(tasks, key=lambda x: x.created_at, reverse=True)

@app.delete("/training/{task_id}")
async def cancel_training(task_id: str):
    """取消训练任务"""
    
    if task_id not in training_tasks:
        raise HTTPException(status_code=404, detail="训练任务不存在")
    
    task_data = training_tasks[task_id]
    
    if task_data["status"] == "running":
        # 这里应该实际停止训练进程
        task_data.update({
            "status": "cancelled",
            "updated_at": time.time()
        })
        return {"message": "训练任务已取消"}
    else:
        return {"message": f"任务状态为 {task_data['status']},无法取消"}

# 使用示例客户端代码
async def api_client_example():
    """API 客户端示例"""
    import httpx
    
    base_url = "http://localhost:8000"
    
    # 创建训练请求
    training_request = {
        "trainer_type": "async_time_series_sequence",
        "project_name": "api_transformer",
        "algorithm_name": "Transformer",
        "model_config": {
            "seq_len": 96,
            "pred_len": 24,
            "enc_in": 7,
            "dec_in": 7,
            "c_out": 7,
            "d_model": 256,
            "n_heads": 4,
            "e_layers": 2,
            "d_layers": 1
        },
        "training_config": {
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 50
        },
        "local_test_mode": True
    }
    
    async with httpx.AsyncClient() as client:
        # 启动训练
        response = await client.post(f"{base_url}/training/start", json=training_request)
        task_info = response.json()
        task_id = task_info["task_id"]
        
        print(f"训练任务已启动: {task_id}")
        
        # 监控训练进度
        while True:
            response = await client.get(f"{base_url}/training/{task_id}/status")
            status = response.json()
            
            print(f"状态: {status['status']}, 进度: {status.get('progress', 0):.1%}")
            
            if status["status"] in ["completed", "failed", "cancelled"]:
                break
            
            await asyncio.sleep(2)
        
        print(f"训练结束,最终状态: {status['status']}")
        if status.get("metrics"):
            print(f"最终指标: {status['metrics']}")

# 启动 API 服务器
# uvicorn main:app --reload --port 8000

# 运行客户端示例
# asyncio.run(api_client_example())
```

### 2. 配置文件驱动的批量训练

```python
import yaml
import json
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any

@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str
    description: str
    trainer_type: str
    algorithm_name: str
    model_config: Dict[str, Any]
    training_config: Dict[str, Any]
    data_config: Dict[str, Any]
    enabled: bool = True

class ExperimentManager:
    """实验管理器"""
    
    def __init__(self, config_file: str, results_dir: str = "./experiment_results"):
        self.config_file = Path(config_file)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        self.experiments = self.load_experiments()
    
    def load_experiments(self) -> List[ExperimentConfig]:
        """加载实验配置"""
        
        if not self.config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            if self.config_file.suffix.lower() == '.yaml' or self.config_file.suffix.lower() == '.yml':
                config_data = yaml.safe_load(f)
            else:
                config_data = json.load(f)
        
        experiments = []
        for exp_data in config_data.get('experiments', []):
            experiments.append(ExperimentConfig(**exp_data))
        
        return experiments
    
    async def run_experiment(self, experiment: ExperimentConfig) -> Dict[str, Any]:
        """运行单个实验"""
        
        print(f"开始实验: {experiment.name}")
        print(f"描述: {experiment.description}")
        
        start_time = time.time()
        
        try:
            # 合并配置
            config = {
                "trainer_type": experiment.trainer_type,
                "project_name": f"exp_{experiment.name}",
                "algorithm_name": experiment.algorithm_name,
                **experiment.model_config,
                **experiment.training_config,
                **experiment.data_config
            }
            
            # 创建训练器
            trainer = await create_async_trainer(**config)
            
            # 执行训练
            await trainer.main()
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 收集结果
            result = {
                "experiment_name": experiment.name,
                "status": "success",
                "duration": duration,
                "config": config,
                "metrics": {
                    "final_loss": getattr(trainer, 'best_loss', None),
                    "best_epoch": getattr(trainer, 'best_epoch', None)
                },
                "timestamp": end_time
            }
            
            print(f"实验 {experiment.name} 成功完成,耗时 {duration:.1f} 秒")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                "experiment_name": experiment.name,
                "status": "failed",
                "duration": duration,
                "error": str(e),
                "timestamp": end_time
            }
            
            print(f"实验 {experiment.name} 失败: {e}")
        
        # 保存结果
        self.save_experiment_result(result)
        
        return result
    
    def save_experiment_result(self, result: Dict[str, Any]):
        """保存实验结果"""
        
        result_file = self.results_dir / f"{result['experiment_name']}_result.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"实验结果已保存: {result_file}")
    
    async def run_all_experiments(self, parallel: bool = False, max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """运行所有实验"""
        
        enabled_experiments = [exp for exp in self.experiments if exp.enabled]
        
        print(f"准备运行 {len(enabled_experiments)} 个实验")
        
        if parallel:
            # 并行运行
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def run_with_semaphore(experiment):
                async with semaphore:
                    return await self.run_experiment(experiment)
            
            results = await asyncio.gather(
                *[run_with_semaphore(exp) for exp in enabled_experiments],
                return_exceptions=True
            )
        else:
            # 串行运行
            results = []
            for experiment in enabled_experiments:
                result = await self.run_experiment(experiment)
                results.append(result)
        
        # 生成汇总报告
        self.generate_summary_report(results)
        
        return results
    
    def generate_summary_report(self, results: List[Dict[str, Any]]):
        """生成汇总报告"""
        
        successful = [r for r in results if isinstance(r, dict) and r.get("status") == "success"]
        failed = [r for r in results if isinstance(r, dict) and r.get("status") == "failed"]
        
        report = {
            "summary": {
                "total_experiments": len(results),
                "successful": len(successful),
                "failed": len(failed),
                "success_rate": len(successful) / len(results) if results else 0
            },
            "successful_experiments": successful,
            "failed_experiments": failed,
            "generated_at": time.time()
        }
        
        # 保存汇总报告
        summary_file = self.results_dir / "experiment_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 实验汇总报告 ===")
        print(f"总实验数: {report['summary']['total_experiments']}")
        print(f"成功: {report['summary']['successful']}")
        print(f"失败: {report['summary']['failed']}")
        print(f"成功率: {report['summary']['success_rate']:.1%}")
        print(f"汇总报告已保存: {summary_file}")

# 示例配置文件 (experiments.yaml)
example_config = """
experiments:
  - name: "transformer_baseline"
    description: "Transformer 基线模型"
    enabled: true
    trainer_type: "async_time_series_sequence"
    algorithm_name: "Transformer"
    model_config:
      seq_len: 96
      pred_len: 24
      enc_in: 7
      dec_in: 7
      c_out: 7
      d_model: 256
      n_heads: 4
      e_layers: 2
      d_layers: 1
    training_config:
      batch_size: 32
      learning_rate: 0.001
      num_epochs: 50
    data_config:
      local_test_mode: true
  
  - name: "informer_comparison"
    description: "Informer 对比模型"
    enabled: true
    trainer_type: "async_time_series_sequence"
    algorithm_name: "Informer"
    model_config:
      seq_len: 96
      pred_len: 24
      enc_in: 7
      dec_in: 7
      c_out: 7
      d_model: 256
      n_heads: 4
      e_layers: 2
      d_layers: 1
      factor: 1
      distil: true
    training_config:
      batch_size: 32
      learning_rate: 0.001
      num_epochs: 50
    data_config:
      local_test_mode: true
  
  - name: "lstm_baseline"
    description: "LSTM 基线模型"
    enabled: true
    trainer_type: "async_time_series_classic"
    algorithm_name: "LSTM"
    model_config:
      model_parameter:
        input_size: 7
        hidden_size: 64
        num_layers: 2
        output_size: 1
        dropout: 0.1
    training_config:
      batch_size: 32
      learning_rate: 0.001
      num_epochs: 50
    data_config:
      local_test_mode: true
"""

async def config_driven_experiments_example():
    """配置文件驱动的实验示例"""
    
    # 创建示例配置文件
    config_file = Path("experiments.yaml")
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(example_config)
    
    print(f"创建示例配置文件: {config_file}")
    
    # 创建实验管理器
    manager = ExperimentManager(str(config_file), "./experiment_results")
    
    print(f"加载了 {len(manager.experiments)} 个实验配置")
    
    # 运行所有实验(串行)
    print("\n开始串行运行实验...")
    results = await manager.run_all_experiments(parallel=False)
    
    print("\n所有实验完成！")
    
    # 清理示例文件
    config_file.unlink()
    
    return results

# 运行示例
# results = asyncio.run(config_driven_experiments_example())
```

## 总结

本文档提供了 `industrytslib` 异步训练功能的全面使用示例,涵盖了从基础使用到高级应用的各种场景:

### 主要特性

1. **基础使用**: 简单的模型训练和配置
2. **高级配置**: 数据库连接、数据增强、长期预测
3. **并发训练**: 多模型并发、参数网格搜索
4. **生产环境**: 生产级配置、模型版本管理
5. **性能优化**: 内存优化、分布式训练
6. **错误处理**: 健壮的错误处理、自动恢复
7. **监控调试**: 实时监控、调试工具
8. **集成应用**: Web API、配置文件驱动

### 最佳实践

- 使用建造者模式创建复杂配置的训练器
- 实施完善的错误处理和重试机制
- 采用检查点机制支持训练恢复
- 集成监控和调试工具提高开发效率
- 使用配置文件管理大规模实验
- 通过 API 集成支持远程训练管理

### 相关文档

- [异步训练器概述](trainers/async_trainer_overview.md)
- [异步基础训练器](trainers/async_basic_trainer.md)
- [异步时序经典训练器](trainers/async_time_series_classic_trainer.md)
- [异步时序序列训练器](trainers/async_time_series_sequence_trainer.md)
- [异步训练管道](trainers/async_training_pipeline.md)

通过这些示例,您可以快速上手并充分利用 `industrytslib` 的异步训练功能,构建高效、可靠的时间序列预测模型。