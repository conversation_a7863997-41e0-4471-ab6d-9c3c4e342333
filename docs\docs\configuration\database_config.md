# 数据库配置文档

本文档描述了 industrytslib 项目中所有数据库连接的配置选项。配置文件使用 TOML 格式,位于 `examples/config/database_config.example.toml`。

## 配置文件结构

数据库配置文件包含多个数据库连接配置,每个配置以 `[section_name]` 的形式定义。

## 支持的数据库类型

- **mssql**: Microsoft SQL Server
- **mongodb**: MongoDB
- **influxdb**: InfluxDB
- **postgres**: PostgreSQL
- **sqllite**: SQLite

## 数据库配置详情

### Web 服务数据库
```toml
[web]
type = "mssql"
target = "web"
server = "************"
port = 1433
database = "eladmin"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: Web 服务后端数据存储
- **数据库**: eladmin
- **备注**: 支持备用服务器配置(注释中的 *********)

### 在线训练器数据库
```toml
[online_trainer]
type = "mssql"
target = "online_trainer"
server = "************"
port = 1433
database = "eladmin"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 在线训练器信息存储
- **数据库**: eladmin

### 实时预测数据库
```toml
[realtimepredict]
type = "mssql"
target = "realtime_predict"
server = "************"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 实时预测数据服务器
- **数据库**: RealtimePredict

### 实时预测备用数据库
```toml
[realtimepredictalter]
type = "mssql"
target = "realtime_predict_alter"
server = "************"
port = 1433
database = "AIPredict"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 实时预测备用数据服务器
- **数据库**: AIPredict

### 实时预测序列数据库
```toml
[realtimepredict_sequence]
type = "mssql"
target = "realtime_predict_sequence"
server = "************"
port = 1433
database = "AIPredict"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 实时预测序列数据服务器
- **数据库**: AIPredict

### 时间序列数据库
```toml
[timeseries]
type = "mssql"
target = "timeseries"
server = "************"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 时间序列数据服务器
- **数据库**: 控制器数据库

### 质量检验数据库
```toml
[quality]
type = "mssql"
target = "quality"
server = "************"
port = 1433
database = "样本检验结果"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 质量检验数据服务器
- **数据库**: 样本检验结果

### 决策历史数据库
```toml
[decision_history]
type = "mssql"
target = "decision_history"
server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 决策历史数据服务器
- **数据库**: 控制器数据库
- **备注**: 使用不同的服务器地址

### 预测事务源数据库
```toml
[predict_transaction_source]
type = "mssql"
target = "predict_transaction_source"
server = "*********"
port = 1433
database = "RealtimePredict"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 预测事务源数据
- **数据库**: RealtimePredict

### 预测事务目标数据库
```toml
[predict_transaction_target]
type = "mssql"
target = "predict_transaction_target"
server = "*********"
port = 1433
database = "控制器数据库"
username = "sa"
password = "ysdxdckj@666"
```
- **用途**: 预测事务目标数据
- **数据库**: 控制器数据库

### MongoDB 配置
```toml
[mongodb]
type = "mongodb"
url = "mongodb://*********:27017"
database = "timeseries"
username = "admin"
password = "ysdxdckj@666"
```
- **用途**: NoSQL 文档数据库
- **数据库**: timeseries

### InfluxDB 配置
```toml
[influxdb]
type = "influxdb"
url = "http://************:8086"
token = "qAAxOG6EbMOZkVQY2nSKWVASRH5k-YNVNVkJKHUbNc9w9oA54OUfEaXg4VBPLxXeNaTIr_hDp29r414wrY9q0Q=="
org = "dckj"
```
- **用途**: 时间序列数据库
- **组织**: dckj
- **认证**: 使用 Token 认证

### PostgreSQL 配置
```toml
[postgresql]
type = "postgres"
target = "base"
host = "localhost"
port = 5432
database = "mydb"
username = "postgres"
password = "123456"
```
- **用途**: 关系型数据库
- **数据库**: mydb

### 模型信息数据库
```toml
[model_info]
type = "sqllite"
target = "model_info"
database = "config/model_info.db"
```
- **用途**: 模型信息存储
- **文件路径**: config/model_info.db

### 模型信息位置配置
```toml
[model_info_location]
location = "toml"
```
- **支持的位置类型**:
  - `sqlite`: 使用 SQLite 数据库
  - `mssql`: 使用 Microsoft SQL Server
  - `toml`: 使用 TOML 配置文件
- **当前配置**: toml

## 配置参数说明

### 通用参数
- `type`: 数据库类型
- `target`: 目标标识符
- `server`/`host`: 服务器地址
- `port`: 端口号
- `database`: 数据库名称
- `username`: 用户名
- `password`: 密码

### InfluxDB 特殊参数
- `url`: 完整的连接URL
- `token`: 访问令牌
- `org`: 组织名称

### MongoDB 特殊参数
- `url`: MongoDB 连接字符串

### SQLite 特殊参数
- `database`: 数据库文件路径

## 安全注意事项

⚠️ **重要**: 
- 生产环境中请勿在配置文件中明文存储密码
- 建议使用环境变量或密钥管理服务
- 定期更换数据库密码和访问令牌
- 限制数据库访问权限

## 使用示例

在应用程序中,可以通过指定配置节名称来使用特定的数据库配置:

```python
# 示例:使用时间序列数据库配置
db_config = load_config("timeseries")
```
