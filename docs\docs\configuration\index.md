# 配置指南

本节提供了IndustryTSLib的配置指南,帮助您正确设置和自定义库的行为。

## 配置文件格式

IndustryTSLib使用TOML格式的配置文件。TOML(Tom's Obvious, Minimal Language)是一种易于阅读的配置文件格式,设计目标是成为一个最小化的配置文件格式。

基本配置文件示例:

```toml
[database]
type = "sqlite"
path = "data/model_info.db"

[logging]
level = "INFO"
path = "logs"

[model]
type = "informer"
input_dim = 32
output_dim = 8
```

## 主要配置节点

### 数据库配置

```toml
[database]
# 数据库类型:sqlite, mssql, postgresql, influxdb
type = "sqlite"
# SQLite数据库路径
path = "data/model_info.db"
# 其他数据库连接信息
host = "localhost"
port = 1433
user = "username"
password = "password"
database = "database_name"
```

### 日志配置

```toml
[logging]
# 日志级别:DEBUG, INFO, WARNING, ERROR, CRITICAL
level = "INFO"
# 日志保存路径
path = "logs"
# 是否在控制台输出
console = true
# 日志文件最大大小(MB)
max_size = 10
# 保留的日志文件数量
backup_count = 5
```

### 模型配置

模型配置会根据不同的模型类型有所不同。以下是一些常见模型的配置示例:

#### Informer模型配置

```toml
[model]
type = "informer"
input_dim = 32
output_dim = 8
d_model = 512
n_heads = 8
e_layers = 3
d_layers = 2
d_ff = 2048
dropout = 0.1
activation = "gelu"
```

#### LSTM模型配置

```toml
[model]
type = "lstm"
input_dim = 32
hidden_dim = 64
num_layers = 2
output_dim = 8
dropout = 0.1
bidirectional = false
```

## 配置加载方式

IndustryTSLib提供了多种加载配置的方式:

### 从文件加载

```python
from industrytslib.utils.readconfig import load_config

# 从指定路径加载配置
config = load_config("path/to/config.toml")

# 使用配置
database_type = config["database"]["type"]
```

### 环境变量覆盖

您可以使用环境变量覆盖配置文件中的设置。环境变量名称应该使用双下划线分隔节点和键:

```bash
# 设置环境变量覆盖数据库类型
export INDUSTRYTSLIB__DATABASE__TYPE="postgresql"
```

### 程序内覆盖

```python
from industrytslib.utils.readconfig import load_config, update_config

# 加载配置
config = load_config("path/to/config.toml")

# 更新配置
update_config(config, "database.host", "new_host")
update_config(config, "model.dropout", 0.2)
```

## 配置验证

IndustryTSLib在加载配置时会自动验证配置的有效性。如果配置无效,将抛出相应的异常并提供详细的错误信息。

以下是一些常见的配置验证错误:

- 缺少必要的配置项
- 配置值类型不正确
- 配置值超出允许范围

## 配置最佳实践

1. **使用版本控制管理配置**:将配置文件纳入版本控制系统,但不要包含敏感信息
2. **使用环境变量存储敏感信息**:数据库密码等敏感信息应通过环境变量提供
3. **为不同环境创建不同配置**:开发、测试和生产环境应使用不同的配置文件
4. **注释配置文件**:使用`#`添加注释,说明各配置项的作用和可选值
5. **定期检查和更新配置**:随着应用的发展,定期审查和更新配置文件 