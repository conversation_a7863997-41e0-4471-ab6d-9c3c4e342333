# 数据加载器

本模块提供用于处理时间序列数据的PyTorch Dataset类,支持多种数据格式和加载策略。

## SequenceTimeSeriesDataset

基于Polars DataFrame的时间序列数据集类,用于序列到序列的预测任务。

### 初始化参数

- `project_name` (str): 项目名称,用于组织保存的归一化器
- `input_data` (pl.DataFrame): 输入时间序列数据
- `output_data` (pl.DataFrame): 输出时间序列数据
- `model_parameter` (dict): 模型配置参数
- `flag` (str): 数据集类型 ('train', 'test', 'val')
- `freq` (str): 时间频率 ('t', 'h', 'd', 'w', 'm', 'y')

### 关键参数说明

#### model_parameter 字典参数

- `seq_len` (int): 输入序列长度,默认120
- `pred_len` (int): 预测序列长度,默认60
- `label_len` (int): 标签序列长度,默认60
- `scaler_type` (str): 归一化类型 ('standard' 或 'minmax')

#### 数据要求

- input_data和output_data的DateTime列必须相同
- 两个DataFrame的行数必须相同
- DateTime列必须为第一列

### 数据处理流程

1. **数据分割**: 按8:1:1比例分割训练/验证/测试集
2. **时间特征提取**: 使用time_features函数提取时间戳特征
3. **数据归一化**: 支持StandardScaler和MinMaxScaler
4. **归一化器保存**: 自动保存到resource/scaler/{project_name}/目录

### 主要方法

#### `__getitem__(index: int)`

返回一个训练样本,包含:
- `seq_x`: 输入序列 [seq_len, input_dim]
- `seq_y`: 目标序列 [label_len + pred_len, output_dim]
- `seq_x_mark`: 输入序列时间特征
- `seq_y_mark`: 目标序列时间特征
- `seq_y_history`: 历史目标序列

#### `inverse_transform_x(x)` / `inverse_transform_y(y)`

对归一化后的数据进行反归一化。

## RandomSequenceTimeSeriesDataset

随机抽取数据集,通过随机选择起始点来获得更好的实验结果。

### 特点

- 继承自SequenceTimeSeriesDataset
- 随机打乱序列起始点
- 使用固定随机种子确保可重现性
- 适用于数据量较大的场景

### 数据处理策略

1. **计算有效起始点**: 根据序列长度计算所有可能的起始位置
2. **随机打乱**: 使用numpy随机打乱起始点顺序
3. **按比例分割**: 将打乱后的起始点按8:1:1分配给训练/验证/测试集
4. **序列提取**: 根据分配的起始点提取对应序列

## OperationSequenceTimeSeriesDataset

基于运行时间段划分数据集的时间序列数据加载器。

### 功能特点

- 自动检测生产运行时间段
- 基于时间段进行数据分割
- 避免非生产时段的数据干扰
- 更符合工业实际应用场景

### 应用场景

- 工业过程数据分析
- 设备运行状态预测
- 生产质量控制
- 故障检测与诊断

## OperationSequenceDatasetTime

带时间信息的运行时间段数据集,在OperationSequenceTimeSeriesDataset基础上增加了时间戳返回。

### 返回数据格式

除了标准的序列数据外,还返回:
- 输入序列对应的时间戳
- 目标序列对应的时间戳
- 便于时间序列分析和可视化

## 已弃用的类

### InputTimeSeriesDataset / OutputTimeSeriesDataset

**状态**: 已弃用 (deprecated)

**替代方案**: 使用SequenceTimeSeriesDataset

**弃用原因**: 
- 功能重复
- 接口不统一
- 维护成本高

### TransformerTimeSeriesDataset

**状态**: 已弃用 (deprecated)

**替代方案**: 使用SequenceTimeSeriesDataset

## 使用示例

```python
# 基础序列数据集
dataset = SequenceTimeSeriesDataset(
    project_name="industrial_prediction",
    input_data=input_df,
    output_data=output_df,
    model_parameter={
        "seq_len": 120,
        "pred_len": 60,
        "label_len": 60,
        "scaler_type": "minmax"
    },
    flag="train",
    freq="t"
)

# 随机序列数据集
random_dataset = RandomSequenceTimeSeriesDataset(
    project_name="industrial_prediction",
    input_data=input_df,
    output_data=output_df,
    model_parameter=model_params,
    flag="train",
    freq="t"
)

# 运行时间段数据集
operation_dataset = OperationSequenceTimeSeriesDataset(
    project_name="industrial_prediction",
    input_data=input_df,
    output_data=output_df,
    model_parameter=model_params,
    flag="train",
    freq="t"
)

# 创建DataLoader
from torch.utils.data import DataLoader

train_loader = DataLoader(
    dataset=dataset,
    batch_size=32,
    shuffle=True,
    num_workers=4
)

# 获取一个批次的数据
for batch_x, batch_y, batch_x_mark, batch_y_mark, batch_y_history in train_loader:
    # batch_x: [batch_size, seq_len, input_dim]
    # batch_y: [batch_size, label_len + pred_len, output_dim]
    # batch_x_mark: [batch_size, seq_len, time_feature_dim]
    # batch_y_mark: [batch_size, label_len + pred_len, time_feature_dim]
    # batch_y_history: [batch_size, seq_len - label_len, output_dim]
    break
```

---

**相关文档:**

*   [数据预处理](./data_preprocess.md)
*   [Polars操作](./polars_operation.md)
*   [时间序列处理](./time_series_processing.md)