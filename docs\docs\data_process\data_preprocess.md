# 数据预处理

## DataFramePreprocess (基础类)

数据预处理基础类,提供通用的数据预处理功能。所有具体的数据预处理类都继承自此基础类。

### 初始化参数

- `input_data` (pl.DataFrame): 输入数据,通常为时间序列数据
- `output_data` (pl.DataFrame): 输出数据,通常为目标变量数据
- `model_parameter` (dict): 模型参数配置字典
- `project_name` (str): 项目名称,用于日志记录和数据保存
- `config_path` (str | Path): 配置文件路径,默认为"config/data_preprocess_config.toml"

### 主要方法

#### missed_data_handle()
处理缺失数据的方法,支持多种插值策略。

#### outlier_handle(df: pl.DataFrame) -> pl.DataFrame
异常值处理方法,基于配置文件中的变量范围过滤异常值。

#### run() -> Tuple[pl.DataFrame, pl.DataFrame]
执行完整的数据预处理流程,返回处理后的输入和输出数据。

## ClassicDataFrameProcess

经典时间序列数据预处理类,适用于传统的时间序列预测任务。

### 预处理流程

1. **缺失值处理** - 使用插值方法填补缺失数据
2. **异常值处理** - 基于变量范围过滤异常值
3. **重采样** - 统一数据采样频率
4. **重复值去除** - 移除连续重复的数据点
5. **数据平滑** - 应用滤波算法减少噪声
6. **数据匹配** - 对齐输入输出数据的时间戳
7. **数据分析** - 生成数据质量报告和可视化图表
8. **数据保存** - 保存预处理后的数据

## SequenceDataFrameProcess

序列数据预处理类,专门用于序列到序列的预测任务。

### 特点

- 继承自 `DataFramePreprocess`
- 针对序列预测任务优化
- 支持变长序列处理
- 包含序列特定的数据验证

## MLDataFrameProcess

机器学习数据预处理类,适用于软测量等机器学习任务。

### 关键参数

- `seq_len`: 用于预测的输入序列长度
- `pred_len`: 预测输出序列长度
- `match_type`: 数据匹配的时间类型
- `x_length`: 软测量模型的输入维度
- `y_length`: 软测量模型的输出维度

## JointDataFrameProcess

多任务联合训练数据预处理类,支持同时处理多个相关的预测任务。

### 应用场景

- 多变量时间序列预测
- 联合优化多个目标
- 共享特征表示学习

---

**See Also:**

*   [Data Loaders](./data_loader.md)
*   [Polars Operations](./polars_operation.md)
*   [Time Series Processing](./time_series_processing.md)