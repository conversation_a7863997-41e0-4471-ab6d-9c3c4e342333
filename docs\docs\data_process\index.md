# 数据处理

本节介绍 `industrytslib` 中的数据处理模块,提供处理、预处理和加载时间序列数据的综合工具。

## 模块概述

数据处理是工业时间序列分析的核心环节,本模块提供了从原始数据到模型输入的完整数据处理流水线。主要包括数据加载、预处理、操作函数和高级时间序列分析等功能。

## 子模块

### [数据加载器](./data_loader.md)
PyTorch Dataset类,用于高效的时间序列数据加载和批处理,支持多种数据格式和预处理流水线。

**主要功能**:
- 序列到序列数据集加载
- 随机序列采样策略
- 运行时间段数据分割
- 自动归一化和特征提取
- 支持多种时间频率

### [数据预处理](./data_preprocess.md)
全面的数据预处理工具,包括缺失值处理、异常值检测、归一化和数据质量评估。

**主要功能**:
- 多种数据预处理策略
- 缺失值智能填补
- 异常值检测与处理
- 数据平滑和滤波
- 数据质量分析

### [Polars操作](./polars_operation.md)
基于Polars DataFrame构建的高性能数据操作函数,针对时间序列操作和工业数据处理进行优化。

**主要功能**:
- 时间处理函数
- 数据平滑和滤波
- 数据质量检查
- 时间序列分析
- 高性能数据变换

### [时间序列处理](./time_series_processing.md)
高级时间序列分析工具,包括信号分解、平稳性检验和频域分析。

**主要功能**:
- EMD/VMD信号分解
- 小波变换分析
- 平稳性检验
- 自相关分析
- 时频域联合分析

## 数据处理流程

典型的工业时间序列数据处理流程如下:

```
原始数据 → 数据加载 → 数据预处理 → 特征工程 → 模型输入
    ↓         ↓         ↓         ↓         ↓
  CSV/DB   DataFrame  清洗/滤波   时间特征   Dataset
```

### 1. 数据加载阶段
- 从各种数据源加载时间序列数据
- 转换为标准的Polars DataFrame格式
- 初步数据验证和格式检查

### 2. 数据预处理阶段
- 缺失值处理和异常值检测
- 数据平滑和噪声滤除
- 时间对齐和重采样
- 数据质量评估

### 3. 特征工程阶段
- 时间特征提取
- 统计特征计算
- 信号分解和变换
- 特征选择和降维

### 4. 数据集构建阶段
- 序列切分和窗口生成
- 训练/验证/测试集划分
- 数据归一化和标准化
- PyTorch Dataset封装

## 最佳实践

### 数据质量保证
1. **数据完整性检查**: 确保时间序列连续性和数据完整性
2. **异常值处理**: 使用统计方法和领域知识识别异常值
3. **数据平滑**: 根据信号特性选择合适的滤波方法
4. **时间对齐**: 确保多变量时间序列的时间戳一致性

### 性能优化
1. **使用Polars**: 利用Polars的高性能特性处理大规模数据
2. **批处理**: 合理设置批处理大小平衡内存和计算效率
3. **并行处理**: 利用多核CPU加速数据预处理
4. **内存管理**: 及时释放不需要的中间数据

### 可重现性
1. **固定随机种子**: 确保数据分割和采样的可重现性
2. **参数记录**: 保存所有预处理参数和配置
3. **版本控制**: 对数据处理脚本进行版本管理
4. **日志记录**: 详细记录数据处理过程和结果

## 扩展性设计

本模块采用模块化设计,支持灵活扩展:

- **插件式架构**: 可以轻松添加新的预处理方法
- **配置驱动**: 通过配置文件控制处理流程
- **接口标准化**: 统一的数据接口便于集成
- **类型安全**: 使用类型提示确保代码健壮性
