# Polars Data Operations

本模块提供了使用Polars库进行数据操作的工具函数,专注于工业时间序列数据处理。

## 时间处理函数

### `convert_datetime_format`

```python
convert_datetime_format(df: pl.DataFrame, column_name: str = "DateTime") -> pl.DataFrame
```

将DataFrame中的时间列转换为标准的datetime格式,支持多种常见的日期时间格式。

**参数:**
- `df` (`pl.DataFrame`): 包含时间列的DataFrame
- `column_name` (`str`, 可选): 时间列的名称,默认为"DateTime"

**返回:**
- `pl.DataFrame`: 转换后的DataFrame,时间列为datetime类型

### `convert_datetime_format_alter`

```python
convert_datetime_format_alter(df: pl.DataFrame, column_name: str = "DateTime") -> pl.DataFrame
```

将DataFrame中的时间列转换为标准的datetime格式的替代实现,支持更多日期时间格式。

**参数:**
- `df` (`pl.DataFrame`): 包含时间列的DataFrame
- `column_name` (`str`, 可选): 时间列的名称,默认为"DateTime"

**返回:**
- `pl.DataFrame`: 转换后的DataFrame,时间列为datetime类型

### `calculate_minute_average`

```python
calculate_minute_average(dataframe_cal: pl.DataFrame) -> pl.DataFrame
```

计算每分钟的平均值。

**参数:**
- `dataframe_cal` (`pl.DataFrame`): 输入DataFrame,默认时间列为DateTime

**返回:**
- `pl.DataFrame`: 按分钟聚合的DataFrame

**示例:**
```python
dataframe_cal = pl.DataFrame({
    'DateTime': ['2024-01-01 00:00:00', '2024-01-01 00:00:01', '2024-01-01 00:00:02'],
    'Value': [1, 2, 3]
})
calculate_minute_average(dataframe_cal)
```

### `data_out_extention`

```python
data_out_extention(df: pl.DataFrame, duration_str: str) -> pl.DataFrame
```

将包含时间戳和值的DataFrame按指定的时长扩展到分钟级别。

**参数:**
- `df` (`pl.DataFrame`): 输入数据,必须包含时间列和值列
- `duration_str` (`str`): 扩展的时长,格式为 "N hour" 或 "N hours"

**返回:**
- `pl.DataFrame`: 扩展后的数据,时间列重命名为'DateTime'

## 数据平滑和滤波函数

### `smooth_filter`

```python
smooth_filter(dataframe_process: pl.DataFrame, window_size: int = 10) -> pl.DataFrame
```

使用简单移动平均(SMA)对数据进行平滑处理。

**参数:**
- `dataframe_process` (`pl.DataFrame`): 输入数据
- `window_size` (`int`, 可选): 滑动窗口的大小,默认为10

**返回:**
- `pl.DataFrame`: 平滑后的数据

### `weighted_moving_average`

```python
weighted_moving_average(dataframe_process: pl.DataFrame, window_size: int = 5, weights: Optional[Sequence[float]] = None) -> pl.DataFrame
```

使用加权移动平均(WMA)对数据进行平滑处理。

**参数:**
- `dataframe_process` (`pl.DataFrame`): 输入数据
- `window_size` (`int`, 可选): 滑动窗口的大小,默认为5
- `weights` (`Sequence[float]`, 可选): 自定义权重序列,默认为None使用线性权重

**返回:**
- `pl.DataFrame`: 加权移动平均后的数据

### `exponential_moving_average`

```python
exponential_moving_average(dataframe_process: pl.DataFrame, alpha: float = 0.2) -> pl.DataFrame
```

使用指数移动平均(EMA)对数据进行平滑处理。

**参数:**
- `dataframe_process` (`pl.DataFrame`): 输入数据
- `alpha` (`float`, 可选): 平滑因子(0 < alpha < 1),默认为0.2

**返回:**
- `pl.DataFrame`: 指数移动平均后的数据

### `savitzky_golay_filter`

```python
savitzky_golay_filter(dataframe_process: pl.DataFrame, window_length: int = 5, polyorder: int = 3) -> pl.DataFrame
```

使用Savitzky-Golay滤波器对数据进行降噪。

**参数:**
- `dataframe_process` (`pl.DataFrame`): 输入数据
- `window_length` (`int`, 可选): 窗口长度,默认为5
- `polyorder` (`int`, 可选): 多项式阶数,默认为3

**返回:**
- `pl.DataFrame`: 滤波后的数据

### `wavelet_denoising`

```python
wavelet_denoising(data: pl.DataFrame, wavelet: str = 'db4', level: int = 1) -> pl.DataFrame
```

使用小波变换对数据进行降噪。

**参数:**
- `data` (`pl.DataFrame`): 输入数据
- `wavelet` (`str`, 可选): 小波类型,默认为'db4'
- `level` (`int`, 可选): 分解层数,默认为1

**返回:**
- `pl.DataFrame`: 降噪后的数据

## 数据质量和处理函数

### `check_device_status`

```python
check_device_status(dataframe_check: pl.DataFrame, column_name: Union[str, List[str], None], duration: int = 5) -> bool
```

根据给定的时间长度判断DataFrame中指定列的最新数据的均值是否大于阈值。

**参数:**
- `dataframe_check` (`pl.DataFrame`): 输入数据
- `column_name` (`Union[str, List[str], None]`): 要检查的列名
- `duration` (`int`, 可选): 用于计算均值的时间长度,以分钟为单位,默认为5

**返回:**
- `bool`: 如果均值大于10则返回True,否则返回False

### `filter_feed_columns`

```python
filter_feed_columns(dataframe_filter: pl.DataFrame) -> pl.DataFrame
```

从DataFrame中筛选出包含"喂料量"或"给料机"的列,并过滤出该列值大于等于10的行。

**参数:**
- `dataframe_filter` (`pl.DataFrame`): 需要筛选的DataFrame

**返回:**
- `pl.DataFrame`: 筛选后的DataFrame

### `check_data_quality`

```python
check_data_quality(data: pl.DataFrame, date_column: str = None) -> bool
```

检查数据质量,评估DataFrame中非日期列的数据变化程度。

**参数:**
- `data` (`pl.DataFrame`): 输入的DataFrame
- `date_column` (`str`, 可选): 日期列名,默认为None

**返回:**
- `bool`: 数据质量是否合格

### `check_data_quality_random`

```python
check_data_quality_random(data: pl.DataFrame, date_column: str = "DateTime") -> bool
```

通过随机选择列检查数据质量,适用于有一些列在短时间内保持不变的场景。

**参数:**
- `data` (`pl.DataFrame`): 输入的DataFrame
- `date_column` (`str`, 可选): 时间列的名称,默认为"DateTime"

**返回:**
- `bool`: 数据质量是否合格

### `duplicate_filter`

```python
duplicate_filter(df: pl.DataFrame, n: int = 60, subset: Optional[list[str]] = None) -> pl.DataFrame
```

移除指定列中连续重复超过n次的行。

**参数:**
- `df` (`pl.DataFrame`): 输入DataFrame
- `n` (`int`, 可选): 允许的最大连续重复次数,默认为60
- `subset` (`list[str]`, 可选): 要检查的列名列表,默认为None(使用所有列)

**返回:**
- `pl.DataFrame`: 过滤后的DataFrame

## 数据处理和转换函数

### `data_joint_decision`

```python
data_joint_decision(history_data: pl.DataFrame, generator_data: pl.DataFrame, decision_length: int) -> pl.DataFrame
```

根据决策长度拼接历史数据和决策数据。

**参数:**
- `history_data` (`pl.DataFrame`): 历史数据
- `generator_data` (`pl.DataFrame`): 新生成的随机数据
- `decision_length` (`int`): 决策长度

**返回:**
- `pl.DataFrame`: 拼接后的数据

### `vstack_with_fill`

```python
vstack_with_fill(df1: pl.DataFrame, df2: pl.DataFrame, default_num: int = 0, default_str: str = "0") -> pl.DataFrame
```

垂直拼接两个DataFrame,自动处理缺失列并填充默认值。

**参数:**
- `df1` (`pl.DataFrame`): 第一个DataFrame
- `df2` (`pl.DataFrame`): 第二个DataFrame
- `default_num` (`int`, 可选): 数值类型列的默认值,默认为0
- `default_str` (`str`, 可选): 字符串类型列的默认值,默认为"0"

**返回:**
- `pl.DataFrame`: 拼接后的DataFrame

### `add_datetime_column`

```python
add_datetime_column(df: pl.DataFrame, datetime_col_name: str = "DateTime", mode: str = "backward") -> pl.DataFrame
```

在Polars DataFrame的第一列位置添加一个DateTime列,时间从当前开始按指定模式变化。

**参数:**
- `df` (`pl.DataFrame`): 输入的DataFrame
- `datetime_col_name` (`str`, 可选): 新添加的时间列的列名,默认为'DateTime'
- `mode` (`str`, 可选): 时间序列模式,'backward'或'forward',默认为'backward'

**返回:**
- `pl.DataFrame`: 添加了时间列的新DataFrame

### `transformation_predict_data`

```python
transform_predict_data(df: pl.DataFrame) -> pl.DataFrame
```

将transformer模型预测输出转换为输入软测量模型的数据,处理包含多组true/predict列。

**参数:**
- `df` (`pl.DataFrame`): 输入的DataFrame,包含DateTime和多组*_true/*_predict列

**返回:**
- `pl.DataFrame`: 处理后的DataFrame

### `optimization_data_process`

```python
optimization_data_process(data_in: pl.DataFrame, time_duration: int) -> pl.DataFrame
```

优化模块中处理历史数据。

**参数:**
- `data_in` (`pl.DataFrame`): 需要转换的DataFrame
- `time_duration` (`int`): 保留的时间长度,单位为分钟

**返回:**
- `pl.DataFrame`: 处理后的DataFrame

## 时间序列分析函数

### `check_time_difference`

```python
check_time_difference(csv_path: str) -> Tuple[bool, datetime]
```

检测csv文件中历史数据的最新时间和当前时间的差值。

**参数:**
- `csv_path` (`str`): 历史数据的路径

**返回:**
- `Tuple[bool, datetime]`: 时间差是否大于7天的布尔值和CSV文件的最新时间

### `detect_production_periods`

```python
detect_production_periods(df: pl.DataFrame, max_gap: timedelta = timedelta(hours=1)) -> pl.DataFrame
```

监测时间间隔,划分生产周期。

**参数:**
- `df` (`pl.DataFrame`): 包含DateTime列的DataFrame
- `max_gap` (`timedelta`, 可选): 判定为新生产周期的最大时间间隔,默认为1小时

**返回:**
- `pl.DataFrame`: 带有production_period列的DataFrame,表示不同的生产周期

### `data_smoothing`

```python
data_smoothing(input_data: pl.DataFrame, output_data: pl.DataFrame, model_parameter: dict) -> Tuple[pl.DataFrame, pl.DataFrame]
```

根据模型参数对输入和输出数据进行平滑处理。

**参数:**
- `input_data` (`pl.DataFrame`): 输入的DataFrame
- `output_data` (`pl.DataFrame`): 输出的DataFrame
- `model_parameter` (`dict`): 模型参数,包含output_flag、smooth_flag、smooth_window_size

**返回:**
- `Tuple[pl.DataFrame, pl.DataFrame]`: 处理后的输入和输出数据

---

**相关资源:**

- [数据加载器](./data_loader.md)
- [数据预处理](./data_preprocess.md)
- [时间序列处理](./time_series_processing.md) 
