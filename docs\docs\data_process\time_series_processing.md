# 时间序列处理

本模块提供高级时间序列处理功能,包括信号分解和平稳性分析。

## TimeSeriesDecomposition

时间序列分解类,支持多种分解方法对时间序列信号进行分析。

### 初始化参数

- `signal` (np.ndarray): 输入的时间序列信号
- `sampling_rate` (float): 信号采样率,默认为1.0 Hz
- `save_path` (Optional[Union[str, Path]]): 保存分解结果的目录路径

### 支持的分解方法

#### EMD (经验模态分解)

**方法**: `apply_emd() -> Tuple[np.ndarray, List[np.ndarray]]`

- 将信号分解为本征模态函数(IMFs)
- 自适应分解,无需预设基函数
- 适用于非线性、非平稳信号
- 返回时间点数组和IMF列表

#### VMD (变分模态分解)

**方法**: `apply_vmd(alpha=2000, tau=0.0, K=3, DC=False, init=1, tol=1e-7) -> Tuple[np.ndarray, np.ndarray, np.ndarray]`

**参数说明**:
- `alpha`: 带宽约束参数,控制模态带宽
- `tau`: 噪声容忍参数
- `K`: 提取的模态数量
- `DC`: 是否包含直流分量
- `init`: 初始化方法(1表示均匀分布)
- `tol`: 收敛容差

**返回值**:
- 分解的模态
- 频域模态
- 模态中心频率

#### 小波变换

**方法**: `apply_wavelet_transform(wavelet='db4', levels=None) -> Tuple[np.ndarray, List[np.ndarray]]`

- 多分辨率分析
- 支持多种小波基函数
- 时频域联合分析
- 返回近似系数和细节系数

### 可视化功能

- `plot_decomposition_results()`: 绘制分解结果
- `save_decomposition_plots()`: 保存分解图表
- 支持多种图表格式输出

## StationarityAnalyzer

多变量时间序列平稳性分析器,提供全面的平稳性检验和分析功能。

### 初始化参数

- `data` (pl.DataFrame): 输入的时间序列数据(Polars DataFrame格式)
- `save_path` (Optional[Union[str, Path]]): 保存分析结果的目录路径

### 分析方法

#### ADF检验 (Augmented Dickey-Fuller Test)

**方法**: `check_stationarity(series: np.ndarray, name: str) -> Dict[str, Any]`

检验时间序列是否存在单位根,判断平稳性。

**返回结果**:
- `adf_statistic`: ADF统计量
- `p_value`: p值
- `critical_values`: 临界值字典
- `is_stationary`: 是否平稳(p值≤0.05为平稳)

#### 时间序列可视化

**方法**: `plot_time_series()`

- 绘制原始时间序列图
- 识别趋势和季节性模式
- 检测异常值和结构突变

#### 自相关分析

**方法**: `plot_acf_pacf(series: np.ndarray, name: str)`

- **ACF (自相关函数)**: 测量序列与其滞后值的线性相关性
- **PACF (偏自相关函数)**: 消除中间滞后影响后的相关性
- 用于识别ARIMA模型的阶数

#### 差分处理

- 自动对非平稳序列进行一阶差分
- 重新检验差分后序列的平稳性
- 比较原序列和差分序列的统计特性

### 完整分析流程

**方法**: `analyze() -> Dict[str, List[Dict[str, Any]]]`

执行完整的平稳性分析,包括:

1. **原始序列分析**
   - 对所有数值列进行ADF检验
   - 生成时间序列图
   - 绘制ACF/PACF图

2. **差分序列分析**
   - 对原始序列进行一阶差分
   - 重新进行平稳性检验
   - 比较差分前后的统计特性

**返回格式**:
```python
{
    'original': [  # 原始序列分析结果
        {
            'name': '变量名',
            'adf_statistic': ADF统计量,
            'p_value': p值,
            'critical_values': 临界值,
            'is_stationary': 是否平稳
        },
        ...
    ],
    'differenced': [  # 差分序列分析结果
        ...
    ]
}
```

### 使用示例

```python
# 时间序列分解
decomposer = TimeSeriesDecomposition(
    signal=data_array, 
    sampling_rate=1.0,
    save_path="decomposition_results"
)

# EMD分解
time, imfs = decomposer.apply_emd()

# VMD分解
modes, freq_modes, center_freqs = decomposer.apply_vmd(K=5, alpha=2000)

# 平稳性分析
analyzer = StationarityAnalyzer(
    data=df, 
    save_path="stationarity_analysis"
)

# 执行完整分析
results = analyzer.analyze()

# 查看结果
for series_type, analyses in results.items():
    print(f"\n{series_type.title()} 序列结果:")
    for analysis in analyses:
        print(f"变量: {analysis['name']}")
        print(f"ADF统计量: {analysis['adf_statistic']:.4f}")
        print(f"P值: {analysis['p_value']:.4f}")
        print(f"是否平稳: {analysis['is_stationary']}")
```

---

**See Also:**

*   [Data Loaders](./data_loader.md)
*   [Data Preprocessing](./data_preprocess.md)
*   [Polars Operations](./polars_operation.md)