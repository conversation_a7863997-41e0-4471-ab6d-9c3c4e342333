本文主要介绍协同优化算法的基本部署流程。
# 1.数据库

所有的安装文件都在deploy文件夹中相应的系统名文件夹下。

## SQL Server

参考专家系统项目整体实施步骤。

## MongoDB

双击deploy目录下的mongo安装exe,一路下一步即可。

## Influxdb

暂时不使用。

# 2.Python环境安装与配置

推荐使用Anaconda配置Python环境。

如果系统硬件有NVIDIA显卡,则需要先安装显卡驱动、CUDA以及CUDNN。

## Windows

#### 安装显卡驱动(Optional)

在deploy文件夹中Windows文件夹下找到NVIDIA的三个驱动,按照图片中的顺序依次默认安装即可。

![image-20240614150204308](deploy.assets/image-20240614150204308.png)

### 安装Anaconda

![image-20240614150311697](deploy.assets/image-20240614150311697.png)

1. 在deploy文件夹中Windows文件夹下找到anaconda.exe,双击运行一路顺序安装即可。

2. 安装完成后配置环境变量:

需要将下面的路径加入path中:

```txt
C:\anaconda3
C:\anaconda3\Scripts
C:\anaconda3\Library\bin
```

请根据实际安装路径调整环境变量。

3. 安装所需要的python库。

在deploy文件夹下执行下面的命令即可(前提是有网)**[推荐]**:

```shell
conda create -n xtyh python=3.11
conda activate xtyh
pip install -r requirements.txt
```

没有网的情况需要将deploy文件夹下的xtyh.tar.gz复制到Anaconda文件夹下的env目录中,然后解压即可。

## Linux

先安装显卡驱动。

![image-20240614150434570](deploy.assets/image-20240614150434570.png)

然后将Anaconda.sh复制过去,然后`sh *.sh`即可安装,然后一直回车,需要输入的地方输入`yes`即可。

剩余步骤同windows。

# 3.Docker

暂时不使用。

# 4.协同优化系统运行与配置

环境安装完成后,在deploy目录下执行下面的命令即可启动协同优化后端:

```shell
conda activate xtyh
python app.py
```

具体使用说明见协同优化系统使用说明。

