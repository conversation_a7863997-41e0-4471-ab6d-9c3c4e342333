工业AI智能大脑python后端Linux部署。以CentOS 7 为例。

使用XTerminal作为SSH终端,所有的文件统一上传到root或者home的Downloads文件夹下。

![image-20240625163459262](deploy_linux.assets/image-20240625163459262.png)

# 0 驱动安装

## 显卡驱动

重庆合川的显卡为T4.在网上找到T4支持的显卡驱动安装包,上传到服务器中。

首先给run文件赋予权限:

```shell
sudo chmod +x NVIDIA-Linux-x86_64-550.78.run
```

然后执行run文件:

```shell
./NVIDIA-Linux-x86_64-550.78.run 
```

![image-20240626151250068](deploy_linux.assets/image-20240626151250068.png)

然后选择Continue installation,回车,开始安装。中途有弹窗按回车默认即可:
![image-20240626151729860](deploy_linux.assets/image-20240626151729860.png)

安装完成,重启服务器:

```shell
sudo reboot
```

重启完成后,输入`nvidia-smi`,出现下图证明安装成功。

![image-20240626160241574](deploy_linux.assets/image-20240626160241574.png)



### CUDA安装

首先给cuda安装程序赋予权限:

```shell
sudo chmod +x cuda_12.4.1_550.54.15_linux.run 
```

![image-20240626150309369](deploy_linux.assets/image-20240626150309369.png)

执行sh安装命令:

```shell
sh cuda_12.4.1_550.54.15_linux.run
```

![image-20240626150415368](deploy_linux.assets/image-20240626150415368.png)

执行命令后等一会,然后输入accept:

![image-20240626150748641](deploy_linux.assets/image-20240626150748641.png)

然后使用方向键取消Driver的安装(我们已经安装过了),然后移动到install,回车:

安装成功如下图所示:

![image-20240626160743370](deploy_linux.assets/image-20240626160743370.png)

然后将cuda加入环境变量:

```txt
export PATH=/usr/local/cuda/bin${PATH:+:${PATH}}
export LD_LIBRARY_PATH=/usr/local/cuda-12.4/lib64\
                    ${LD_LIBRARY_PATH:+:${LD_LIBRARY_PATH}}
```

![image-20240626160949704](deploy_linux.assets/image-20240626160949704.png)

保存,输入命令`nvcc -V`验证是否安装成功(注意:此时要重启终端才能生效):

![image-20240626161056103](deploy_linux.assets/image-20240626161056103.png)

### CUDNN 安装

首先解压压缩文件:

```shell
tar -xvf cudnn-linux-x86_64-9.1.0.70_cuda12-archive.tar.xz 
```

然后执行`copy`命令将cudnn库拷贝到上面安装的cuda目录中:

```shell
sudo cp lib/* /usr/local/cuda-12.4/lib64/
sudo cp include/* /usr/local/cuda-12.4/include/
sudo chmod a+r /usr/local/cuda-12.4/lib64/*
sudo chmod a+r /usr/local/cuda-12.4/include/*
```

验证cudnn安装:

```shell
cat /usr/local/cuda-12.4/include/cudnn_version.h | grep CUDNN_MAJOR -A 2
```

![image-20240626162234624](deploy_linux.assets/image-20240626162234624.png)

出现上面的输出证明CUDNN安装完成。

# Anaconda

## 安装

在Downloads目录下,输入:

```sh
sh Anaconda3-2024.02-1-Linux-x86_64.sh 
```

![image-20240625170539532](deploy_linux.assets/image-20240625170539532.png)

然后一直按回车,同意协议,知道出现输入yes为止:

![image-20240625170714334](deploy_linux.assets/image-20240625170714334.png)

输入yes,回车:

![image-20240625170751440](deploy_linux.assets/image-20240625170751440.png)

回车确认默认安装位置,等待安装完成:

![image-20240625171138559](deploy_linux.assets/image-20240625171138559.png)

再输入yes初始化conda环境:

![image-20240625171221256](deploy_linux.assets/image-20240625171221256.png)

**完成Anaconda安装！！！**

## 创建环境并安装库

### 方法一:创建环境然后安装库
#### 在线
1. 在线创建虚拟环境:
```shell
conda create -n industryai python=3.12
```
2. 然后安装提供的whl文件:
```shell
pip install industrytslib-*.whl
```
> Notes:industrytslib这个库是不断更新的,在线安装会把dependence也都会安装上
3. 尝试安装其他依赖库
包括:
- Casual-conv1d
- Mamba-ssm

#### 离线
执行下面的命令离线创建虚拟环境:

```shell
conda create -n industryai python=3.12 --offline
```

![image-20240626143630721](deploy_linux.assets/image-20240626143630721.png)

然后激活环境,开始安装程序需要的库:

```shell
conda activate industryai
```

![image-20240626143731816](deploy_linux.assets/image-20240626143731816.png)

进入Downloads目录下,解压pylib.zip:

```shell
unzip pylib.zip -d pylib
```

![image-20240626144218571](deploy_linux.assets/image-20240626144218571.png)

然后进入pylib目录下,执行下面的命令,将所有的包安装上:

```shell
pip install --no-index --find-links=./pkgs_311 -r requirements.txt
```

这种情况容易报错,不好解决,采用方法二:

### 方法二:将整个库解压到相应的目录中

1. **复制压缩文件 output. tar. gz 到新的电脑环境**
2. 进入到 conda 的安装目录下:`/anaconda/envs/`,在该名目录下创建文件夹,复制 output. tar. gz 到文件夹中。
3. 解压 conda 环境:`sudo tar -xzvf output. tar. gz -C env/`
4. 使用 conda env list 查看虚拟环境
5. conda activate 激活环境

![image-20240626191305198](deploy_linux.assets/image-20240626191305198.png)

### 验证安装

如上图所示依次输入下面的命令,得到上面结果说明安装成功。

```shell
python
import torch
torch.cuda.is_available()
# True
```

## 其他部件安装
unixodbc和ODBC for SQL Server二选一安装即可。

### unixodbc **[!important]**

如果可以联网的话输入下面的命令即可:

```shell
sudo apt install unixodbc
```

离线参考下面的步骤:

### ODBC for SQL Server **[!important]**

#### 离线安装

1. 下载odbc for sql server的安装包,地址:[SQL Server 文件導覽提示 - SQL Server | Microsoft Learn](https://learn.microsoft.com/zh-tw/sql/sql-server/sql-docs-navigation-guide?view=sql-server-ver16#applies-to),上传到服务器中。
2. 使用dpkg -i 安装:
```shell
sudo dpkg -i msodbc*.deb
```


# 1 应用模式部署

在安装完显卡驱动和python环境的条件下,cd进入算法服务器目录下,执行如下命令即可:

```shell
conda activate industryai
python main.py
```

# 2 Docker模式部署

## 安装Docker 

```shell

```


## Docker运行主程序
```shell
docker run ...
```
