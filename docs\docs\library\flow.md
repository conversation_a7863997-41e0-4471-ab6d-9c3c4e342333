<!-- industrytslib 工作流mermaid图 -->

```mermaid
graph TB
    subgraph IndustryTSLib
        Core["Core Functionality"]
        Utils["Utilities"]
        AsyncCore["Async Core"]
        Version["Version Info"]
    end

    subgraph Core["Core Functionality"]
        Training["Training Pipeline"]
        Predictor["Predictor Pipeline"] 
        Decision["Decision Pipeline"]
        Background["Background Tasks"]
        Base["Base Classes"]
    end

    subgraph AsyncCore["Async Core"]
        AsyncTrainers["Async Model Trainers"]
        AsyncPredictors["Async Realtime Predictors"]
        AsyncDecision["Async Decision Agents"]
        AsyncBackground["Async Background Tasks"]
        AsyncBase["Async Base Classes"]
    end

    subgraph Utils["Utilities"]
        LogUtils["Logging Utilities"]
        ConfigUtils["Configuration Utils"]
        DBUtils["Database Utils"]
    end

    subgraph Training["Training Pipeline"]
        ClassicTrainer["Classic Model Trainer"]
        SequenceTrainer["Sequence Model Trainer"]
        JointTrainer["Joint Model Trainer"]
    end

    subgraph Predictor["Predictor Pipeline"]
        ClassicPredictor["Classic Model Predictor"]
        SequencePredictor["Sequence Model Predictor"]
    end

    subgraph Decision["Decision Pipeline"]
        DecisionAgent["Decision Making Agent"]
        OptimizationModels["Optimization Models"]
    end

    subgraph Background["Background Tasks"]
        DecisionHistory["Decision History"]
        PredictionTransaction["Prediction Transaction"]
        PredictionSettle["Prediction Settle"]
    end

    Core --> Training
    Core --> Predictor
    Core --> Decision
    Core --> Background
    Core --> Base

    AsyncCore --> AsyncTrainers
    AsyncCore --> AsyncPredictors
    AsyncCore --> AsyncDecision
    AsyncCore --> AsyncBackground
    AsyncCore --> AsyncBase

    Training --> ClassicTrainer
    Training --> SequenceTrainer
    Training --> JointTrainer

    Predictor --> ClassicPredictor
    Predictor --> SequencePredictor

    Decision --> DecisionAgent
    Decision --> OptimizationModels

    Background --> DecisionHistory
    Background --> PredictionTransaction
    Background --> PredictionSettle

    Utils --> LogUtils
    Utils --> ConfigUtils
    Utils --> DBUtils

    classDef default fill:#f9f,stroke:#333,stroke-width:2px;
    classDef core fill:#bbf,stroke:#333,stroke-width:2px;
    classDef async fill:#bfb,stroke:#333,stroke-width:2px;
    classDef utils fill:#fbb,stroke:#333,stroke-width:2px;

    class Core,Training,Predictor,Decision,Background,Base core;
    class AsyncCore,AsyncTrainers,AsyncPredictors,AsyncDecision,AsyncBackground,AsyncBase async;
    class Utils,LogUtils,ConfigUtils,DBUtils utils;
```