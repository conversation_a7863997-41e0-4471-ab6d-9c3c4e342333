# LLM API参考文档

本文档提供industrytslib LLM模块的完整API参考,包括Ollama和SiliconFlow两种LLM服务的API。

## 📋 目录

- [Ollama API](#ollama-api)
  - [OllamaClient](#ollamaclient)
  - [OllamaConfig](#ollamaconfig)
  - [OllamaRequest](#ollamarequest)
- [SiliconFlow API](#siliconflow-api)
  - [SiliconFlowClient](#siliconflowclient)
  - [SiliconFlowConfig](#siliconflowconfig)
  - [SiliconFlowRequest](#siliconflowrequest)
  - [SiliconFlowMessage](#siliconflowmessage)
  - [SiliconFlowResponse](#siliconflowresponse)
- [便捷函数](#便捷函数)
  - [Ollama便捷函数](#ollama便捷函数)
  - [SiliconFlow便捷函数](#siliconflow便捷函数)
- [异常类](#异常类)
- [类型定义](#类型定义)

## <a name="ollama-api"></a>Ollama API

### <a name="ollamaclient"></a>OllamaClient

Ollama API的主要客户端类,提供同步和异步的文本生成功能。

```python
class OllamaClient:
    """Ollama API客户端"""
    
    def __init__(self, config: Optional[OllamaConfig] = None)
```

#### 参数

- `config` (Optional[OllamaConfig]): 客户端配置,如果为None则使用默认配置

#### 方法

##### generate

```python
def generate(self, request: OllamaRequest) -> Union[str, Generator[str, None, None]]:
    """生成文本
    
    Args:
        request: Ollama请求对象
        
    Returns:
        str: 非流式模式下返回完整文本
        Generator[str, None, None]: 流式模式下返回文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 请求参数无效
    """
```

**示例:**

```python
from industrytslib.utils.llm import OllamaClient, OllamaRequest

client = OllamaClient()
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="解释LSTM的工作原理",
    stream=False
)

response = client.generate(request)
print(response)
```

##### chat

```python
def chat(self, request: OllamaRequest) -> Union[str, Generator[str, None, None]]:
    """聊天对话
    
    Args:
        request: 包含messages字段的Ollama请求对象
        
    Returns:
        str: 非流式模式下返回完整回复
        Generator[str, None, None]: 流式模式下返回文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 请求参数无效
    """
```

**示例:**

```python
messages = [
    {"role": "system", "content": "你是一个工业AI助手"},
    {"role": "user", "content": "如何选择时间序列预测模型？"}
]

request = OllamaRequest(
    model="qwen2.5:7b",
    messages=messages,
    stream=False
)

response = client.chat(request)
print(response)
```

##### list_models

```python
def list_models(self) -> List[str]:
    """获取可用模型列表
    
    Returns:
        List[str]: 可用模型名称列表
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
    """
```

**示例:**

```python
models = client.list_models()
print("可用模型:")
for model in models:
    print(f"  - {model}")
```

##### async_generate

```python
async def async_generate(self, request: OllamaRequest) -> Union[str, AsyncGenerator[str, None]]:
    """异步生成文本
    
    Args:
        request: Ollama请求对象
        
    Returns:
        str: 非流式模式下返回完整文本
        AsyncGenerator[str, None]: 流式模式下返回异步文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 请求参数无效
    """
```

**示例:**

```python
import asyncio

async def async_example():
    client = OllamaClient()
    request = OllamaRequest(
        model="qwen2.5:7b",
        prompt="分析工业数据的异常检测方法",
        stream=False
    )
    
    response = await client.async_generate(request)
    print(response)

asyncio.run(async_example())
```

##### async_chat

```python
async def async_chat(self, request: OllamaRequest) -> Union[str, AsyncGenerator[str, None]]:
    """异步聊天对话
    
    Args:
        request: 包含messages字段的Ollama请求对象
        
    Returns:
        str: 非流式模式下返回完整回复
        AsyncGenerator[str, None]: 流式模式下返回异步文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 请求参数无效
    """
```

### <a name="ollamaconfig"></a>OllamaConfig

Ollama客户端配置类,基于Pydantic BaseModel。

```python
class OllamaConfig(BaseModel):
    """Ollama客户端配置"""
    
    host: str = "localhost"
    port: int = 11434
    timeout: int = 30
```

#### 字段

- `host` (str): Ollama服务器地址,默认"localhost"
- `port` (int): Ollama服务器端口,默认11434
- `timeout` (int): 请求超时时间(秒),默认30

**示例:**

```python
from industrytslib.utils.llm import OllamaConfig, OllamaClient

# 自定义配置
config = OllamaConfig(
    host="*************",
    port=11434,
    timeout=60
)

client = OllamaClient(config)
```

### <a name="ollamarequest"></a>OllamaRequest

Ollama API请求类,基于Pydantic BaseModel。

```python
class OllamaRequest(BaseModel):
    """Ollama API请求"""
    
    model: str
    prompt: Optional[str] = None
    messages: Optional[List[Dict[str, str]]] = None
    stream: bool = True
    options: Optional[Dict[str, Any]] = None
```

#### 字段

- `model` (str): 模型名称,必填
- `prompt` (Optional[str]): 提示词,用于generate API
- `messages` (Optional[List[Dict[str, str]]]): 消息列表,用于chat API
- `stream` (bool): 是否流式输出,默认True
- `options` (Optional[Dict[str, Any]]): 模型参数选项

#### 验证规则

- `prompt`和`messages`至少需要提供一个
- `prompt`用于generate API,`messages`用于chat API

**示例:**

```python
from industrytslib.utils.llm import OllamaRequest

# Generate API请求
generate_request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="解释工业4.0的核心技术",
    stream=False,
    options={
        "temperature": 0.3,
        "top_p": 0.8,
        "num_predict": 200
    }
)

# Chat API请求
chat_request = OllamaRequest(
    model="qwen2.5:7b",
    messages=[
        {"role": "system", "content": "你是一个工业AI助手"},
        {"role": "user", "content": "如何优化PID控制器参数？"}
    ],
    stream=True
)
```

#### 模型参数选项

`options`字段支持以下参数:

```python
options = {
    "temperature": 0.7,      # 随机性控制 (0.0-1.0)
    "top_p": 0.9,           # 核采样 (0.0-1.0)
    "top_k": 40,            # Top-K采样
    "num_predict": 200,     # 最大生成token数
    "repeat_penalty": 1.1,  # 重复惩罚 (>1.0)
    "stop": ["\n", "。"],    # 停止词列表
    "seed": 42,             # 随机种子
    "num_ctx": 2048,        # 上下文长度
    "num_batch": 512,       # 批处理大小
    "num_gqa": 1,           # 分组查询注意力
    "num_gpu": 1,           # GPU数量
    "main_gpu": 0,          # 主GPU ID
    "low_vram": False,      # 低显存模式
    "f16_kv": True,         # 使用FP16键值缓存
    "logits_all": False,    # 返回所有logits
    "vocab_only": False,    # 仅加载词汇表
    "use_mmap": True,       # 使用内存映射
    "use_mlock": False,     # 使用内存锁定
    "embedding_only": False, # 仅嵌入模式
    "num_thread": 8         # 线程数
}
```

## <a name="siliconflow-api"></a>SiliconFlow API

### <a name="siliconflowclient"></a>SiliconFlowClient

SiliconFlow API的主要客户端类,提供同步和异步的文本生成功能。

```python
class SiliconFlowClient:
    """SiliconFlow API客户端"""
    
    def __init__(self, api_key: str = None, config: SiliconFlowConfig = None)
```

#### 参数

- `api_key` (str): SiliconFlow API密钥,如果为None则从环境变量获取
- `config` (Optional[SiliconFlowConfig]): 客户端配置,如果为None则使用默认配置

#### 方法

##### chat_completions

```python
def chat_completions(self, request: SiliconFlowRequest) -> SiliconFlowResponse:
    """聊天完成
    
    Args:
        request: SiliconFlow请求对象
        
    Returns:
        SiliconFlowResponse: 响应对象
        
    Raises:
        SiliconFlowError: API调用失败
        ValueError: 请求参数无效
    """
```

**示例:**

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowRequest, SiliconFlowMessage

client = SiliconFlowClient(api_key="your-api-key")

messages = [
    SiliconFlowMessage(role="user", content="解释LSTM的工作原理")
]

request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=messages,
    temperature=0.3,
    max_tokens=500
)

response = client.chat_completions(request)
print(response.choices[0].message.content)
```

##### async_chat_completions

```python
async def async_chat_completions(self, request: SiliconFlowRequest) -> SiliconFlowResponse:
    """异步聊天完成
    
    Args:
        request: SiliconFlow请求对象
        
    Returns:
        SiliconFlowResponse: 响应对象
        
    Raises:
        SiliconFlowError: API调用失败
        ValueError: 请求参数无效
    """
```

**示例:**

```python
import asyncio

async def async_example():
    client = SiliconFlowClient(api_key="your-api-key")
    
    request = SiliconFlowRequest(
        model="Qwen/Qwen2.5-7B-Instruct",
        messages=[
            SiliconFlowMessage(role="user", content="分析工业数据的异常检测方法")
        ],
        temperature=0.2
    )
    
    response = await client.async_chat_completions(request)
    print(response.choices[0].message.content)

asyncio.run(async_example())
```

### <a name="siliconflowconfig"></a>SiliconFlowConfig

SiliconFlow客户端配置类,基于Pydantic BaseModel。

```python
class SiliconFlowConfig(BaseModel):
    """SiliconFlow客户端配置"""
    
    api_key: str
    base_url: str = "https://api.siliconflow.cn/v1"
    timeout: int = 60
    max_retries: int = 3
    retry_delay: float = 1.0
```

#### 字段

- `api_key` (str): SiliconFlow API密钥,必填
- `base_url` (str): API基础URL,默认"https://api.siliconflow.cn/v1"
- `timeout` (int): 请求超时时间(秒),默认60
- `max_retries` (int): 最大重试次数,默认3
- `retry_delay` (float): 重试延迟(秒),默认1.0

**示例:**

```python
from industrytslib.utils.llm import SiliconFlowConfig, SiliconFlowClient

# 自定义配置
config = SiliconFlowConfig(
    api_key="your-api-key",
    timeout=120,
    max_retries=5,
    retry_delay=2.0
)

client = SiliconFlowClient(config=config)
```

### <a name="siliconflowrequest"></a>SiliconFlowRequest

SiliconFlow API请求类,基于Pydantic BaseModel。

```python
class SiliconFlowRequest(BaseModel):
    """SiliconFlow API请求"""
    
    model: str
    messages: List[SiliconFlowMessage]
    temperature: float = 0.7
    top_p: float = 1.0
    max_tokens: int = 1024
    stream: bool = False
    stop: Optional[List[str]] = None
```

#### 字段

- `model` (str): 模型名称,必填
- `messages` (List[SiliconFlowMessage]): 消息列表,必填
- `temperature` (float): 温度参数,控制随机性,范围0.0-1.0,默认0.7
- `top_p` (float): Top-p采样参数,范围0.0-1.0,默认1.0
- `max_tokens` (int): 最大生成token数,默认1024
- `stream` (bool): 是否流式输出,默认False
- `stop` (Optional[List[str]]): 停止词列表,可选

**示例:**

```python
from industrytslib.utils.llm import SiliconFlowRequest, SiliconFlowMessage

# 基础请求
request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=[
        SiliconFlowMessage(
            role="system",
            content="你是一个工业AI助手"
        ),
        SiliconFlowMessage(
            role="user",
            content="如何优化PID控制器参数？"
        )
    ],
    temperature=0.3,
    max_tokens=800,
    stream=False
)

# 流式请求
stream_request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=[
        SiliconFlowMessage(
            role="user",
            content="详细解释时间序列预测的方法"
        )
    ],
    temperature=0.2,
    stream=True,
    stop=["\n\n", "总结"]
)
```

### <a name="siliconflowmessage"></a>SiliconFlowMessage

聊天消息类,基于Pydantic BaseModel。

```python
class SiliconFlowMessage(BaseModel):
    """SiliconFlow聊天消息"""
    
    role: str
    content: str
```

#### 字段

- `role` (str): 消息角色,支持"system"、"user"、"assistant"
- `content` (str): 消息内容

**示例:**

```python
from industrytslib.utils.llm import SiliconFlowMessage

# 系统消息
system_msg = SiliconFlowMessage(
    role="system",
    content="你是一个专业的工业时间序列分析专家"
)

# 用户消息
user_msg = SiliconFlowMessage(
    role="user",
    content="请分析这个时间序列数据的趋势"
)

# 助手消息
assistant_msg = SiliconFlowMessage(
    role="assistant",
    content="根据数据分析,该时间序列呈现上升趋势..."
)
```

### <a name="siliconflowresponse"></a>SiliconFlowResponse

SiliconFlow API响应类,基于Pydantic BaseModel。

```python
class SiliconFlowResponse(BaseModel):
    """SiliconFlow API响应"""
    
    id: str
    object: str
    created: int
    model: str
    choices: List[SiliconFlowChoice]
    usage: SiliconFlowUsage
```

#### 字段

- `id` (str): 响应ID
- `object` (str): 对象类型
- `created` (int): 创建时间戳
- `model` (str): 使用的模型名称
- `choices` (List[SiliconFlowChoice]): 选择列表
- `usage` (SiliconFlowUsage): 使用统计

**示例:**

```python
# 响应对象通常由API调用返回
response = client.chat_completions(request)

print(f"响应ID: {response.id}")
print(f"使用模型: {response.model}")
print(f"回复内容: {response.choices[0].message.content}")
print(f"使用token: {response.usage.total_tokens}")
```

## <a name="便捷函数"></a>便捷函数

### <a name="ollama便捷函数"></a>Ollama便捷函数

### quick_generate

快速文本生成函数,简化常用操作。

```python
def quick_generate(
    prompt: str,
    model: str = "qwen2.5:7b",
    stream: bool = False,
    host: str = "localhost",
    port: int = 11434,
    timeout: int = 30,
    **options
) -> Union[str, Generator[str, None, None]]:
    """快速生成文本
    
    Args:
        prompt: 提示词
        model: 模型名称
        stream: 是否流式输出
        host: 服务器地址
        port: 服务器端口
        timeout: 超时时间
        **options: 模型参数选项
        
    Returns:
        str: 非流式模式下返回完整文本
        Generator[str, None, None]: 流式模式下返回文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 参数无效
    """
```

**示例:**

```python
from industrytslib.utils.llm import quick_generate

# 简单调用
response = quick_generate(
    prompt="解释时间序列预测的基本概念",
    model="qwen2.5:7b",
    stream=False,
    temperature=0.3
)
print(response)

# 流式输出
for chunk in quick_generate(
    prompt="详细分析LSTM的优缺点",
    stream=True,
    temperature=0.2,
    num_predict=300
):
    print(chunk, end="", flush=True)
```

### async_quick_generate

异步快速文本生成函数。

```python
async def async_quick_generate(
    prompt: str,
    model: str = "qwen2.5:7b",
    stream: bool = False,
    host: str = "localhost",
    port: int = 11434,
    timeout: int = 30,
    **options
) -> Union[str, AsyncGenerator[str, None]]:
    """异步快速生成文本
    
    Args:
        prompt: 提示词
        model: 模型名称
        stream: 是否流式输出
        host: 服务器地址
        port: 服务器端口
        timeout: 超时时间
        **options: 模型参数选项
        
    Returns:
        str: 非流式模式下返回完整文本
        AsyncGenerator[str, None]: 流式模式下返回异步文本生成器
        
    Raises:
        ConnectionError: 连接服务器失败
        TimeoutError: 请求超时
        ValueError: 参数无效
    """
```

**示例:**

```python
import asyncio
from industrytslib.utils.llm import async_quick_generate

async def async_example():
    # 异步生成
    response = await async_quick_generate(
        prompt="分析工业数据的预处理步骤",
        model="qwen2.5:7b",
        stream=False,
        temperature=0.2
    )
    print(response)
    
    # 异步流式生成
    async for chunk in await async_quick_generate(
        prompt="详细说明PID控制器的调参方法",
        stream=True,
        temperature=0.1
    ):
        print(chunk, end="", flush=True)

asyncio.run(async_example())
```

### create_ollama_client

创建Ollama客户端的便捷函数。

```python
def create_ollama_client(
    host: str = "localhost",
    port: int = 11434,
    timeout: int = 30
) -> OllamaClient:
    """创建Ollama客户端
    
    Args:
        host: 服务器地址
        port: 服务器端口
        timeout: 超时时间
        
    Returns:
        OllamaClient: 配置好的客户端实例
    """
```

**示例:**

```python
from industrytslib.utils.llm import create_ollama_client

# 创建本地客户端
local_client = create_ollama_client()

# 创建远程客户端
remote_client = create_ollama_client(
    host="*************",
    port=11434,
    timeout=60
)
```

### <a name="siliconflow便捷函数"></a>SiliconFlow便捷函数

### sf_quick_generate

SiliconFlow快速文本生成函数。

```python
def sf_quick_generate(
    prompt: str,
    model: str = "Qwen/Qwen2.5-7B-Instruct",
    api_key: str = None,
    temperature: float = 0.7,
    top_p: float = 1.0,
    max_tokens: int = 1024,
    stream: bool = False,
    **kwargs
) -> Union[str, Iterator[str]]:
    """快速生成文本
    
    Args:
        prompt: 提示词
        model: 模型名称
        api_key: API密钥,如果为None则从环境变量获取
        temperature: 温度参数
        top_p: Top-p参数
        max_tokens: 最大token数
        stream: 是否流式输出
        **kwargs: 其他参数
        
    Returns:
        str: 非流式模式下返回完整文本
        Iterator[str]: 流式模式下返回文本迭代器
        
    Raises:
        SiliconFlowError: API调用失败
        ValueError: 参数无效
    """
```

**示例:**

```python
from industrytslib.utils.llm import sf_quick_generate

# 简单调用
response = sf_quick_generate(
    prompt="解释时间序列预测的基本概念",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.3,
    max_tokens=500,
    stream=False
)
print(response)

# 流式输出
for chunk in sf_quick_generate(
    prompt="详细分析LSTM的优缺点",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    stream=True,
    temperature=0.2,
    max_tokens=800
):
    print(chunk, end="", flush=True)
```

### sf_async_quick_generate

SiliconFlow异步快速文本生成函数。

```python
async def sf_async_quick_generate(
    prompt: str,
    model: str = "Qwen/Qwen2.5-7B-Instruct",
    api_key: str = None,
    temperature: float = 0.7,
    top_p: float = 1.0,
    max_tokens: int = 1024,
    stream: bool = False,
    **kwargs
) -> Union[str, AsyncIterator[str]]:
    """异步快速生成文本
    
    Args:
        prompt: 提示词
        model: 模型名称
        api_key: API密钥,如果为None则从环境变量获取
        temperature: 温度参数
        top_p: Top-p参数
        max_tokens: 最大token数
        stream: 是否流式输出
        **kwargs: 其他参数
        
    Returns:
        str: 非流式模式下返回完整文本
        AsyncIterator[str]: 流式模式下返回异步文本迭代器
        
    Raises:
        SiliconFlowError: API调用失败
        ValueError: 参数无效
    """
```

**示例:**

```python
import asyncio
from industrytslib.utils.llm import sf_async_quick_generate

async def async_example():
    # 异步生成
    response = await sf_async_quick_generate(
        prompt="分析工业数据的预处理步骤",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key",
        temperature=0.2,
        max_tokens=600,
        stream=False
    )
    print(response)
    
    # 异步流式生成
    async for chunk in await sf_async_quick_generate(
        prompt="详细说明PID控制器的调参方法",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key",
        stream=True,
        temperature=0.1
    ):
        print(chunk, end="", flush=True)

asyncio.run(async_example())
```

### create_siliconflow_client

创建SiliconFlow客户端的便捷函数。

```python
def create_siliconflow_client(
    api_key: str = None,
    **config_kwargs
) -> SiliconFlowClient:
    """创建SiliconFlow客户端
    
    Args:
        api_key: API密钥,如果为None则从环境变量获取
        **config_kwargs: 配置参数
        
    Returns:
        SiliconFlowClient: 配置好的客户端实例
    """
```

**示例:**

```python
from industrytslib.utils.llm import create_siliconflow_client

# 创建默认客户端
client = create_siliconflow_client(api_key="your-api-key")

# 创建自定义配置客户端
custom_client = create_siliconflow_client(
    api_key="your-api-key",
    timeout=120,
    max_retries=5,
    retry_delay=2.0
)
```

### get_supported_models

获取SiliconFlow支持的模型列表。

```python
def get_supported_models() -> List[str]:
    """获取支持的模型列表
    
    Returns:
        List[str]: 支持的模型名称列表
    """
```

**示例:**

```python
from industrytslib.utils.llm import get_supported_models

models = get_supported_models()
print(f"总共支持 {len(models)} 个模型")

# 显示前10个模型
for model in models[:10]:
    print(f"  - {model}")
```

### is_reasoning_model

判断是否为推理模型。

```python
def is_reasoning_model(model: str) -> bool:
    """判断是否为推理模型
    
    Args:
        model: 模型名称
        
    Returns:
        bool: 是否为推理模型
    """
```

**示例:**

```python
from industrytslib.utils.llm import is_reasoning_model

# 检查不同模型
models_to_check = [
    "Qwen/Qwen2.5-7B-Instruct",
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
    "Qwen/QwQ-32B-Preview"
]

for model in models_to_check:
    is_reasoning = is_reasoning_model(model)
    print(f"{model}: {'推理模型' if is_reasoning else '常规模型'}")
```

## <a name="异常类"></a>异常类

### Ollama异常类

#### OllamaConnectionError

```python
class OllamaConnectionError(Exception):
    """Ollama连接错误"""
    pass
```

### OllamaTimeoutError

```python
class OllamaTimeoutError(Exception):
    """Ollama请求超时错误"""
    pass
```

### OllamaValidationError

```python
class OllamaValidationError(Exception):
    """Ollama请求验证错误"""
    pass
```

**异常处理示例:**

```python
from industrytslib.utils.llm import (
    OllamaClient, OllamaRequest,
    OllamaConnectionError, OllamaTimeoutError, OllamaValidationError
)

try:
    client = OllamaClient()
    request = OllamaRequest(
        model="qwen2.5:7b",
        prompt="分析工业数据",
        stream=False
    )
    response = client.generate(request)
    print(response)
    
except OllamaConnectionError:
    print("连接Ollama服务器失败,请检查服务是否运行")
except OllamaTimeoutError:
    print("请求超时,请稍后重试")
except OllamaValidationError as e:
    print(f"请求参数无效: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

### SiliconFlow异常类

#### SiliconFlowError

```python
class SiliconFlowError(Exception):
    """SiliconFlow API错误基类"""
    pass
```

#### SiliconFlowAuthError

```python
class SiliconFlowAuthError(SiliconFlowError):
    """SiliconFlow认证错误"""
    pass
```

#### SiliconFlowRateLimitError

```python
class SiliconFlowRateLimitError(SiliconFlowError):
    """SiliconFlow频率限制错误"""
    pass
```

#### SiliconFlowModelError

```python
class SiliconFlowModelError(SiliconFlowError):
    """SiliconFlow模型错误"""
    pass
```

#### SiliconFlowValidationError

```python
class SiliconFlowValidationError(SiliconFlowError):
    """SiliconFlow请求验证错误"""
    pass
```

**异常处理示例:**

```python
from industrytslib.utils.llm import (
    sf_quick_generate,
    SiliconFlowError, SiliconFlowAuthError, SiliconFlowRateLimitError,
    SiliconFlowModelError, SiliconFlowValidationError
)
import time

def robust_generate(prompt, model, api_key, max_retries=3):
    """带错误处理的生成函数"""
    for attempt in range(max_retries):
        try:
            return sf_quick_generate(
                prompt=prompt,
                model=model,
                api_key=api_key,
                temperature=0.3
            )
            
        except SiliconFlowAuthError:
            print("API密钥无效,请检查密钥设置")
            break
            
        except SiliconFlowRateLimitError:
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2
                print(f"遇到频率限制,等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print("频率限制,请稍后再试")
                
        except SiliconFlowModelError as e:
            print(f"模型错误: {e}")
            break
            
        except SiliconFlowValidationError as e:
            print(f"请求参数无效: {e}")
            break
            
        except SiliconFlowError as e:
            print(f"SiliconFlow API错误: {e}")
            if attempt == max_retries - 1:
                break
            time.sleep(1)
            
        except Exception as e:
            print(f"未知错误: {e}")
            break
    
    return None

# 使用示例
result = robust_generate(
    prompt="分析工业数据的异常检测方法",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

if result:
    print(result)
else:
    print("生成失败")
```

## <a name="类型定义"></a>类型定义

### 导入的类型

```python
from typing import (
    Optional, Union, List, Dict, Any,
    Generator, AsyncGenerator
)
from pydantic import BaseModel, validator, model_validator
```

### 消息类型

```python
MessageType = Dict[str, str]  # {"role": str, "content": str}
MessagesType = List[MessageType]
```

**消息格式:**

```python
# 系统消息
system_message = {
    "role": "system",
    "content": "你是一个专业的工业AI助手"
}

# 用户消息
user_message = {
    "role": "user",
    "content": "如何优化工业过程控制？"
}

# 助手消息
assistant_message = {
    "role": "assistant",
    "content": "工业过程控制优化可以从以下几个方面入手..."
}

# 完整对话
messages = [system_message, user_message, assistant_message]
```

### 选项类型

```python
OptionsType = Dict[str, Any]  # 模型参数选项
```

### 响应类型

```python
# 同步响应类型
SyncResponseType = Union[str, Generator[str, None, None]]

# 异步响应类型
AsyncResponseType = Union[str, AsyncGenerator[str, None]]
```

## 完整使用示例

### 基础使用

```python
from industrytslib.utils.llm import (
    OllamaClient, OllamaConfig, OllamaRequest,
    quick_generate, create_ollama_client
)

# 方式1:使用便捷函数
response = quick_generate(
    prompt="解释工业4.0的核心概念",
    model="qwen2.5:7b",
    temperature=0.3
)
print(response)

# 方式2:使用客户端类
client = create_ollama_client()
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析时间序列数据的特征工程方法",
    stream=False,
    options={"temperature": 0.2, "num_predict": 300}
)
response = client.generate(request)
print(response)

# 方式3:完全自定义配置
config = OllamaConfig(
    host="localhost",
    port=11434,
    timeout=60
)
client = OllamaClient(config)
```

### 高级使用

```python
import asyncio
from industrytslib.utils.llm import OllamaClient, OllamaRequest

class IndustrialAIAssistant:
    """工业AI助手类"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
        self.system_prompt = """
        你是一个专业的工业AI助手,专门协助工程师进行:
        - 时间序列预测和分析
        - 工业过程优化
        - 异常检测和故障诊断
        - 数据预处理和特征工程
        请用专业、准确、实用的方式回答问题。
        """
    
    def analyze_data(self, data_description: str, stream: bool = False):
        """分析工业数据"""
        prompt = f"""
        请分析以下工业数据:
        
        数据描述:{data_description}
        
        请提供:
        1. 数据质量评估
        2. 可能的数据问题
        3. 预处理建议
        4. 特征工程建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=stream,
            options={"temperature": 0.2, "num_predict": 400}
        )
        
        return self.client.generate(request)
    
    def recommend_model(self, problem_type: str, data_characteristics: str):
        """推荐模型架构"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {
                "role": "user",
                "content": f"""
                问题类型:{problem_type}
                数据特征:{data_characteristics}
                
                请推荐最适合的深度学习模型架构和配置。
                """
            }
        ]
        
        request = OllamaRequest(
            model=self.model,
            messages=messages,
            stream=False,
            options={"temperature": 0.1, "num_predict": 500}
        )
        
        return self.client.chat(request)
    
    async def batch_analysis(self, questions: List[str]):
        """批量分析"""
        tasks = []
        for question in questions:
            request = OllamaRequest(
                model=self.model,
                prompt=question,
                stream=False,
                options={"temperature": 0.2}
            )
            task = self.client.async_generate(request)
            tasks.append(task)
        
        return await asyncio.gather(*tasks)

# 使用示例
assistant = IndustrialAIAssistant()

# 数据分析
analysis = assistant.analyze_data(
    "化工反应器温度数据,1分钟采样,包含3个月历史数据,存在部分缺失值"
)
print(analysis)

# 模型推荐
recommendation = assistant.recommend_model(
    "多变量时间序列预测",
    "10个传感器变量,5分钟采样,明显的日周期性"
)
print(recommendation)

# 批量分析
questions = [
    "解释LSTM的梯度消失问题",
    "分析Transformer在时间序列中的应用",
    "说明注意力机制的工作原理"
]
results = asyncio.run(assistant.batch_analysis(questions))
for i, result in enumerate(results):
    print(f"问题{i+1}的回答: {result}")
```

---

## 总结

本API参考文档涵盖了industrytslib LLM模块的所有公开接口。主要包括:

- **OllamaClient**: 核心客户端类,提供完整的API功能
- **OllamaConfig**: 配置类,用于自定义连接参数
- **OllamaRequest**: 请求类,封装API请求参数
- **便捷函数**: 简化常用操作的快捷函数
- **异常类**: 完善的错误处理机制
- **类型定义**: 完整的类型提示支持

通过这些接口,你可以轻松地在工业AI应用中集成强大的LLM功能。