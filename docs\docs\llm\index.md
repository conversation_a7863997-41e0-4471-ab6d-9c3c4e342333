# LLM集成模块教程

本教程介绍如何在industrytslib中使用LLM(大语言模型)功能,专门为工业时间序列AI应用场景设计。

## 📋 目录

- [概述](#概述)
- [安装与配置](#安装与配置)
- [快速开始](#快速开始)
- [基础功能](#基础功能)
- [高级功能](#高级功能)
- [工业应用场景](#工业应用场景)
- [性能优化](#性能优化)
- [故障排除](#故障排除)
- [API参考](#api参考)

## 📚 文档导航

- [快速开始](quickstart.md) - 5分钟上手LLM功能
- [通用LLM客户端指南](universal-client-guide.md) - 详细的使用指南和最佳实践
- [API参考](api-reference.md) - 完整的API文档
- [工业应用场景](industrial-applications.md) - 实际应用案例
- [性能优化](performance-optimization.md) - 提升性能的技巧
- [故障排除](troubleshooting.md) - 常见问题解决方案

## <a name="概述"></a>概述

**industrytslib LLM模块** 为工业时间序列AI应用提供了强大的大语言模型集成能力。通过统一的接口设计,您可以轻松地将先进的LLM技术应用到工业数据分析、异常检测、工艺优化等关键场景中,实现智能化的工业决策支持。

### 🎯 设计理念

- **工业优先**:专门针对工业时间序列场景进行优化
- **统一接口**:支持多个LLM服务提供商,使用相同的API
- **生产就绪**:内置错误处理、重试机制和性能优化
- **易于集成**:简单的配置和直观的API设计

industrytslib的LLM模块提供了完整的大语言模型客户端,支持Ollama和SiliconFlow两种服务,专门针对工业时间序列预测、异常检测和智能决策等场景进行了优化。

### 🚀 核心特性

#### 多厂商支持
- **统一接口**:支持Ollama、SiliconFlow等多个LLM服务提供商
- **运行时切换**:可在程序运行时动态切换不同的服务提供商
- **配置灵活**:支持环境变量、配置文件、代码配置等多种方式

#### 工业场景优化
- **时间序列分析**:专门优化的提示词和参数配置
- **异常检测诊断**:智能的故障分析和解决方案推荐
- **工艺优化建议**:基于数据的工艺参数优化建议
- **模型选择指导**:针对工业数据特点的模型推荐

#### 性能与可靠性
- **异步支持**:原生支持异步操作,提高并发性能
- **流式输出**:支持实时流式文本生成,提升响应体验
- **错误处理**:统一的错误处理和智能重试机制
- **类型安全**:完整的类型提示和数据验证
- **扩展性强**:易于添加新的LLM服务提供商

### 🌟 详细特性

#### Ollama客户端
- ✅ **多服务器支持**:本地和远程Ollama服务器
- ✅ **双模式API**:同步和异步调用
- ✅ **流式处理**:实时流式和批量文本生成
- ✅ **多API支持**:Generate API和Chat API
- ✅ **模型管理**:支持获取可用模型列表

#### SiliconFlow客户端
- ✅ **云端LLM服务**:支持SiliconFlow平台的多种大语言模型
- ✅ **丰富模型选择**:Qwen、DeepSeek、GLM、MiniMax等系列模型
- ✅ **推理模型支持**:特别支持DeepSeek-R1、QwQ等推理模型
- ✅ **Chat Completions API**:兼容OpenAI格式的聊天完成接口
- ✅ **参数控制**:支持temperature、top_p、max_tokens等生成参数
- ✅ **重试机制**:内置指数退避重试策略

#### 通用特性
- ✅ **类型安全**:完整的类型提示和数据验证
- ✅ **工业优化**:专门的工业AI提示词和场景
- ✅ **错误处理**:完善的错误处理和日志记录
- ✅ **异步支持**:高性能异步API调用

### 🏭 工业应用场景

#### 🔍 时间序列数据分析
- **数据质量评估**:自动识别数据质量问题,提供预处理建议
- **模式识别**:发现隐藏的数据模式和周期性规律
- **趋势分析**:分析长期趋势和短期波动的原因
- **预测建模**:推荐最适合的预测模型和参数配置
- **结果解释**:将复杂的分析结果转化为业务洞察

#### ⚠️ 异常检测与诊断
- **智能监控**:实时监控设备状态,及时发现异常
- **故障诊断**:基于历史数据和专家知识进行根因分析
- **预测性维护**:预测设备故障时间,制定维护计划
- **风险评估**:评估异常的严重程度和影响范围
- **解决方案**:提供具体的处理步骤和预防措施

#### ⚙️ 工艺优化
- **参数调优**:基于历史数据推荐最优工艺参数
- **能耗优化**:分析能耗模式,提供节能改进方案
- **质量控制**:优化工艺流程,提高产品质量稳定性
- **效率提升**:识别生产瓶颈,提供效率改进建议
- **成本控制**:分析成本结构,提供降本增效策略

#### 🤖 智能决策支持
- **模型选择**:根据数据特征推荐最适合的AI模型
- **架构设计**:提供系统架构和技术选型建议
- **性能优化**:分析模型性能,提供优化方案
- **部署指导**:提供模型部署和运维的最佳实践

## <a name="安装与配置"></a>安装与配置

### 📦 安装依赖

```bash
# 使用uv安装(推荐)
uv add industrytslib

# 或使用pip安装
pip install industrytslib

# 安装可选依赖(用于特定功能)
uv add requests pydantic loguru
```

### ⚙️ 环境配置

#### 方式一:环境变量配置

```bash
# SiliconFlow服务配置
export LLM_PROVIDER="siliconflow"
export SILICONFLOW_API_KEY="your-siliconflow-api-key"
export LLM_MODEL="Qwen/Qwen2.5-7B-Instruct"
export LLM_BASE_URL="https://api.siliconflow.cn/v1"

# Ollama服务配置(本地部署)
export LLM_PROVIDER="ollama"
export OLLAMA_HOST="localhost"
export OLLAMA_PORT="11434"
export LLM_MODEL="qwen2.5:7b"
```

#### 方式二:配置文件

创建 `llm_config.toml` 文件:

```toml
# 默认配置
[default]
provider = "siliconflow"
model = "Qwen/Qwen2.5-7B-Instruct"
temperature = 0.7
max_tokens = 1024

# SiliconFlow配置
[siliconflow]
api_key = "your-api-key"
base_url = "https://api.siliconflow.cn/v1"
timeout = 60
max_retries = 3

# Ollama配置
[ollama]
host = "localhost"
port = 11434
timeout = 60
```

### 🚀 Ollama服务器安装(可选)

#### 本地安装

```bash
# 下载并安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 启动Ollama服务
ollama serve

# 下载模型(推荐用于工业场景)
ollama pull qwen2.5:7b
ollama pull llama3.1:latest
```

#### 远程服务器配置

如果使用远程Ollama服务器,确保:

1. 远程服务器已安装并运行Ollama
2. 端口11434已开放
3. 网络连接正常

### ✅ 验证安装

```bash
# 快速测试连接
uv run examples/experiment/exp_llm/quick_test_remote.py

# 或使用Python测试
python -c "from industrytslib.utils.llm import quick_generate; print(quick_generate('Hello'))"
```

## <a name="快速开始"></a>🚀 快速开始

### 第一个LLM调用

#### 使用便捷函数(推荐新手)

```python
from industrytslib.utils.llm import quick_generate

# 简单的文本生成
response = quick_generate("请简单介绍工业4.0的核心特征")
print(response)
```

#### 使用通用客户端(推荐生产环境)

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig

# 创建配置
config = LLMConfig(
    provider="siliconflow",  # 或 "ollama"
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"  # SiliconFlow需要
)

# 创建客户端
client = UniversalLLMClient(config)

# 发送消息
messages = [
    {"role": "user", "content": "什么是数字孪生技术？"}
]

response = client.chat_completions(messages)
print(f"响应: {response.content}")
print(f"使用令牌: {response.usage.total_tokens if response.usage else 'N/A'}")
```

### 工业场景示例

#### 时间序列数据分析

```python
from industrytslib.utils.llm import quick_chat

# 分析工业数据
response = quick_chat(
    messages=[
        {
            "role": "system",
            "content": "你是一个专业的工业数据分析专家。"
        },
        {
            "role": "user",
            "content": """我有一个化工反应器的温度数据:
            - 采样频率:1分钟
            - 数据长度:30天
            - 温度范围:80-120°C
            - 存在2%缺失值和明显的24小时周期
            
            请推荐合适的预测模型和预处理策略。"""
        }
    ],
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)
print(response)
```

#### 异常检测诊断

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig

# 配置异常检测专用客户端
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.1,  # 低温度确保诊断准确性
    max_tokens=512
)

client = UniversalLLMClient(config)

# 异常诊断
messages = [
    {
        "role": "system",
        "content": "你是一个工业异常检测专家,能够诊断设备异常并提供解决方案。"
    },
    {
        "role": "user",
        "content": """检测到以下异常:
        - 温度:正常80-85°C,当前92°C且持续上升
        - 压力:正常2.0-2.5bar,当前2.1bar
        - 流量:正常100-120L/min,当前85L/min且下降
        
        异常持续2小时,请分析原因并提供处理建议。"""
    }
]

response = client.chat_completions(messages)
print(f"诊断结果: {response.content}")
```

### 最简单的使用方式

```python
from industrytslib.utils.llm import quick_generate

# 快速生成文本
response = quick_generate(
    prompt="请简单介绍工业4.0的核心特征",
    model="qwen2.5:7b",
    stream=False
)
print(response)
```

### 流式输出

```python
from industrytslib.utils.llm import quick_generate

# 流式生成,实时显示结果
response_stream = quick_generate(
    prompt="请详细解释时间序列预测在化工行业的应用",
    model="qwen2.5:7b",
    stream=True
)

for chunk in response_stream:
    print(chunk, end="", flush=True)
```

### 连接远程服务器

```python
from industrytslib.utils.llm import create_ollama_client, OllamaRequest

# 创建远程客户端
client = create_ollama_client(
    host="*************",  # 远程服务器IP
    port=11434,
    timeout=60
)

# 创建请求
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析工业数据预处理的关键步骤",
    stream=False
)

response = client.generate(request)
print(response)
```

## <a name="基础功能"></a>基础功能

### 1. 客户端配置

```python
from industrytslib.utils.llm import OllamaClient, OllamaConfig

# 基础配置
config = OllamaConfig(
    host="localhost",  # 服务器地址
    port=11434,        # 端口
    timeout=30         # 超时时间(秒)
)

client = OllamaClient(config)
```

### 2. 文本生成

```python
from industrytslib.utils.llm import OllamaRequest

# 创建生成请求
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="请解释LSTM在工业预测中的优势",
    stream=False,
    options={
        "temperature": 0.3,  # 控制随机性
        "top_p": 0.8,       # 核采样
        "num_predict": 200  # 最大生成token数
    }
)

response = client.generate(request)
print(response)
```

### 3. 聊天对话

```python
# 聊天API使用
messages = [
    {
        "role": "system", 
        "content": "你是一个专业的工业AI助手,专门帮助解决时间序列预测和工业数据分析问题。"
    },
    {
        "role": "user", 
        "content": "在化工过程中,如何选择合适的时间序列预测模型？"
    }
]

request = OllamaRequest(
    model="qwen2.5:7b",
    messages=messages,
    stream=False
)

response = client.chat(request)
print(response)
```

### 4. 获取可用模型

```python
# 获取服务器上的可用模型
models = client.list_models()
print("可用模型:")
for model in models:
    print(f"  - {model}")
```

## <a name="高级功能"></a>高级功能

### 1. 异步操作

```python
import asyncio
from industrytslib.utils.llm import async_quick_generate

async def async_example():
    # 异步生成
    response = await async_quick_generate(
        prompt="分析工业数据的异常检测方法",
        model="qwen2.5:7b",
        stream=False
    )
    print(response)
    
    # 异步流式生成
    async for chunk in await async_quick_generate(
        prompt="详细说明PID控制器的工作原理",
        model="qwen2.5:7b",
        stream=True
    ):
        print(chunk, end="", flush=True)

# 运行异步函数
asyncio.run(async_example())
```

### 2. 批量处理

```python
import asyncio
from industrytslib.utils.llm import OllamaClient, OllamaRequest

async def batch_process():
    client = OllamaClient()
    
    # 准备多个请求
    prompts = [
        "解释传感器数据预处理的重要性",
        "分析工业4.0中的数字化转型",
        "说明预测性维护的核心技术"
    ]
    
    # 并发处理
    tasks = []
    for prompt in prompts:
        request = OllamaRequest(
            model="qwen2.5:7b",
            prompt=prompt,
            stream=False
        )
        task = client.async_generate(request)
        tasks.append(task)
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks)
    
    for i, result in enumerate(results):
        print(f"结果 {i+1}: {result[:100]}...")

asyncio.run(batch_process())
```

### 3. 模型参数优化

```python
# 针对不同场景的参数配置

# 专业技术分析(低随机性)
technical_options = {
    "temperature": 0.1,  # 极低随机性
    "top_p": 0.7,
    "top_k": 20,
    "repeat_penalty": 1.1
}

# 创意性建议(中等随机性)
creative_options = {
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 40,
    "repeat_penalty": 1.0
}

# 快速响应(限制长度)
quick_options = {
    "temperature": 0.3,
    "num_predict": 100,  # 限制输出长度
    "top_p": 0.8
}

request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析这个异常数据模式",
    stream=False,
    options=technical_options  # 使用技术分析参数
)
```

## <a name="工业应用场景"></a>工业应用场景

### 1. 时间序列数据分析

```python
from industrytslib.utils.llm import OllamaClient, OllamaRequest
import json

def analyze_time_series_data(data_description, metrics):
    """分析时间序列数据质量和模型性能"""
    
    client = OllamaClient()
    
    prompt = f"""
基于以下工业时间序列数据信息,请提供专业分析:

数据描述:{data_description}

当前模型性能指标:
{json.dumps(metrics, indent=2, ensure_ascii=False)}

请分析:
1. 数据质量评估
2. 模型性能是否满足工业要求
3. 可能的改进建议
4. 推荐的下一步优化方向
"""
    
    request = OllamaRequest(
        model="qwen2.5:7b",
        prompt=prompt,
        stream=False,
        options={
            "temperature": 0.2,  # 专业分析需要低随机性
            "top_p": 0.8,
            "num_predict": 500
        }
    )
    
    return client.generate(request)

# 使用示例
data_desc = "化工反应器温度数据,采样频率1分钟,包含3个月历史数据"
metrics = {
    "RMSE": 2.34,
    "MAE": 1.87,
    "R2": 0.92,
    "MAPE": 3.2
}

analysis = analyze_time_series_data(data_desc, metrics)
print(analysis)
```

### 2. 异常诊断助手

```python
def diagnose_anomaly(anomaly_data, context):
    """工业异常诊断"""
    
    client = OllamaClient()
    
    system_prompt = """
你是一个专业的工业AI助手,专门协助工程师进行设备异常诊断。
你具备丰富的工业过程控制、设备维护和故障分析经验。
请用专业、准确、实用的方式提供诊断建议。
"""
    
    user_prompt = f"""
检测到工业过程异常,请协助诊断:

异常数据:{anomaly_data}
工艺背景:{context}

请提供:
1. 可能的异常原因分析
2. 风险等级评估(低/中/高)
3. 建议的应对措施
4. 预防性维护建议
5. 是否需要立即停机检查
"""
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    request = OllamaRequest(
        model="qwen2.5:7b",
        messages=messages,
        stream=True,  # 流式输出,实时显示诊断过程
        options={
            "temperature": 0.1,  # 诊断需要极高准确性
            "top_p": 0.7,
            "num_predict": 400
        }
    )
    
    print("🔄 正在分析异常...")
    response_generator = client.chat(request)
    full_response = ""
    
    for chunk in response_generator:
        print(chunk, end="", flush=True)
        full_response += chunk
    
    return full_response

# 使用示例
anomaly_data = "反应器温度突然上升15°C,压力传感器读数异常波动"
context = "聚合反应过程,正常操作温度180-200°C,当前第3批次生产"

diagnosis = diagnose_anomaly(anomaly_data, context)
```

### 3. 模型架构推荐

```python
def recommend_model_architecture(problem_type, data_characteristics):
    """推荐最适合的模型架构"""
    
    client = OllamaClient()
    
    prompt = f"""
针对以下工业AI任务,请推荐最适合的深度学习模型架构:

问题类型:{problem_type}
数据特征:{data_characteristics}

请提供:
1. 推荐的模型架构(如LSTM、Transformer、TimesNet、PatchTST等)
2. 模型配置建议(层数、隐藏单元数等)
3. 训练策略建议
4. 预期性能指标
5. 实施注意事项

请基于industrytslib库中可用的模型进行推荐。
"""
    
    request = OllamaRequest(
        model="qwen2.5:7b",
        prompt=prompt,
        stream=False,
        options={
            "temperature": 0.2,
            "top_p": 0.9,
            "num_predict": 600
        }
    )
    
    return client.generate(request)

# 使用示例
problem = "多变量时间序列预测,预测未来24小时的产品质量指标"
data_chars = "15个传感器变量,1分钟采样,包含季节性和趋势,存在缺失值"

recommendation = recommend_model_architecture(problem, data_chars)
print(recommendation)
```

## <a name="性能优化"></a>性能优化

### 1. 连接池管理

```python
from industrytslib.utils.llm import OllamaClient
import asyncio

class LLMManager:
    """LLM连接管理器"""
    
    def __init__(self, max_connections=5):
        self.clients = []
        self.max_connections = max_connections
        self._init_clients()
    
    def _init_clients(self):
        """初始化客户端池"""
        for _ in range(self.max_connections):
            client = OllamaClient()
            self.clients.append(client)
    
    async def process_batch(self, requests):
        """批量处理请求"""
        semaphore = asyncio.Semaphore(self.max_connections)
        
        async def process_single(request):
            async with semaphore:
                client = self.clients[0]  # 简化示例
                return await client.async_generate(request)
        
        tasks = [process_single(req) for req in requests]
        return await asyncio.gather(*tasks)
```

### 2. 缓存机制

```python
from functools import lru_cache
import hashlib

class CachedLLMClient:
    """带缓存的LLM客户端"""
    
    def __init__(self):
        self.client = OllamaClient()
        self.cache = {}
    
    def _get_cache_key(self, request):
        """生成缓存键"""
        content = f"{request.model}_{request.prompt}_{request.options}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def generate_with_cache(self, request):
        """带缓存的生成"""
        cache_key = self._get_cache_key(request)
        
        if cache_key in self.cache:
            print("🎯 缓存命中")
            return self.cache[cache_key]
        
        response = self.client.generate(request)
        self.cache[cache_key] = response
        return response
```

### 3. 错误重试机制

```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    print(f"⚠️ 第{attempt + 1}次尝试失败: {e}")
                    time.sleep(delay * (2 ** attempt))  # 指数退避
            return None
        return wrapper
    return decorator

class RobustLLMClient:
    """健壮的LLM客户端"""
    
    def __init__(self):
        self.client = OllamaClient()
    
    @retry_on_failure(max_retries=3, delay=2)
    def robust_generate(self, request):
        """带重试的生成"""
        return self.client.generate(request)
```

## <a name="故障排除"></a>故障排除

### 常见问题

#### 1. 连接失败

```python
# 检查连接状态
def check_connection(host="localhost", port=11434):
    """检查Ollama服务器连接"""
    try:
        config = OllamaConfig(host=host, port=port, timeout=10)
        client = OllamaClient(config)
        models = client.list_models()
        print(f"✅ 连接成功,可用模型: {len(models)} 个")
        return True
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("💡 请检查:")
        print("   1. Ollama服务是否运行")
        print("   2. 端口是否正确")
        print("   3. 网络连接是否正常")
        return False

# 使用
check_connection()
```

#### 2. 模型不存在

```python
def ensure_model_exists(model_name):
    """确保模型存在"""
    client = OllamaClient()
    models = client.list_models()
    
    if model_name not in models:
        print(f"⚠️ 模型 {model_name} 不存在")
        print("可用模型:")
        for model in models:
            print(f"  - {model}")
        return False
    return True
```

#### 3. 性能监控

```python
import time
from contextlib import contextmanager

@contextmanager
def performance_monitor():
    """性能监控上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏱️ 执行时间: {duration:.2f}秒")

# 使用示例
with performance_monitor():
    response = client.generate(request)
```

### 调试模式

```python
import logging
from loguru import logger

# 启用详细日志
logger.add("llm_debug.log", level="DEBUG")

# 调试客户端
class DebugLLMClient(OllamaClient):
    """调试版LLM客户端"""
    
    def generate(self, request):
        logger.debug(f"发送请求: {request.dict()}")
        start_time = time.time()
        
        try:
            response = super().generate(request)
            duration = time.time() - start_time
            logger.debug(f"请求成功,耗时: {duration:.2f}秒")
            return response
        except Exception as e:
            logger.error(f"请求失败: {e}")
            raise
```

## <a name="api参考"></a>API参考

### 核心类

#### OllamaConfig

```python
class OllamaConfig(BaseModel):
    host: str = "localhost"          # 服务器地址
    port: int = 11434               # 端口
    timeout: int = 30               # 超时时间(秒)
```

#### OllamaRequest

```python
class OllamaRequest(BaseModel):
    model: str                      # 模型名称
    prompt: Optional[str] = None    # 提示词(generate API)
    messages: Optional[list] = None # 消息列表(chat API)
    stream: bool = True             # 是否流式输出
    options: Optional[Dict] = None  # 模型参数
```

#### OllamaClient

```python
class OllamaClient:
    def __init__(self, config: Optional[OllamaConfig] = None)
    def generate(self, request: OllamaRequest) -> Union[str, Generator]
    def chat(self, request: OllamaRequest) -> Union[str, Generator]
    def list_models(self) -> List[str]
    async def async_generate(self, request: OllamaRequest)
    async def async_chat(self, request: OllamaRequest)
```

### 便捷函数

```python
# 快速生成
def quick_generate(
    prompt: str,
    model: str = "qwen2.5:7b",
    stream: bool = False,
    **options
) -> Union[str, Generator]

# 异步快速生成
async def async_quick_generate(
    prompt: str,
    model: str = "qwen2.5:7b",
    stream: bool = False,
    **options
) -> Union[str, AsyncGenerator]

# 创建客户端
def create_ollama_client(
    host: str = "localhost",
    port: int = 11434,
    timeout: int = 30
) -> OllamaClient
```

### 模型参数选项

```python
options = {
    "temperature": 0.7,      # 随机性控制 (0.0-1.0)
    "top_p": 0.9,           # 核采样 (0.0-1.0)
    "top_k": 40,            # Top-K采样
    "num_predict": 200,     # 最大生成token数
    "repeat_penalty": 1.1,  # 重复惩罚
    "stop": ["\n", "。"],    # 停止词
}
```

## 最佳实践

### 1. 工业场景提示词设计

```python
# 专业系统提示词
INDUSTRIAL_SYSTEM_PROMPT = """
你是一个专业的工业AI助手,专门协助工程师进行时间序列预测、过程优化和异常检测。
你具备以下专业知识:
- 工业过程控制和优化
- 时间序列预测模型(LSTM、Transformer、TimesNet等)
- 异常检测和故障诊断
- 数据预处理和特征工程
- 工业数据分析和可视化

请用专业、准确、实用的方式回答问题,并提供具体的技术建议。
"""

# 结构化提示词模板
def create_analysis_prompt(data_type, problem, context):
    return f"""
## 分析任务
数据类型:{data_type}
问题描述:{problem}
业务背景:{context}

## 请提供
1. 问题分析
2. 技术方案
3. 实施建议
4. 风险评估
"""
```

### 2. 错误处理策略

```python
class IndustrialLLMClient:
    """工业级LLM客户端"""
    
    def __init__(self):
        self.client = OllamaClient()
        self.fallback_responses = {
            "connection_error": "连接服务器失败,请检查网络连接",
            "model_error": "模型不可用,请检查模型配置",
            "timeout_error": "请求超时,请稍后重试"
        }
    
    def safe_generate(self, request):
        """安全的生成方法"""
        try:
            return self.client.generate(request)
        except ConnectionError:
            return self.fallback_responses["connection_error"]
        except TimeoutError:
            return self.fallback_responses["timeout_error"]
        except Exception as e:
            logger.error(f"LLM生成失败: {e}")
            return f"生成失败: {str(e)}"
```

### 3. 生产环境配置

```python
# 生产环境配置
PRODUCTION_CONFIG = {
    "timeout": 60,           # 较长超时时间
    "max_retries": 3,       # 重试次数
    "cache_enabled": True,   # 启用缓存
    "log_level": "INFO",     # 日志级别
    "model_options": {
        "temperature": 0.2,   # 生产环境使用低随机性
        "top_p": 0.8,
        "num_predict": 300
    }
}

def create_production_client():
    """创建生产环境客户端"""
    config = OllamaConfig(
        timeout=PRODUCTION_CONFIG["timeout"]
    )
    return OllamaClient(config)
```

---

## 📚 更多示例

查看 `examples/experiment/exp_llm/` 目录下的完整示例:

- **`quick_test_remote.py`** - SiliconFlow快速测试和连接验证
- **`test_ollama_client.py`** - Ollama客户端完整功能测试
- **`test_siliconflow_client.py`** - SiliconFlow客户端高级功能
- **`test_universal_client.py`** - 通用客户端运行时切换示例
- **`industrial_scenarios.py`** - 工业场景应用示例
- **`performance_optimization.py`** - 性能优化最佳实践

## 🎯 下一步学习

### 📖 深入学习
1. **[通用LLM客户端指南](universal-client-guide.md)** - 详细的使用指南和最佳实践
2. **[API参考文档](api-reference.md)** - 完整的API说明和参数详解
3. **[工业应用场景](industrial-applications.md)** - 更多实际应用案例和解决方案

### ⚡ 性能优化
4. **[性能优化指南](performance-optimization.md)** - 提升系统性能的技巧和策略
5. **[故障排除指南](troubleshooting.md)** - 常见问题的解决方案

### 🔧 实践项目
6. **构建智能监控系统** - 结合时间序列预测和LLM分析
7. **开发异常诊断助手** - 自动化故障检测和解决方案推荐
8. **创建工艺优化顾问** - 基于数据的工艺参数优化建议

## 💡 技术支持

- **GitHub Issues**: [提交问题和建议](https://github.com/your-repo/industrytslib/issues)
- **文档反馈**: 如果发现文档问题,欢迎提交PR
- **社区讨论**: 加入我们的技术交流群

---

## 总结

industrytslib的LLM模块为工业时间序列AI应用提供了强大而灵活的大语言模型集成能力。通过本教程,你应该能够:

- ✅ 配置和使用Ollama客户端
- ✅ 实现基础的文本生成和聊天功能
- ✅ 应用于实际的工业场景
- ✅ 优化性能和处理错误
- ✅ 遵循最佳实践

**industrytslib LLM模块** 为工业AI应用提供了强大而灵活的大语言模型集成能力。通过统一的接口设计和丰富的功能特性,它能够帮助您快速构建智能化的工业解决方案。

立即开始您的工业AI之旅！🚀

如有问题或建议,请查看项目文档或提交Issue。