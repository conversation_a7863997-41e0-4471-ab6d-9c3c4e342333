# 工业应用场景指南

本指南详细介绍LLM在工业时间序列AI中的具体应用场景和实现方法。

## 📋 目录

- [时间序列数据分析](#时间序列数据分析)
- [模型架构推荐](#模型架构推荐)
- [异常检测与诊断](#异常检测与诊断)
- [工艺参数优化](#工艺参数优化)
- [预测性维护](#预测性维护)
- [质量控制分析](#质量控制分析)
- [决策支持系统](#决策支持系统)
- [数据预处理指导](#数据预处理指导)

## <a name="时间序列数据分析"></a>时间序列数据分析

### 应用场景

- 数据质量评估
- 趋势分析和模式识别
- 季节性和周期性检测
- 数据异常识别
- 特征重要性分析

### 实现示例

#### 数据质量评估

```python
from industrytslib.utils.llm import OllamaClient, OllamaRequest
import json

class TimeSeriesAnalyzer:
    """时间序列数据分析器"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def assess_data_quality(self, data_info: dict):
        """评估数据质量"""
        prompt = f"""
        作为工业数据分析专家,请评估以下时间序列数据的质量:
        
        数据信息:
        {json.dumps(data_info, indent=2, ensure_ascii=False)}
        
        请从以下维度进行评估:
        1. 数据完整性(缺失值比例、连续性)
        2. 数据一致性(异常值、噪声水平)
        3. 数据准确性(传感器漂移、校准问题)
        4. 数据时效性(采样频率是否合适)
        5. 数据代表性(是否覆盖各种工况)
        
        请给出具体的改进建议和风险评估。
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={
                "temperature": 0.1,  # 专业分析需要低随机性
                "top_p": 0.8,
                "num_predict": 500
            }
        )
        
        return self.client.generate(request)
    
    def analyze_patterns(self, data_description: str, statistical_summary: dict):
        """分析数据模式"""
        prompt = f"""
        请分析以下工业时间序列数据的模式特征:
        
        数据描述:{data_description}
        
        统计摘要:
        {json.dumps(statistical_summary, indent=2, ensure_ascii=False)}
        
        请识别和分析:
        1. 趋势特征(上升、下降、平稳)
        2. 季节性模式(日、周、月、年周期)
        3. 周期性变化(设备运行周期、生产批次)
        4. 随机性特征(白噪声、有色噪声)
        5. 非线性特征(突变点、结构性变化)
        
        请推荐适合的预测模型类型。
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 600}
        )
        
        return self.client.generate(request)

# 使用示例
analyzer = TimeSeriesAnalyzer()

# 数据质量评估
data_info = {
    "数据源": "化工反应器温度传感器",
    "采样频率": "1分钟",
    "数据时长": "6个月",
    "变量数量": 15,
    "缺失值比例": "2.3%",
    "异常值比例": "0.8%",
    "数据范围": "180-220°C",
    "传感器精度": "±0.5°C"
}

quality_assessment = analyzer.assess_data_quality(data_info)
print("数据质量评估结果:")
print(quality_assessment)

# 模式分析
statistical_summary = {
    "均值": 195.6,
    "标准差": 8.2,
    "最小值": 178.3,
    "最大值": 218.7,
    "偏度": 0.15,
    "峰度": -0.23,
    "自相关系数": [0.95, 0.89, 0.82, 0.75],
    "频域主要频率": ["24小时", "8小时", "2小时"]
}

pattern_analysis = analyzer.analyze_patterns(
    "化工反应器温度数据,连续生产过程",
    statistical_summary
)
print("\n模式分析结果:")
print(pattern_analysis)
```

#### 特征重要性分析

```python
def analyze_feature_importance(self, features_info: list, target_variable: str):
    """分析特征重要性"""
    features_text = "\n".join([f"- {f['name']}: {f['description']}" for f in features_info])
    
    prompt = f"""
    作为工业数据科学专家,请分析以下特征对目标变量的重要性:
    
    目标变量:{target_variable}
    
    可用特征:
    {features_text}
    
    请从以下角度分析:
    1. 物理相关性(基于工艺机理的相关性)
    2. 统计相关性(可能的数学关系)
    3. 时间延迟效应(滞后影响)
    4. 交互效应(特征间的协同作用)
    5. 噪声敏感性(特征的稳定性)
    
    请按重要性排序并给出特征工程建议。
    """
    
    request = OllamaRequest(
        model=self.model,
        prompt=prompt,
        stream=False,
        options={"temperature": 0.15, "num_predict": 400}
    )
    
    return self.client.generate(request)

# 使用示例
features_info = [
    {"name": "反应器温度", "description": "主反应器内部温度,°C"},
    {"name": "进料流量", "description": "原料进料流量,L/min"},
    {"name": "催化剂浓度", "description": "催化剂浓度,mol/L"},
    {"name": "反应压力", "description": "反应器内压力,bar"},
    {"name": "冷却水温度", "description": "冷却水入口温度,°C"},
    {"name": "搅拌速度", "description": "反应器搅拌速度,rpm"}
]

feature_analysis = analyzer.analyze_feature_importance(
    features_info,
    "产品收率"
)
print("特征重要性分析:")
print(feature_analysis)
```

## <a name="模型架构推荐"></a>模型架构推荐

### 应用场景

- 根据数据特征选择合适的模型
- 模型超参数配置建议
- 模型组合策略
- 性能优化建议

### 实现示例

```python
class ModelRecommendationSystem:
    """模型推荐系统"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
        self.system_prompt = """
        你是一个专业的工业AI模型架构专家,熟悉各种时间序列预测模型:
        - 传统模型:ARIMA、指数平滑、状态空间模型
        - 机器学习:随机森林、XGBoost、SVM
        - 深度学习:LSTM、GRU、Transformer、TimesNet、PatchTST、Mamba
        - 混合模型:CNN-LSTM、Attention-LSTM、Informer
        
        请基于industrytslib库中可用的模型进行推荐。
        """
    
    def recommend_architecture(self, problem_spec: dict):
        """推荐模型架构"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {
                "role": "user",
                "content": f"""
                请为以下工业AI任务推荐最适合的模型架构:
                
                任务规格:
                {json.dumps(problem_spec, indent=2, ensure_ascii=False)}
                
                请提供:
                1. 推荐的主要模型(排序前3个)
                2. 模型配置建议(层数、隐藏单元、注意力头等)
                3. 训练策略(学习率、批大小、正则化)
                4. 预期性能指标范围
                5. 实施难度和计算资源需求
                6. 模型可解释性评估
                """
            }
        ]
        
        request = OllamaRequest(
            model=self.model,
            messages=messages,
            stream=False,
            options={"temperature": 0.2, "num_predict": 700}
        )
        
        return self.client.chat(request)
    
    def optimize_hyperparameters(self, model_type: str, data_characteristics: dict):
        """超参数优化建议"""
        prompt = f"""
        针对{model_type}模型和以下数据特征,请提供超参数优化建议:
        
        数据特征:
        {json.dumps(data_characteristics, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 关键超参数的推荐范围
        2. 超参数搜索策略(网格搜索、贝叶斯优化等)
        3. 交叉验证策略
        4. 早停策略
        5. 学习率调度方案
        6. 正则化技术选择
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.15, "num_predict": 500}
        )
        
        return self.client.generate(request)

# 使用示例
recommender = ModelRecommendationSystem()

# 模型架构推荐
problem_spec = {
    "任务类型": "多变量时间序列预测",
    "预测目标": "未来24小时的产品质量指标",
    "输入变量数量": 15,
    "历史窗口长度": "7天",
    "预测窗口长度": "24小时",
    "采样频率": "5分钟",
    "数据特征": {
        "季节性": "强日周期性",
        "趋势性": "弱上升趋势",
        "非线性": "中等非线性",
        "噪声水平": "低",
        "缺失值": "<1%"
    },
    "性能要求": {
        "准确性": "MAPE < 5%",
        "实时性": "推理时间 < 100ms",
        "稳定性": "高"
    },
    "资源限制": {
        "GPU内存": "8GB",
        "训练时间": "< 4小时",
        "模型大小": "< 100MB"
    }
}

architecture_recommendation = recommender.recommend_architecture(problem_spec)
print("模型架构推荐:")
print(architecture_recommendation)

# 超参数优化建议
data_characteristics = {
    "序列长度": 2016,  # 7天 * 24小时 * 12个5分钟
    "变量数量": 15,
    "目标变量数量": 3,
    "数据分布": "近似正态分布",
    "相关性": "变量间中等相关",
    "平稳性": "经差分后平稳",
    "异方差性": "轻微异方差"
}

hyperparameter_advice = recommender.optimize_hyperparameters(
    "TimesNet",
    data_characteristics
)
print("\n超参数优化建议:")
print(hyperparameter_advice)
```

## <a name="异常检测与诊断"></a>异常检测与诊断

### 应用场景

- 实时异常检测
- 异常根因分析
- 故障模式识别
- 维护建议生成

### 实现示例

```python
class AnomalyDiagnosisSystem:
    """异常诊断系统"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def diagnose_anomaly(self, anomaly_data: dict, context: dict):
        """诊断异常"""
        prompt = f"""
        作为工业设备诊断专家,请分析以下异常情况:
        
        异常数据:
        {json.dumps(anomaly_data, indent=2, ensure_ascii=False)}
        
        工艺背景:
        {json.dumps(context, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 异常类型识别(传感器故障、设备异常、工艺偏差等)
        2. 可能的根本原因分析(按概率排序)
        3. 风险等级评估(低/中/高/紧急)
        4. 即时应对措施
        5. 长期预防措施
        6. 是否需要立即停机检查
        7. 预计影响范围和持续时间
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=True,  # 流式输出,实时显示诊断过程
            options={
                "temperature": 0.1,  # 诊断需要极高准确性
                "top_p": 0.7,
                "num_predict": 600
            }
        )
        
        print("🔍 正在分析异常...")
        response_generator = self.client.generate(request)
        full_response = ""
        
        for chunk in response_generator:
            print(chunk, end="", flush=True)
            full_response += chunk
        
        return full_response
    
    def predict_failure_mode(self, sensor_trends: dict, maintenance_history: list):
        """预测故障模式"""
        maintenance_text = "\n".join([
            f"- {record['date']}: {record['action']} ({record['component']})"
            for record in maintenance_history
        ])
        
        prompt = f"""
        基于传感器趋势和维护历史,预测可能的故障模式:
        
        传感器趋势:
        {json.dumps(sensor_trends, indent=2, ensure_ascii=False)}
        
        维护历史:
        {maintenance_text}
        
        请分析:
        1. 最可能的故障模式(前3个)
        2. 预计故障时间窗口
        3. 故障严重程度评估
        4. 预防性维护建议
        5. 关键监控指标
        6. 备件准备建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 500}
        )
        
        return self.client.generate(request)

# 使用示例
diagnosis_system = AnomalyDiagnosisSystem()

# 异常诊断
anomaly_data = {
    "异常时间": "2024-01-15 14:30:00",
    "异常变量": {
        "反应器温度": {
            "正常范围": "180-200°C",
            "当前值": "215°C",
            "变化趋势": "急剧上升",
            "持续时间": "15分钟"
        },
        "压力": {
            "正常范围": "2.0-2.5 bar",
            "当前值": "2.8 bar",
            "变化趋势": "缓慢上升",
            "持续时间": "30分钟"
        },
        "冷却水流量": {
            "正常范围": "50-60 L/min",
            "当前值": "45 L/min",
            "变化趋势": "下降",
            "持续时间": "20分钟"
        }
    },
    "报警级别": "高",
    "相关事件": "冷却系统维护后重启"
}

context = {
    "设备类型": "连续搅拌反应器",
    "工艺过程": "聚合反应",
    "正常操作温度": "185-195°C",
    "当前生产批次": "第3批次",
    "操作班次": "白班",
    "环境温度": "25°C",
    "最近维护": "冷却系统清洗(2小时前)"
}

diagnosis_result = diagnosis_system.diagnose_anomaly(anomaly_data, context)
print("\n\n异常诊断完成。")

# 故障模式预测
sensor_trends = {
    "轴承温度": {
        "7天趋势": "逐渐上升",
        "当前值": "65°C",
        "正常范围": "40-55°C",
        "变化率": "+2°C/天"
    },
    "振动幅度": {
        "7天趋势": "波动增大",
        "当前值": "4.2 mm/s",
        "正常范围": "1.5-3.0 mm/s",
        "变化率": "+0.3 mm/s/天"
    },
    "润滑油压力": {
        "7天趋势": "缓慢下降",
        "当前值": "1.8 bar",
        "正常范围": "2.0-2.5 bar",
        "变化率": "-0.05 bar/天"
    }
}

maintenance_history = [
    {"date": "2024-01-01", "action": "更换轴承", "component": "主轴承"},
    {"date": "2023-12-15", "action": "润滑油更换", "component": "润滑系统"},
    {"date": "2023-12-01", "action": "振动检测", "component": "转子"}
]

failure_prediction = diagnosis_system.predict_failure_mode(
    sensor_trends,
    maintenance_history
)
print("\n故障模式预测:")
print(failure_prediction)
```

## <a name="工艺参数优化"></a>工艺参数优化

### 应用场景

- 操作参数优化建议
- 工艺条件调整
- 能耗优化分析
- 产品质量改进

### 实现示例

```python
class ProcessOptimizationAdvisor:
    """工艺优化顾问"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def optimize_parameters(self, current_conditions: dict, objectives: dict, constraints: dict):
        """优化工艺参数"""
        prompt = f"""
        作为工艺优化专家,请为以下工业过程提供参数优化建议:
        
        当前工艺条件:
        {json.dumps(current_conditions, indent=2, ensure_ascii=False)}
        
        优化目标:
        {json.dumps(objectives, indent=2, ensure_ascii=False)}
        
        约束条件:
        {json.dumps(constraints, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 关键参数调整建议(具体数值范围)
        2. 参数调整的优先级排序
        3. 预期的性能改进幅度
        4. 调整过程中的风险评估
        5. 分步实施方案
        6. 监控指标和判断标准
        7. 回退方案
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 600}
        )
        
        return self.client.generate(request)
    
    def analyze_energy_efficiency(self, energy_data: dict, production_data: dict):
        """分析能耗效率"""
        prompt = f"""
        请分析以下工业过程的能耗效率并提供优化建议:
        
        能耗数据:
        {json.dumps(energy_data, indent=2, ensure_ascii=False)}
        
        生产数据:
        {json.dumps(production_data, indent=2, ensure_ascii=False)}
        
        请分析:
        1. 能耗效率评估(与行业标准对比)
        2. 主要能耗环节识别
        3. 节能潜力分析
        4. 具体节能措施建议
        5. 投资回报期估算
        6. 环保效益评估
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.15, "num_predict": 500}
        )
        
        return self.client.generate(request)

# 使用示例
optimizer = ProcessOptimizationAdvisor()

# 工艺参数优化
current_conditions = {
    "反应温度": "190°C",
    "反应压力": "2.2 bar",
    "催化剂浓度": "0.15 mol/L",
    "进料流量": "55 L/min",
    "搅拌速度": "300 rpm",
    "反应时间": "4.5小时",
    "当前收率": "85%",
    "当前纯度": "92%",
    "能耗": "150 kWh/批次"
}

objectives = {
    "主要目标": "提高产品收率",
    "目标收率": "≥90%",
    "次要目标": [
        "保持产品纯度≥90%",
        "降低能耗10%",
        "缩短反应时间"
    ],
    "权重分配": {
        "收率": 0.5,
        "纯度": 0.3,
        "能耗": 0.2
    }
}

constraints = {
    "安全约束": {
        "最高温度": "200°C",
        "最高压力": "3.0 bar",
        "最低温度": "180°C"
    },
    "设备约束": {
        "最大搅拌速度": "500 rpm",
        "最大进料流量": "80 L/min",
        "反应器容量": "1000 L"
    },
    "经济约束": {
        "催化剂成本限制": "不超过当前成本20%",
        "能耗限制": "不超过当前能耗",
        "改造投资": "<50万元"
    }
}

optimization_advice = optimizer.optimize_parameters(
    current_conditions,
    objectives,
    constraints
)
print("工艺参数优化建议:")
print(optimization_advice)

# 能耗效率分析
energy_data = {
    "总能耗": "150 kWh/批次",
    "能耗分布": {
        "加热": "60 kWh (40%)",
        "搅拌": "30 kWh (20%)",
        "冷却": "25 kWh (17%)",
        "泵送": "20 kWh (13%)",
        "其他": "15 kWh (10%)"
    },
    "能耗趋势": "过去3个月平均上升5%",
    "峰值功率": "45 kW",
    "功率因数": "0.85"
}

production_data = {
    "产量": "800 kg/批次",
    "批次周期": "6小时",
    "日产量": "3200 kg",
    "设备利用率": "85%",
    "产品价值": "50元/kg",
    "电价": "0.8元/kWh"
}

energy_analysis = optimizer.analyze_energy_efficiency(
    energy_data,
    production_data
)
print("\n能耗效率分析:")
print(energy_analysis)
```

## <a name="预测性维护"></a>预测性维护

### 应用场景

- 设备健康状态评估
- 维护计划优化
- 故障预测和预警
- 备件需求预测

### 实现示例

```python
class PredictiveMaintenanceAdvisor:
    """预测性维护顾问"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def assess_equipment_health(self, sensor_data: dict, maintenance_history: list, operating_conditions: dict):
        """评估设备健康状态"""
        maintenance_text = "\n".join([
            f"- {record['date']}: {record['type']} - {record['description']}"
            for record in maintenance_history[-10:]  # 最近10次维护记录
        ])
        
        prompt = f"""
        作为设备维护专家,请评估以下设备的健康状态:
        
        传感器数据:
        {json.dumps(sensor_data, indent=2, ensure_ascii=False)}
        
        运行条件:
        {json.dumps(operating_conditions, indent=2, ensure_ascii=False)}
        
        最近维护记录:
        {maintenance_text}
        
        请提供:
        1. 设备整体健康评分(0-100分)
        2. 各子系统健康状态评估
        3. 关键风险点识别
        4. 剩余使用寿命预估
        5. 维护紧急程度评级
        6. 推荐的监控频率
        7. 预防性维护建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.1, "num_predict": 600}
        )
        
        return self.client.generate(request)
    
    def optimize_maintenance_schedule(self, equipment_list: list, resource_constraints: dict, business_requirements: dict):
        """优化维护计划"""
        equipment_text = "\n".join([
            f"- {eq['name']}: 健康评分{eq['health_score']}, 上次维护{eq['last_maintenance']}, 重要性{eq['criticality']}"
            for eq in equipment_list
        ])
        
        prompt = f"""
        请为以下设备制定优化的维护计划:
        
        设备清单:
        {equipment_text}
        
        资源约束:
        {json.dumps(resource_constraints, indent=2, ensure_ascii=False)}
        
        业务要求:
        {json.dumps(business_requirements, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 维护优先级排序
        2. 建议的维护时间窗口
        3. 资源分配方案
        4. 维护成本估算
        5. 风险缓解措施
        6. 应急预案
        7. 维护效果评估指标
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 700}
        )
        
        return self.client.generate(request)

# 使用示例
maintenance_advisor = PredictiveMaintenanceAdvisor()

# 设备健康状态评估
sensor_data = {
    "轴承温度": {
        "当前值": "68°C",
        "正常范围": "40-55°C",
        "7天趋势": "持续上升",
        "报警阈值": "70°C"
    },
    "振动烈度": {
        "当前值": "4.5 mm/s",
        "正常范围": "1.5-3.0 mm/s",
        "频谱分析": "轴承特征频率异常",
        "报警阈值": "5.0 mm/s"
    },
    "润滑油质量": {
        "粘度": "偏高15%",
        "水分含量": "0.08%",
        "金属颗粒": "铁含量超标",
        "酸值": "正常"
    },
    "电机电流": {
        "当前值": "52A",
        "正常范围": "45-50A",
        "功率因数": "0.82",
        "谐波含量": "3.2%"
    }
}

operating_conditions = {
    "运行时间": "连续运行72小时",
    "负载率": "85%",
    "环境温度": "35°C",
    "湿度": "65%",
    "工作制度": "三班连续",
    "累计运行时间": "15600小时"
}

maintenance_history = [
    {"date": "2024-01-01", "type": "预防性维护", "description": "更换轴承润滑脂"},
    {"date": "2023-12-15", "type": "故障维修", "description": "更换损坏的密封件"},
    {"date": "2023-12-01", "type": "定期检查", "description": "振动检测和油液分析"},
    {"date": "2023-11-15", "type": "预防性维护", "description": "清洁冷却系统"},
    {"date": "2023-11-01", "type": "校准", "description": "传感器校准和标定"}
]

health_assessment = maintenance_advisor.assess_equipment_health(
    sensor_data,
    maintenance_history,
    operating_conditions
)
print("设备健康状态评估:")
print(health_assessment)

# 维护计划优化
equipment_list = [
    {
        "name": "主反应器搅拌器",
        "health_score": 65,
        "last_maintenance": "2024-01-01",
        "criticality": "高"
    },
    {
        "name": "进料泵A",
        "health_score": 80,
        "last_maintenance": "2023-12-15",
        "criticality": "中"
    },
    {
        "name": "冷却塔风机",
        "health_score": 45,
        "last_maintenance": "2023-11-20",
        "criticality": "高"
    },
    {
        "name": "压缩机",
        "health_score": 75,
        "last_maintenance": "2024-01-10",
        "criticality": "高"
    }
]

resource_constraints = {
    "维护人员": "3人",
    "维护窗口": "每周末8小时",
    "备件库存": "充足",
    "维护预算": "月度50万元",
    "外包资源": "可调用专业团队"
}

business_requirements = {
    "生产连续性": "关键设备停机时间<4小时",
    "安全要求": "零安全事故",
    "质量要求": "产品质量不受影响",
    "成本控制": "维护成本控制在预算内",
    "合规要求": "符合环保和安全法规"
}

maintenance_schedule = maintenance_advisor.optimize_maintenance_schedule(
    equipment_list,
    resource_constraints,
    business_requirements
)
print("\n维护计划优化:")
print(maintenance_schedule)
```

## <a name="质量控制分析"></a>质量控制分析

### 应用场景

- 产品质量趋势分析
- 质量异常根因分析
- 工艺参数与质量关联分析
- 质量改进建议

### 实现示例

```python
class QualityControlAnalyzer:
    """质量控制分析器"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def analyze_quality_trends(self, quality_data: dict, process_data: dict):
        """分析质量趋势"""
        prompt = f"""
        作为质量控制专家,请分析以下产品质量趋势:
        
        质量数据:
        {json.dumps(quality_data, indent=2, ensure_ascii=False)}
        
        工艺数据:
        {json.dumps(process_data, indent=2, ensure_ascii=False)}
        
        请分析:
        1. 质量趋势评估(改善/恶化/稳定)
        2. 关键质量指标变化分析
        3. 工艺参数与质量的关联性
        4. 质量波动的主要原因
        5. 质量改进的潜在机会
        6. 预警指标和阈值建议
        7. 质量控制策略优化建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.15, "num_predict": 600}
        )
        
        return self.client.generate(request)
    
    def investigate_quality_issue(self, issue_description: dict, investigation_data: dict):
        """质量问题调查"""
        prompt = f"""
        请协助调查以下质量问题:
        
        问题描述:
        {json.dumps(issue_description, indent=2, ensure_ascii=False)}
        
        调查数据:
        {json.dumps(investigation_data, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 问题严重程度评估
        2. 可能的根本原因分析(鱼骨图思路)
        3. 影响范围评估
        4. 即时纠正措施
        5. 长期预防措施
        6. 质量体系改进建议
        7. 客户沟通策略
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.1, "num_predict": 700}
        )
        
        return self.client.generate(request)

# 使用示例
quality_analyzer = QualityControlAnalyzer()

# 质量趋势分析
quality_data = {
    "产品纯度": {
        "当前批次": "91.5%",
        "目标值": "≥92%",
        "30天平均": "91.8%",
        "趋势": "轻微下降",
        "标准差": "0.8%"
    },
    "收率": {
        "当前批次": "87%",
        "目标值": "≥85%",
        "30天平均": "86.5%",
        "趋势": "稳定",
        "标准差": "1.2%"
    },
    "杂质含量": {
        "A类杂质": "0.3%",
        "B类杂质": "0.15%",
        "总杂质": "0.45%",
        "限度": "<0.5%",
        "趋势": "略有上升"
    },
    "物理性质": {
        "熔点": "156.2°C (标准: 155-158°C)",
        "密度": "1.245 g/cm³ (标准: 1.240-1.250)",
        "粒度分布": "D50=125μm (标准: 120-130μm)"
    }
}

process_data = {
    "反应条件": {
        "温度": "192°C (目标: 190±3°C)",
        "压力": "2.3 bar (目标: 2.2±0.2 bar)",
        "反应时间": "4.2小时 (目标: 4.0±0.5小时)"
    },
    "原料质量": {
        "原料A纯度": "99.2%",
        "原料B水分": "0.05%",
        "催化剂活性": "95%"
    },
    "设备状态": {
        "搅拌效率": "正常",
        "传热效率": "轻微下降",
        "密封性能": "良好"
    }
}

quality_trend_analysis = quality_analyzer.analyze_quality_trends(
    quality_data,
    process_data
)
print("质量趋势分析:")
print(quality_trend_analysis)

# 质量问题调查
issue_description = {
    "问题类型": "产品纯度不合格",
    "发现时间": "2024-01-15 16:00",
    "影响批次": "B240115-03",
    "问题严重程度": "中等",
    "客户投诉": "否",
    "检测结果": {
        "纯度": "89.5% (标准: ≥92%)",
        "主要杂质": "未知杂质峰 2.1%",
        "外观": "颜色偏黄"
    }
}

investigation_data = {
    "生产记录": {
        "操作员": "张三 (经验丰富)",
        "班次": "夜班",
        "设备状态": "正常运行",
        "原料批次": "A240110-05, B240112-02"
    },
    "工艺偏差": {
        "温度偏差": "反应后期温度超标5°C, 持续30分钟",
        "时间偏差": "反应时间延长0.5小时",
        "其他": "搅拌速度在第3小时降低至250rpm"
    },
    "原料检验": {
        "原料A": "合格 (纯度99.1%)",
        "原料B": "合格 (水分0.04%)",
        "催化剂": "合格 (活性96%)"
    },
    "设备检查": {
        "反应器": "清洁度良好",
        "搅拌器": "发现轻微磨损",
        "温控系统": "PID参数需要调整"
    }
}

quality_investigation = quality_analyzer.investigate_quality_issue(
    issue_description,
    investigation_data
)
print("\n质量问题调查:")
print(quality_investigation)
```

## <a name="决策支持系统"></a>决策支持系统

### 应用场景

- 生产计划优化
- 投资决策分析
- 风险评估和管理
- 技术路线选择

### 实现示例

```python
class DecisionSupportSystem:
    """决策支持系统"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def analyze_investment_decision(self, investment_proposal: dict, financial_data: dict, risk_factors: dict):
        """投资决策分析"""
        prompt = f"""
        作为工业投资分析专家,请评估以下投资提案:
        
        投资提案:
        {json.dumps(investment_proposal, indent=2, ensure_ascii=False)}
        
        财务数据:
        {json.dumps(financial_data, indent=2, ensure_ascii=False)}
        
        风险因素:
        {json.dumps(risk_factors, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 投资可行性评估
        2. 财务指标分析(NPV、IRR、回收期)
        3. 风险评估和缓解措施
        4. 敏感性分析
        5. 投资建议(推荐/谨慎/不推荐)
        6. 实施时间表建议
        7. 关键成功因素
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.1, "num_predict": 700}
        )
        
        return self.client.generate(request)
    
    def optimize_production_plan(self, demand_forecast: dict, capacity_constraints: dict, cost_structure: dict):
        """生产计划优化"""
        prompt = f"""
        请为以下生产系统制定优化的生产计划:
        
        需求预测:
        {json.dumps(demand_forecast, indent=2, ensure_ascii=False)}
        
        产能约束:
        {json.dumps(capacity_constraints, indent=2, ensure_ascii=False)}
        
        成本结构:
        {json.dumps(cost_structure, indent=2, ensure_ascii=False)}
        
        请提供:
        1. 最优生产计划方案
        2. 产能利用率分析
        3. 成本效益分析
        4. 瓶颈识别和解决方案
        5. 库存管理策略
        6. 应急预案
        7. 持续改进建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 600}
        )
        
        return self.client.generate(request)

# 使用示例
decision_support = DecisionSupportSystem()

# 投资决策分析
investment_proposal = {
    "项目名称": "智能化生产线升级",
    "投资总额": "2000万元",
    "项目周期": "18个月",
    "主要内容": [
        "自动化设备升级",
        "数字化控制系统",
        "AI预测系统部署",
        "人员培训"
    ],
    "预期效益": {
        "产能提升": "30%",
        "质量改善": "缺陷率降低50%",
        "能耗降低": "15%",
        "人工成本节约": "年节约300万元"
    }
}

financial_data = {
    "当前年收入": "8000万元",
    "当前年利润": "1200万元",
    "现金流": "充足",
    "负债率": "35%",
    "资本成本": "8%",
    "税率": "25%",
    "折旧年限": "10年"
}

risk_factors = {
    "技术风险": "新技术成熟度有待验证",
    "市场风险": "产品需求可能波动",
    "财务风险": "投资回收期较长",
    "运营风险": "员工适应新系统需要时间",
    "外部风险": "政策变化、原材料价格波动"
}

investment_analysis = decision_support.analyze_investment_decision(
    investment_proposal,
    financial_data,
    risk_factors
)
print("投资决策分析:")
print(investment_analysis)

# 生产计划优化
demand_forecast = {
    "产品A": {
        "Q1需求": "1000吨",
        "Q2需求": "1200吨",
        "Q3需求": "1100吨",
        "Q4需求": "1300吨",
        "价格趋势": "稳中有升"
    },
    "产品B": {
        "Q1需求": "800吨",
        "Q2需求": "900吨",
        "Q3需求": "850吨",
        "Q4需求": "950吨",
        "价格趋势": "基本稳定"
    }
}

capacity_constraints = {
    "生产线1": {
        "最大产能": "600吨/季度",
        "适用产品": "产品A、产品B",
        "切换成本": "10万元/次",
        "维护窗口": "每季度1周"
    },
    "生产线2": {
        "最大产能": "400吨/季度",
        "适用产品": "仅产品A",
        "切换成本": "无",
        "维护窗口": "每半年1周"
    },
    "原料供应": {
        "原料X": "充足供应",
        "原料Y": "季度限额2000吨",
        "原料Z": "价格波动较大"
    }
}

cost_structure = {
    "产品A": {
        "原料成本": "3000元/吨",
        "加工成本": "1500元/吨",
        "销售价格": "6000元/吨",
        "毛利率": "25%"
    },
    "产品B": {
        "原料成本": "2500元/吨",
        "加工成本": "1200元/吨",
        "销售价格": "5200元/吨",
        "毛利率": "29%"
    },
    "固定成本": {
        "人工成本": "200万元/季度",
        "设备折旧": "150万元/季度",
        "管理费用": "100万元/季度"
    }
}

production_optimization = decision_support.optimize_production_plan(
    demand_forecast,
    capacity_constraints,
    cost_structure
)
print("\n生产计划优化:")
print(production_optimization)
```

## <a name="数据预处理指导"></a>数据预处理指导

### 应用场景

- 数据清洗策略制定
- 特征工程指导
- 数据增强建议
- 数据质量改进

### 实现示例

```python
class DataPreprocessingGuide:
    """数据预处理指导"""
    
    def __init__(self, model="qwen2.5:7b"):
        self.client = OllamaClient()
        self.model = model
    
    def recommend_preprocessing_strategy(self, data_profile: dict, analysis_goal: str):
        """推荐预处理策略"""
        prompt = f"""
        作为数据科学专家,请为以下工业数据制定预处理策略:
        
        数据概况:
        {json.dumps(data_profile, indent=2, ensure_ascii=False)}
        
        分析目标:{analysis_goal}
        
        请提供:
        1. 数据清洗策略(缺失值、异常值处理)
        2. 特征工程建议(变换、组合、选择)
        3. 数据标准化/归一化方案
        4. 时间序列特有的预处理步骤
        5. 数据增强技术建议
        6. 数据验证和质量检查方法
        7. 预处理流程的自动化建议
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.2, "num_predict": 700}
        )
        
        return self.client.generate(request)
    
    def design_feature_engineering(self, raw_features: list, domain_knowledge: dict, target_variable: str):
        """设计特征工程"""
        features_text = "\n".join([f"- {f['name']}: {f['type']} - {f['description']}" for f in raw_features])
        
        prompt = f"""
        请为以下工业数据设计特征工程方案:
        
        原始特征:
        {features_text}
        
        领域知识:
        {json.dumps(domain_knowledge, indent=2, ensure_ascii=False)}
        
        目标变量:{target_variable}
        
        请设计:
        1. 基础特征变换(对数、平方根、Box-Cox等)
        2. 时间相关特征(滞后、滑动窗口、差分)
        3. 交互特征(比值、乘积、组合)
        4. 统计特征(均值、方差、分位数)
        5. 频域特征(FFT、小波变换)
        6. 领域特定特征(基于工艺机理)
        7. 特征选择策略
        """
        
        request = OllamaRequest(
            model=self.model,
            prompt=prompt,
            stream=False,
            options={"temperature": 0.15, "num_predict": 600}
        )
        
        return self.client.generate(request)

# 使用示例
preprocessing_guide = DataPreprocessingGuide()

# 预处理策略推荐
data_profile = {
    "数据类型": "多变量时间序列",
    "数据量": "100万条记录",
    "时间跨度": "2年",
    "采样频率": "1分钟",
    "变量数量": 25,
    "数据质量问题": {
        "缺失值": "3.2%,主要集中在传感器故障期间",
        "异常值": "1.5%,包括传感器漂移和设备故障",
        "数据漂移": "部分传感器存在长期漂移",
        "噪声水平": "信噪比约为30dB"
    },
    "数据分布": {
        "正态性": "大部分变量近似正态分布",
        "偏度": "温度变量轻微右偏",
        "异方差性": "流量变量存在异方差"
    },
    "相关性": {
        "强相关变量对": 3,
        "多重共线性": "VIF>5的变量有2个",
        "滞后相关": "存在1-5分钟的滞后效应"
    }
}

preprocessing_strategy = preprocessing_guide.recommend_preprocessing_strategy(
    data_profile,
    "建立产品质量预测模型,要求预测精度MAPE<3%"
)
print("数据预处理策略:")
print(preprocessing_strategy)

# 特征工程设计
raw_features = [
    {"name": "反应器温度", "type": "连续型", "description": "主反应器内部温度,°C"},
    {"name": "进料流量", "type": "连续型", "description": "原料进料流量,L/min"},
    {"name": "反应压力", "type": "连续型", "description": "反应器内压力,bar"},
    {"name": "搅拌速度", "type": "连续型", "description": "搅拌器转速,rpm"},
    {"name": "催化剂浓度", "type": "连续型", "description": "催化剂浓度,mol/L"},
    {"name": "冷却水温度", "type": "连续型", "description": "冷却水入口温度,°C"},
    {"name": "操作模式", "type": "分类型", "description": "生产模式:连续/批次/半批次"},
    {"name": "班次", "type": "分类型", "description": "操作班次:白班/夜班"}
]

domain_knowledge = {
    "工艺机理": {
        "关键反应": "聚合反应,放热反应",
        "控制要点": "温度控制是关键,压力次之",
        "时间常数": "反应器时间常数约15分钟",
        "传质限制": "高粘度时存在传质限制"
    },
    "设备特性": {
        "反应器类型": "连续搅拌反应器",
        "传热特性": "传热系数随温度变化",
        "混合特性": "搅拌速度影响混合效果"
    },
    "质量影响因素": {
        "主要因素": ["温度", "时间", "催化剂浓度"],
        "次要因素": ["压力", "搅拌速度", "进料速度"],
        "交互效应": "温度与催化剂浓度存在协同效应"
    }
}

feature_engineering = preprocessing_guide.design_feature_engineering(
    raw_features,
    domain_knowledge,
    "产品收率"
)
print("\n特征工程设计:")
print(feature_engineering)
```

## 📚 最佳实践总结

### 1. 提示词设计原则

- **专业性**:使用工业领域专业术语和概念
- **具体性**:提供具体的数据和参数
- **结构化**:使用JSON格式组织复杂数据
- **目标导向**:明确分析目标和期望输出

### 2. 模型选择建议

- **分析任务**:使用较低temperature(0.1-0.2)确保准确性
- **创意任务**:使用中等temperature(0.3-0.5)增加多样性
- **流式输出**:用于实时诊断和长时间分析
- **批量处理**:用于大量数据的批量分析

### 3. 集成策略

```python
# 与industrytslib其他模块集成示例
from industrytslib.core.data_processor import DataProcessor
from industrytslib.models.timesnet import TimesNet
from industrytslib.utils.llm import OllamaClient

class IntelligentAnalysisSystem:
    """智能分析系统"""
    
    def __init__(self):
        self.data_processor = DataProcessor()
        self.model = TimesNet()
        self.llm_client = OllamaClient()
    
    def comprehensive_analysis(self, data, task_description):
        """综合分析流程"""
        # 1. 数据预处理
        processed_data = self.data_processor.process(data)
        
        # 2. 模型预测
        predictions = self.model.predict(processed_data)
        
        # 3. LLM分析和解释
        analysis_prompt = f"""
        请分析以下预测结果:
        任务描述:{task_description}
        预测结果:{predictions.tolist()}
        数据特征:{processed_data.describe().to_dict()}
        
        请提供详细的分析报告。
        """
        
        llm_analysis = self.llm_client.generate(
            OllamaRequest(
                model="qwen2.5:7b",
                prompt=analysis_prompt,
                stream=False
            )
        )
        
        return {
            "predictions": predictions,
            "analysis": llm_analysis,
            "data_summary": processed_data.describe()
        }
```

### 4. 性能优化建议

- **缓存机制**:对相似查询使用缓存
- **批量处理**:合并多个小查询
- **异步调用**:使用异步客户端提高并发性
- **模型选择**:根据任务复杂度选择合适的模型大小

### 5. 安全和合规

- **数据脱敏**:敏感数据在发送前进行脱敏处理
- **访问控制**:实施适当的访问权限控制
- **审计日志**:记录所有LLM交互的审计日志
- **合规检查**:确保符合行业数据保护法规

## 🔗 相关资源

- [快速开始指南](quickstart.md)
- [API参考文档](api-reference.md)
- [industrytslib核心文档](../index.md)
- [模型库文档](../AIModels.md)

---

*本文档持续更新中,如有问题或建议,请提交Issue或Pull Request。*