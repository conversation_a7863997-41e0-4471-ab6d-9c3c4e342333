# 性能优化指南

本指南提供全面的性能优化策略,帮助您最大化industrytslib LLM功能的效率和响应速度。

## 📋 目录

- [性能基准测试](#性能基准测试)
- [模型选择优化](#模型选择优化)
- [请求优化策略](#请求优化策略)
- [缓存机制](#缓存机制)
- [并发处理](#并发处理)
- [资源管理](#资源管理)
- [网络优化](#网络优化)
- [监控和分析](#监控和分析)

## <a name="性能基准测试"></a>性能基准测试

### 建立性能基线

```python
import time
import statistics
import asyncio
from concurrent.futures import ThreadPoolExecutor
from industrytslib.utils.llm import OllamaClient, OllamaRequest

class PerformanceBenchmark:
    """性能基准测试工具"""
    
    def __init__(self):
        self.client = OllamaClient()
        self.results = []
    
    def benchmark_model_performance(self, models, test_cases):
        """基准测试不同模型性能"""
        results = {}
        
        for model in models:
            print(f"\n测试模型: {model}")
            model_results = []
            
            for case_name, prompt, expected_length in test_cases:
                print(f"  测试用例: {case_name}")
                
                # 预热
                self._warmup_model(model)
                
                # 多次测试取平均值
                times = []
                for i in range(3):
                    start_time = time.time()
                    
                    request = OllamaRequest(
                        model=model,
                        prompt=prompt,
                        stream=False,
                        options={
                            "num_predict": expected_length,
                            "temperature": 0.1
                        }
                    )
                    
                    try:
                        response = self.client.generate(request)
                        end_time = time.time()
                        
                        response_time = end_time - start_time
                        times.append(response_time)
                        
                        # 计算吞吐量(tokens/秒)
                        tokens_per_second = len(response) / response_time
                        
                        print(f"    运行 {i+1}: {response_time:.2f}s, {tokens_per_second:.1f} tokens/s")
                        
                    except Exception as e:
                        print(f"    运行 {i+1}: 错误 - {e}")
                        times.append(float('inf'))
                
                avg_time = statistics.mean([t for t in times if t != float('inf')])
                model_results.append({
                    "case": case_name,
                    "avg_time": avg_time,
                    "times": times
                })
            
            results[model] = model_results
        
        return results
    
    def _warmup_model(self, model):
        """模型预热"""
        warmup_request = OllamaRequest(
            model=model,
            prompt="测试",
            options={"num_predict": 10}
        )
        try:
            self.client.generate(warmup_request)
        except:
            pass
    
    def benchmark_concurrent_requests(self, model, num_concurrent=5):
        """并发请求基准测试"""
        print(f"\n并发测试: {num_concurrent} 个并发请求")
        
        def single_request():
            request = OllamaRequest(
                model=model,
                prompt="分析工业数据:温度25°C,压力2.1bar,流量50L/min",
                options={"num_predict": 100, "temperature": 0.1}
            )
            
            start_time = time.time()
            try:
                response = self.client.generate(request)
                return time.time() - start_time
            except Exception as e:
                print(f"请求失败: {e}")
                return float('inf')
        
        # 串行测试
        print("串行执行:")
        serial_start = time.time()
        serial_times = []
        for i in range(num_concurrent):
            response_time = single_request()
            serial_times.append(response_time)
            print(f"  请求 {i+1}: {response_time:.2f}s")
        serial_total = time.time() - serial_start
        
        # 并行测试
        print("\n并行执行:")
        parallel_start = time.time()
        with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(single_request) for _ in range(num_concurrent)]
            parallel_times = [f.result() for f in futures]
        parallel_total = time.time() - parallel_start
        
        print(f"\n结果对比:")
        print(f"串行总时间: {serial_total:.2f}s")
        print(f"并行总时间: {parallel_total:.2f}s")
        print(f"性能提升: {serial_total/parallel_total:.2f}x")
        
        return {
            "serial_total": serial_total,
            "parallel_total": parallel_total,
            "speedup": serial_total/parallel_total
        }

# 使用示例
benchmark = PerformanceBenchmark()

# 定义测试用例
test_cases = [
    ("简单分析", "分析温度数据:25°C", 50),
    ("中等分析", "分析以下工艺参数:温度25°C,压力2.1bar,流量50L/min,请提供优化建议", 200),
    ("复杂分析", "作为工业专家,请详细分析反应器数据并提供完整的诊断报告" + "数据点," * 50, 500)
]

# 测试不同模型
models = ["qwen2.5:7b", "llama3.1:latest"]
results = benchmark.benchmark_model_performance(models, test_cases)

# 并发测试
concurrent_results = benchmark.benchmark_concurrent_requests("qwen2.5:7b", 3)
```

### 性能指标分析

```python
class PerformanceAnalyzer:
    """性能分析器"""
    
    def analyze_results(self, benchmark_results):
        """分析基准测试结果"""
        print("\n=== 性能分析报告 ===")
        
        for model, results in benchmark_results.items():
            print(f"\n模型: {model}")
            print("-" * 40)
            
            total_time = sum(r["avg_time"] for r in results)
            avg_time = total_time / len(results)
            
            print(f"平均响应时间: {avg_time:.2f}s")
            print(f"总测试时间: {total_time:.2f}s")
            
            # 按用例分析
            for result in results:
                case_name = result["case"]
                avg_time = result["avg_time"]
                times = result["times"]
                
                if times and all(t != float('inf') for t in times):
                    std_dev = statistics.stdev(times)
                    print(f"  {case_name}: {avg_time:.2f}s ±{std_dev:.2f}s")
                else:
                    print(f"  {case_name}: 测试失败")
    
    def recommend_optimizations(self, results):
        """推荐优化策略"""
        print("\n=== 优化建议 ===")
        
        # 分析响应时间模式
        all_times = []
        for model_results in results.values():
            for result in model_results:
                if result["avg_time"] != float('inf'):
                    all_times.append(result["avg_time"])
        
        if all_times:
            avg_response_time = statistics.mean(all_times)
            
            if avg_response_time > 10:
                print("⚠️  响应时间较长,建议:")
                print("   - 使用更小的模型")
                print("   - 减少num_predict参数")
                print("   - 优化提示词长度")
                print("   - 考虑使用流式输出")
            
            elif avg_response_time > 5:
                print("📊 响应时间中等,建议:")
                print("   - 实施响应缓存")
                print("   - 优化模型参数")
                print("   - 考虑并发处理")
            
            else:
                print("✅ 响应时间良好")
                print("   - 可以考虑使用更大模型提高质量")
                print("   - 实施高级优化策略")

# 分析结果
analyzer = PerformanceAnalyzer()
analyzer.analyze_results(results)
analyzer.recommend_optimizations(results)
```

## <a name="模型选择优化"></a>模型选择优化

### 智能模型选择

```python
class IntelligentModelSelector:
    """智能模型选择器"""
    
    def __init__(self):
        self.model_profiles = {
            "qwen2.5:1.5b": {
                "size": "small",
                "speed": "fast",
                "quality": "basic",
                "memory": "low",
                "use_cases": ["简单查询", "状态检查", "数据摘要"]
            },
            "qwen2.5:7b": {
                "size": "medium",
                "speed": "medium",
                "quality": "good",
                "memory": "medium",
                "use_cases": ["数据分析", "趋势预测", "异常检测"]
            },
            "qwen2.5:14b": {
                "size": "large",
                "speed": "slow",
                "quality": "excellent",
                "memory": "high",
                "use_cases": ["复杂分析", "决策支持", "深度诊断"]
            }
        }
    
    def select_optimal_model(self, task_requirements):
        """选择最优模型"""
        priority = task_requirements.get("priority", "balanced")
        complexity = task_requirements.get("complexity", "medium")
        response_time_limit = task_requirements.get("max_response_time", 10)
        quality_requirement = task_requirements.get("min_quality", "good")
        
        # 评分系统
        scores = {}
        
        for model, profile in self.model_profiles.items():
            score = 0
            
            # 速度评分
            if priority == "speed":
                speed_scores = {"fast": 10, "medium": 6, "slow": 2}
                score += speed_scores.get(profile["speed"], 0) * 0.4
            
            # 质量评分
            elif priority == "quality":
                quality_scores = {"basic": 3, "good": 7, "excellent": 10}
                score += quality_scores.get(profile["quality"], 0) * 0.4
            
            # 平衡评分
            else:
                speed_scores = {"fast": 8, "medium": 10, "slow": 4}
                quality_scores = {"basic": 4, "good": 10, "excellent": 8}
                score += speed_scores.get(profile["speed"], 0) * 0.2
                score += quality_scores.get(profile["quality"], 0) * 0.2
            
            # 复杂度匹配
            complexity_match = {
                "simple": {"small": 10, "medium": 6, "large": 2},
                "medium": {"small": 4, "medium": 10, "large": 8},
                "complex": {"small": 2, "medium": 6, "large": 10}
            }
            score += complexity_match.get(complexity, {}).get(profile["size"], 0) * 0.3
            
            # 资源约束
            memory_scores = {"low": 10, "medium": 7, "high": 4}
            score += memory_scores.get(profile["memory"], 0) * 0.1
            
            scores[model] = score
        
        # 选择最高分模型
        best_model = max(scores.items(), key=lambda x: x[1])
        
        return {
            "recommended_model": best_model[0],
            "score": best_model[1],
            "all_scores": scores,
            "reasoning": self._explain_selection(best_model[0], task_requirements)
        }
    
    def _explain_selection(self, model, requirements):
        """解释模型选择原因"""
        profile = self.model_profiles[model]
        
        reasons = []
        
        if requirements.get("priority") == "speed":
            reasons.append(f"优先考虑速度,该模型速度等级:{profile['speed']}")
        
        if requirements.get("priority") == "quality":
            reasons.append(f"优先考虑质量,该模型质量等级:{profile['quality']}")
        
        complexity = requirements.get("complexity", "medium")
        reasons.append(f"任务复杂度为{complexity},模型大小{profile['size']}匹配")
        
        return "; ".join(reasons)

# 使用示例
selector = IntelligentModelSelector()

# 不同场景的模型选择
scenarios = [
    {
        "name": "实时监控",
        "requirements": {
            "priority": "speed",
            "complexity": "simple",
            "max_response_time": 2
        }
    },
    {
        "name": "深度分析",
        "requirements": {
            "priority": "quality",
            "complexity": "complex",
            "min_quality": "excellent"
        }
    },
    {
        "name": "日常分析",
        "requirements": {
            "priority": "balanced",
            "complexity": "medium"
        }
    }
]

for scenario in scenarios:
    print(f"\n场景: {scenario['name']}")
    selection = selector.select_optimal_model(scenario['requirements'])
    print(f"推荐模型: {selection['recommended_model']}")
    print(f"评分: {selection['score']:.1f}")
    print(f"原因: {selection['reasoning']}")
```

## <a name="请求优化策略"></a>请求优化策略

### 提示词优化

```python
class PromptOptimizer:
    """提示词优化器"""
    
    def __init__(self):
        self.templates = {
            "data_analysis": """
            作为工业数据分析专家,请分析以下数据:
            
            数据: {data}
            
            请提供:
            1. 关键指标分析
            2. 异常情况识别
            3. 改进建议
            
            分析结果:
            """,
            
            "anomaly_detection": """
            检测异常:
            数据: {data}
            正常范围: {normal_range}
            
            异常分析:
            """,
            
            "optimization": """
            优化建议:
            当前状态: {current_state}
            目标: {target}
            
            建议:
            """
        }
    
    def optimize_prompt(self, task_type, data, max_length=2000):
        """优化提示词"""
        template = self.templates.get(task_type, "{data}")
        
        # 数据压缩
        if isinstance(data, dict):
            compressed_data = self._compress_dict(data, max_length // 2)
        elif isinstance(data, str):
            compressed_data = data[:max_length // 2]
        else:
            compressed_data = str(data)[:max_length // 2]
        
        # 生成提示词
        if task_type == "data_analysis":
            prompt = template.format(data=compressed_data)
        elif task_type == "anomaly_detection":
            prompt = template.format(
                data=compressed_data,
                normal_range=data.get("normal_range", "未知")
            )
        elif task_type == "optimization":
            prompt = template.format(
                current_state=compressed_data,
                target=data.get("target", "优化性能")
            )
        else:
            prompt = compressed_data
        
        # 长度检查和截断
        if len(prompt) > max_length:
            prompt = prompt[:max_length-50] + "\n\n[数据已截断,请基于可见部分进行分析]"
        
        return prompt
    
    def _compress_dict(self, data_dict, max_length):
        """压缩字典数据"""
        import json
        
        # 尝试完整序列化
        full_json = json.dumps(data_dict, ensure_ascii=False, indent=2)
        
        if len(full_json) <= max_length:
            return full_json
        
        # 压缩策略1:移除缩进
        compact_json = json.dumps(data_dict, ensure_ascii=False, separators=(',', ':'))
        
        if len(compact_json) <= max_length:
            return compact_json
        
        # 压缩策略2:只保留关键字段
        key_fields = ['value', 'timestamp', 'status', 'temperature', 'pressure', 'flow']
        compressed_dict = {}
        
        for key, value in data_dict.items():
            if any(kf in key.lower() for kf in key_fields):
                compressed_dict[key] = value
            
            # 检查长度
            test_json = json.dumps(compressed_dict, ensure_ascii=False)
            if len(test_json) > max_length * 0.8:
                break
        
        return json.dumps(compressed_dict, ensure_ascii=False, indent=2)
    
    def batch_optimize_prompts(self, requests):
        """批量优化提示词"""
        optimized_requests = []
        
        for req in requests:
            if hasattr(req, 'prompt') and len(req.prompt) > 1000:
                # 尝试提取任务类型
                task_type = self._detect_task_type(req.prompt)
                
                # 优化提示词
                optimized_prompt = self.optimize_prompt(
                    task_type, 
                    req.prompt, 
                    max_length=1000
                )
                
                # 创建新请求
                optimized_req = OllamaRequest(
                    model=req.model,
                    prompt=optimized_prompt,
                    stream=req.stream,
                    options=req.options
                )
                
                optimized_requests.append(optimized_req)
            else:
                optimized_requests.append(req)
        
        return optimized_requests
    
    def _detect_task_type(self, prompt):
        """检测任务类型"""
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ['异常', '故障', '报警', 'anomaly']):
            return "anomaly_detection"
        elif any(word in prompt_lower for word in ['优化', '改进', 'optimize']):
            return "optimization"
        elif any(word in prompt_lower for word in ['分析', '数据', 'analyze']):
            return "data_analysis"
        else:
            return "general"

# 使用示例
optimizer = PromptOptimizer()

# 优化单个提示词
data = {
    "temperature": [20, 21, 22, 23, 24] * 100,  # 大量数据
    "pressure": [1.0, 1.1, 1.2, 1.1, 1.0] * 100,
    "timestamp": ["2024-01-01 10:00:00"] * 500
}

optimized_prompt = optimizer.optimize_prompt("data_analysis", data)
print(f"优化后提示词长度: {len(optimized_prompt)}")
print(f"优化后提示词预览: {optimized_prompt[:200]}...")
```

### 参数调优

```python
class ParameterTuner:
    """参数调优器"""
    
    def __init__(self):
        self.optimal_params = {
            "data_analysis": {
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 20,
                "repeat_penalty": 1.1,
                "num_predict": 300
            },
            "creative_tasks": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.0,
                "num_predict": 500
            },
            "technical_writing": {
                "temperature": 0.2,
                "top_p": 0.85,
                "top_k": 30,
                "repeat_penalty": 1.05,
                "num_predict": 400
            }
        }
    
    def tune_parameters(self, task_type, quality_priority="balanced"):
        """调优参数"""
        base_params = self.optimal_params.get(task_type, self.optimal_params["data_analysis"])
        
        # 根据质量优先级调整
        tuned_params = base_params.copy()
        
        if quality_priority == "speed":
            tuned_params["num_predict"] = min(tuned_params["num_predict"], 200)
            tuned_params["temperature"] = min(tuned_params["temperature"] + 0.1, 0.5)
        
        elif quality_priority == "quality":
            tuned_params["num_predict"] = min(tuned_params["num_predict"] * 1.5, 800)
            tuned_params["temperature"] = max(tuned_params["temperature"] - 0.05, 0.05)
        
        return tuned_params
    
    def adaptive_tuning(self, request_history):
        """自适应参数调优"""
        if not request_history:
            return self.optimal_params["data_analysis"]
        
        # 分析历史请求性能
        avg_response_time = sum(h["response_time"] for h in request_history) / len(request_history)
        avg_quality_score = sum(h.get("quality_score", 5) for h in request_history) / len(request_history)
        
        # 基于性能调整参数
        adaptive_params = self.optimal_params["data_analysis"].copy()
        
        if avg_response_time > 10:  # 响应时间过长
            adaptive_params["num_predict"] = max(adaptive_params["num_predict"] * 0.8, 100)
            adaptive_params["temperature"] += 0.1
        
        if avg_quality_score < 3:  # 质量不佳
            adaptive_params["temperature"] = max(adaptive_params["temperature"] - 0.1, 0.05)
            adaptive_params["top_p"] = max(adaptive_params["top_p"] - 0.1, 0.7)
        
        return adaptive_params

# 使用示例
tuner = ParameterTuner()

# 获取优化参数
optimal_params = tuner.tune_parameters("data_analysis", "speed")
print(f"优化参数: {optimal_params}")

# 创建优化的请求
optimized_request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析反应器数据...",
    options=optimal_params
)
```

## <a name="缓存机制"></a>缓存机制

### 智能响应缓存

```python
import hashlib
import json
import time
from typing import Dict, Any, Optional

class IntelligentCache:
    """智能缓存系统"""
    
    def __init__(self, max_size=1000, ttl=3600):
        self.cache = {}  # {hash: {response, timestamp, access_count}}
        self.max_size = max_size
        self.ttl = ttl  # 生存时间(秒)
        self.hit_count = 0
        self.miss_count = 0
    
    def _generate_cache_key(self, request: OllamaRequest) -> str:
        """生成缓存键"""
        # 创建请求的唯一标识
        cache_data = {
            "model": request.model,
            "prompt": request.prompt,
            "options": request.options or {}
        }
        
        # 对于相似的数值,进行归一化
        normalized_prompt = self._normalize_prompt(request.prompt)
        cache_data["prompt"] = normalized_prompt
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _normalize_prompt(self, prompt: str) -> str:
        """归一化提示词以提高缓存命中率"""
        import re
        
        # 替换数值为占位符(对于相似的数值查询)
        normalized = re.sub(r'\d+\.\d+', 'NUM', prompt)
        normalized = re.sub(r'\d+', 'NUM', normalized)
        
        # 移除多余空格
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized
    
    def get(self, request: OllamaRequest) -> Optional[str]:
        """获取缓存响应"""
        cache_key = self._generate_cache_key(request)
        
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            
            # 检查是否过期
            if time.time() - cache_entry["timestamp"] > self.ttl:
                del self.cache[cache_key]
                self.miss_count += 1
                return None
            
            # 更新访问计数和时间
            cache_entry["access_count"] += 1
            cache_entry["last_access"] = time.time()
            
            self.hit_count += 1
            return cache_entry["response"]
        
        self.miss_count += 1
        return None
    
    def put(self, request: OllamaRequest, response: str):
        """存储响应到缓存"""
        cache_key = self._generate_cache_key(request)
        
        # 如果缓存已满,删除最少使用的项
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[cache_key] = {
            "response": response,
            "timestamp": time.time(),
            "last_access": time.time(),
            "access_count": 1
        }
    
    def _evict_lru(self):
        """删除最少使用的缓存项"""
        if not self.cache:
            return
        
        # 找到最少使用的项
        lru_key = min(
            self.cache.keys(),
            key=lambda k: (self.cache[k]["access_count"], self.cache[k]["last_access"])
        )
        
        del self.cache[lru_key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            "cache_size": len(self.cache),
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": f"{hit_rate:.2%}",
            "total_requests": total_requests
        }
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.hit_count = 0
        self.miss_count = 0

class CachedLLMClient:
    """带缓存的LLM客户端"""
    
    def __init__(self, cache_size=1000, cache_ttl=3600):
        self.client = OllamaClient()
        self.cache = IntelligentCache(cache_size, cache_ttl)
    
    def generate(self, request: OllamaRequest) -> str:
        """带缓存的生成"""
        # 尝试从缓存获取
        cached_response = self.cache.get(request)
        if cached_response is not None:
            print("✓ 缓存命中")
            return cached_response
        
        # 缓存未命中,调用实际API
        print("○ 缓存未命中,调用API")
        response = self.client.generate(request)
        
        # 存储到缓存
        self.cache.put(request, response)
        
        return response
    
    def get_cache_stats(self):
        """获取缓存统计"""
        return self.cache.get_stats()

# 使用示例
cached_client = CachedLLMClient(cache_size=500, cache_ttl=1800)

# 测试缓存效果
test_requests = [
    OllamaRequest(
        model="qwen2.5:7b",
        prompt="分析温度数据:25.1°C",
        options={"temperature": 0.1, "num_predict": 100}
    ),
    OllamaRequest(
        model="qwen2.5:7b",
        prompt="分析温度数据:25.2°C",  # 相似请求
        options={"temperature": 0.1, "num_predict": 100}
    ),
    OllamaRequest(
        model="qwen2.5:7b",
        prompt="分析温度数据:25.1°C",  # 重复请求
        options={"temperature": 0.1, "num_predict": 100}
    )
]

print("测试缓存性能:")
for i, request in enumerate(test_requests):
    print(f"\n请求 {i+1}:")
    start_time = time.time()
    response = cached_client.generate(request)
    end_time = time.time()
    print(f"响应时间: {end_time - start_time:.2f}s")
    print(f"响应长度: {len(response)}")

# 显示缓存统计
print("\n缓存统计:")
stats = cached_client.get_cache_stats()
for key, value in stats.items():
    print(f"{key}: {value}")
```

## <a name="并发处理"></a>并发处理

### 异步并发处理

```python
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
from typing import List

class AsyncLLMClient:
    """异步LLM客户端"""
    
    def __init__(self, max_concurrent=5):
        self.base_url = "http://localhost:11434"
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def generate_async(self, session: aiohttp.ClientSession, request: OllamaRequest) -> str:
        """异步生成响应"""
        async with self.semaphore:
            url = f"{self.base_url}/api/generate"
            
            payload = {
                "model": request.model,
                "prompt": request.prompt,
                "stream": False,
                "options": request.options or {}
            }
            
            try:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("response", "")
                    else:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")
            
            except Exception as e:
                print(f"异步请求失败: {e}")
                return f"错误: {e}"
    
    async def batch_generate(self, requests: List[OllamaRequest]) -> List[str]:
        """批量异步生成"""
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.generate_async(session, request)
                for request in requests
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常
            processed_results = []
            for result in results:
                if isinstance(result, Exception):
                    processed_results.append(f"错误: {result}")
                else:
                    processed_results.append(result)
            
            return processed_results

class ConcurrentProcessor:
    """并发处理器"""
    
    def __init__(self, max_workers=3):
        self.sync_client = OllamaClient()
        self.async_client = AsyncLLMClient(max_concurrent=max_workers)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    def process_batch_sync(self, requests: List[OllamaRequest]) -> List[str]:
        """同步批量处理"""
        def process_single(request):
            try:
                return self.sync_client.generate(request)
            except Exception as e:
                return f"错误: {e}"
        
        # 使用线程池并发处理
        futures = [self.executor.submit(process_single, req) for req in requests]
        results = [future.result() for future in futures]
        
        return results
    
    async def process_batch_async(self, requests: List[OllamaRequest]) -> List[str]:
        """异步批量处理"""
        return await self.async_client.batch_generate(requests)
    
    def benchmark_concurrency(self, requests: List[OllamaRequest]):
        """并发性能基准测试"""
        print(f"测试 {len(requests)} 个请求的并发性能")
        
        # 串行处理
        print("\n1. 串行处理:")
        start_time = time.time()
        serial_results = []
        for request in requests:
            try:
                result = self.sync_client.generate(request)
                serial_results.append(result)
            except Exception as e:
                serial_results.append(f"错误: {e}")
        serial_time = time.time() - start_time
        print(f"   时间: {serial_time:.2f}s")
        
        # 同步并发处理
        print("\n2. 同步并发处理:")
        start_time = time.time()
        sync_concurrent_results = self.process_batch_sync(requests)
        sync_concurrent_time = time.time() - start_time
        print(f"   时间: {sync_concurrent_time:.2f}s")
        print(f"   加速比: {serial_time/sync_concurrent_time:.2f}x")
        
        # 异步并发处理
        print("\n3. 异步并发处理:")
        start_time = time.time()
        async_results = asyncio.run(self.process_batch_async(requests))
        async_time = time.time() - start_time
        print(f"   时间: {async_time:.2f}s")
        print(f"   加速比: {serial_time/async_time:.2f}x")
        
        return {
            "serial_time": serial_time,
            "sync_concurrent_time": sync_concurrent_time,
            "async_time": async_time,
            "sync_speedup": serial_time/sync_concurrent_time,
            "async_speedup": serial_time/async_time
        }

# 使用示例
processor = ConcurrentProcessor(max_workers=3)

# 创建测试请求
test_requests = [
    OllamaRequest(
        model="qwen2.5:7b",
        prompt=f"分析数据点 {i}: 温度{20+i}°C,压力{2.0+i*0.1}bar",
        options={"num_predict": 100, "temperature": 0.1}
    )
    for i in range(5)
]

# 运行并发基准测试
benchmark_results = processor.benchmark_concurrency(test_requests)
print("\n=== 并发性能总结 ===")
for key, value in benchmark_results.items():
    if "time" in key:
        print(f"{key}: {value:.2f}s")
    else:
        print(f"{key}: {value:.2f}x")
```

## <a name="资源管理"></a>资源管理

### 内存和CPU优化

```python
import psutil
import gc
import threading
import time
from contextlib import contextmanager

class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self.memory_threshold = 80  # 内存使用阈值(百分比)
        self.monitoring = False
        self.monitor_thread = None
    
    @contextmanager
    def monitor_resources(self):
        """资源监控上下文管理器"""
        initial_memory = psutil.virtual_memory().percent
        initial_cpu = psutil.cpu_percent()
        
        print(f"初始资源使用 - 内存: {initial_memory:.1f}%, CPU: {initial_cpu:.1f}%")
        
        try:
            yield
        finally:
            # 强制垃圾回收
            gc.collect()
            
            final_memory = psutil.virtual_memory().percent
            final_cpu = psutil.cpu_percent()
            
            print(f"最终资源使用 - 内存: {final_memory:.1f}%, CPU: {final_cpu:.1f}%")
            print(f"资源变化 - 内存: {final_memory-initial_memory:+.1f}%, CPU: {final_cpu-initial_cpu:+.1f}%")
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        memory_info = psutil.virtual_memory()
        
        if memory_info.percent > self.memory_threshold:
            print(f"⚠️ 内存使用过高: {memory_info.percent:.1f}%")
            self.cleanup_memory()
            return False
        
        return True
    
    def cleanup_memory(self):
        """清理内存"""
        print("执行内存清理...")
        
        # 强制垃圾回收
        collected = gc.collect()
        print(f"垃圾回收释放了 {collected} 个对象")
        
        # 清理缓存(如果有的话)
        # 这里可以添加清理应用程序特定缓存的代码
    
    def start_monitoring(self, interval=5):
        """开始资源监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        
        def monitor_loop():
            while self.monitoring:
                memory_percent = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent(interval=1)
                
                if memory_percent > self.memory_threshold:
                    print(f"⚠️ 内存使用警告: {memory_percent:.1f}%")
                    self.cleanup_memory()
                
                if cpu_percent > 90:
                    print(f"⚠️ CPU使用过高: {cpu_percent:.1f}%")
                
                time.sleep(interval)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        print("资源监控已启动")
    
    def stop_monitoring(self):
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print("资源监控已停止")

class OptimizedLLMClient:
    """资源优化的LLM客户端"""
    
    def __init__(self):
        self.client = OllamaClient()
        self.resource_manager = ResourceManager()
        self.request_queue = []
        self.max_queue_size = 10
    
    def generate_with_resource_management(self, request: OllamaRequest) -> str:
        """带资源管理的生成"""
        # 检查资源使用情况
        if not self.resource_manager.check_memory_usage():
            print("内存不足,延迟处理请求")
            time.sleep(2)  # 等待内存清理
        
        with self.resource_manager.monitor_resources():
            try:
                response = self.client.generate(request)
                return response
            except Exception as e:
                print(f"生成失败: {e}")
                # 尝试内存清理后重试
                self.resource_manager.cleanup_memory()
                return self.client.generate(request)
    
    def batch_generate_optimized(self, requests: List[OllamaRequest]) -> List[str]:
        """优化的批量生成"""
        results = []
        
        # 启动资源监控
        self.resource_manager.start_monitoring()
        
        try:
            for i, request in enumerate(requests):
                print(f"处理请求 {i+1}/{len(requests)}")
                
                # 每处理几个请求后检查资源
                if i % 3 == 0 and i > 0:
                    self.resource_manager.check_memory_usage()
                
                result = self.generate_with_resource_management(request)
                results.append(result)
                
                # 短暂休息以避免过载
                time.sleep(0.1)
        
        finally:
            self.resource_manager.stop_monitoring()
        
        return results

# 使用示例
optimized_client = OptimizedLLMClient()

# 创建资源密集型请求
heavy_requests = [
    OllamaRequest(
        model="qwen2.5:7b",
        prompt=f"详细分析以下复杂工业数据:" + "数据点," * 200,
        options={"num_predict": 500, "temperature": 0.1}
    )
    for _ in range(3)
]

print("开始资源优化的批量处理...")
results = optimized_client.batch_generate_optimized(heavy_requests)
print(f"\n处理完成,共生成 {len(results)} 个响应")
```

## <a name="网络优化"></a>网络优化

### 连接池和重试机制

```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import time

class OptimizedNetworkClient:
    """网络优化的客户端"""
    
    def __init__(self, host="localhost", port=11434):
        self.base_url = f"http://{host}:{port}"
        self.session = self._create_optimized_session()
    
    def _create_optimized_session(self):
        """创建优化的会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=1,  # 退避因子
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
            allowed_methods=["POST"]  # 允许重试的方法
        )
        
        # 配置连接池
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,  # 连接池大小
            pool_maxsize=20,  # 最大连接数
            pool_block=False  # 非阻塞
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认超时
        session.timeout = 30
        
        return session
    
    def generate_with_retry(self, request: OllamaRequest, max_retries=3) -> str:
        """带重试的生成"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": request.model,
            "prompt": request.prompt,
            "stream": False,
            "options": request.options or {}
        }
        
        for attempt in range(max_retries + 1):
            try:
                response = self.session.post(url, json=payload)
                response.raise_for_status()
                
                result = response.json()
                return result.get("response", "")
            
            except requests.exceptions.RequestException as e:
                if attempt == max_retries:
                    raise Exception(f"请求失败,已重试 {max_retries} 次: {e}")
                
                wait_time = 2 ** attempt  # 指数退避
                print(f"请求失败,{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                time.sleep(wait_time)
    
    def test_connection_quality(self):
        """测试连接质量"""
        print("测试网络连接质量...")
        
        # 测试延迟
        latencies = []
        for i in range(5):
            start_time = time.time()
            try:
                response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
                if response.status_code == 200:
                    latency = (time.time() - start_time) * 1000
                    latencies.append(latency)
                    print(f"  Ping {i+1}: {latency:.1f}ms")
                else:
                    print(f"  Ping {i+1}: 失败 (HTTP {response.status_code})")
            except Exception as e:
                print(f"  Ping {i+1}: 失败 ({e})")
        
        if latencies:
            avg_latency = sum(latencies) / len(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            
            print(f"\n连接质量报告:")
            print(f"  平均延迟: {avg_latency:.1f}ms")
            print(f"  最小延迟: {min_latency:.1f}ms")
            print(f"  最大延迟: {max_latency:.1f}ms")
            print(f"  成功率: {len(latencies)/5*100:.0f}%")
            
            # 连接质量评估
            if avg_latency < 50:
                print(f"  质量评级: 优秀 ✅")
            elif avg_latency < 200:
                print(f"  质量评级: 良好 ✓")
            elif avg_latency < 500:
                print(f"  质量评级: 一般 ⚠️")
            else:
                print(f"  质量评级: 较差 ❌")
        else:
            print("连接测试失败")
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()

# 使用示例
network_client = OptimizedNetworkClient()

# 测试连接质量
network_client.test_connection_quality()

# 使用优化的网络客户端
test_request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="测试网络优化",
    options={"num_predict": 50}
)

print("\n使用网络优化客户端:")
start_time = time.time()
response = network_client.generate_with_retry(test_request)
end_time = time.time()

print(f"响应时间: {end_time - start_time:.2f}s")
print(f"响应长度: {len(response)}")
```

## <a name="监控和分析"></a>监控和分析

### 性能监控仪表板

```python
import json
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, history_size=1000):
        self.metrics = {
            "request_count": 0,
            "total_response_time": 0,
            "error_count": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        self.history = deque(maxlen=history_size)
        self.model_stats = defaultdict(lambda: {
            "count": 0,
            "total_time": 0,
            "errors": 0
        })
        
        self.hourly_stats = defaultdict(int)
    
    def record_request(self, model: str, response_time: float, success: bool = True, 
                      cache_hit: bool = False, prompt_length: int = 0, response_length: int = 0):
        """记录请求指标"""
        timestamp = datetime.now()
        
        # 更新总体指标
        self.metrics["request_count"] += 1
        self.metrics["total_response_time"] += response_time
        
        if not success:
            self.metrics["error_count"] += 1
        
        if cache_hit:
            self.metrics["cache_hits"] += 1
        else:
            self.metrics["cache_misses"] += 1
        
        # 更新模型统计
        self.model_stats[model]["count"] += 1
        self.model_stats[model]["total_time"] += response_time
        if not success:
            self.model_stats[model]["errors"] += 1
        
        # 更新小时统计
        hour_key = timestamp.strftime("%Y-%m-%d %H:00")
        self.hourly_stats[hour_key] += 1
        
        # 记录详细历史
        self.history.append({
            "timestamp": timestamp.isoformat(),
            "model": model,
            "response_time": response_time,
            "success": success,
            "cache_hit": cache_hit,
            "prompt_length": prompt_length,
            "response_length": response_length
        })
    
    def get_dashboard_data(self):
        """获取仪表板数据"""
        total_requests = self.metrics["request_count"]
        
        if total_requests == 0:
            return {"message": "暂无数据"}
        
        avg_response_time = self.metrics["total_response_time"] / total_requests
        error_rate = self.metrics["error_count"] / total_requests * 100
        cache_hit_rate = self.metrics["cache_hits"] / (self.metrics["cache_hits"] + self.metrics["cache_misses"]) * 100
        
        # 计算吞吐量(最近1小时)
        recent_hour = datetime.now() - timedelta(hours=1)
        recent_requests = sum(1 for record in self.history 
                            if datetime.fromisoformat(record["timestamp"]) > recent_hour)
        throughput = recent_requests  # 请求/小时
        
        # 模型性能排名
        model_performance = []
        for model, stats in self.model_stats.items():
            if stats["count"] > 0:
                avg_time = stats["total_time"] / stats["count"]
                error_rate_model = stats["errors"] / stats["count"] * 100
                
                model_performance.append({
                    "model": model,
                    "avg_response_time": avg_time,
                    "request_count": stats["count"],
                    "error_rate": error_rate_model
                })
        
        model_performance.sort(key=lambda x: x["avg_response_time"])
        
        return {
            "overview": {
                "total_requests": total_requests,
                "avg_response_time": f"{avg_response_time:.2f}s",
                "error_rate": f"{error_rate:.1f}%",
                "cache_hit_rate": f"{cache_hit_rate:.1f}%",
                "throughput": f"{throughput} req/h"
            },
            "model_performance": model_performance,
            "recent_activity": list(self.history)[-10:],  # 最近10条记录
            "hourly_distribution": dict(self.hourly_stats)
        }
    
    def print_dashboard(self):
        """打印仪表板"""
        data = self.get_dashboard_data()
        
        if "message" in data:
            print(data["message"])
            return
        
        print("\n" + "=" * 60)
        print("🚀 LLM 性能监控仪表板")
        print("=" * 60)
        
        # 概览信息
        overview = data["overview"]
        print(f"\n📊 总体概览:")
        print(f"   总请求数: {overview['total_requests']}")
        print(f"   平均响应时间: {overview['avg_response_time']}")
        print(f"   错误率: {overview['error_rate']}")
        print(f"   缓存命中率: {overview['cache_hit_rate']}")
        print(f"   吞吐量: {overview['throughput']}")
        
        # 模型性能
        print(f"\n🏆 模型性能排名:")
        for i, model in enumerate(data["model_performance"][:3]):
            rank_emoji = ["🥇", "🥈", "🥉"][i] if i < 3 else "📊"
            print(f"   {rank_emoji} {model['model']}:")
            print(f"      响应时间: {model['avg_response_time']:.2f}s")
            print(f"      请求数: {model['request_count']}")
            print(f"      错误率: {model['error_rate']:.1f}%")
        
        # 最近活动
        print(f"\n🕒 最近活动:")
        for record in data["recent_activity"][-5:]:
            status = "✅" if record["success"] else "❌"
            cache = "💾" if record["cache_hit"] else "🌐"
            timestamp = datetime.fromisoformat(record["timestamp"]).strftime("%H:%M:%S")
            print(f"   {timestamp} {status} {cache} {record['model']} ({record['response_time']:.2f}s)")
    
    def export_metrics(self, filename=None):
        """导出指标数据"""
        if filename is None:
            filename = f"llm_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_time": datetime.now().isoformat(),
            "metrics": self.metrics,
            "model_stats": dict(self.model_stats),
            "history": list(self.history),
            "hourly_stats": dict(self.hourly_stats)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"指标数据已导出到: {filename}")
        return filename

class MonitoredLLMClient:
    """带监控的LLM客户端"""
    
    def __init__(self):
        self.client = OllamaClient()
        self.monitor = PerformanceMonitor()
        self.cache = IntelligentCache()
    
    def generate(self, request: OllamaRequest) -> str:
        """带监控的生成"""
        start_time = time.time()
        success = True
        cache_hit = False
        response = ""
        
        try:
            # 尝试缓存
            cached_response = self.cache.get(request)
            if cached_response:
                cache_hit = True
                response = cached_response
            else:
                response = self.client.generate(request)
                self.cache.put(request, response)
        
        except Exception as e:
            success = False
            response = f"错误: {e}"
        
        finally:
            response_time = time.time() - start_time
            
            # 记录指标
            self.monitor.record_request(
                model=request.model,
                response_time=response_time,
                success=success,
                cache_hit=cache_hit,
                prompt_length=len(request.prompt),
                response_length=len(response)
            )
        
        return response
    
    def get_dashboard(self):
        """获取监控仪表板"""
        return self.monitor.get_dashboard_data()
    
    def print_dashboard(self):
        """打印监控仪表板"""
        self.monitor.print_dashboard()

# 使用示例
monitored_client = MonitoredLLMClient()

# 模拟一些请求
test_requests = [
    OllamaRequest(model="qwen2.5:7b", prompt="分析温度数据", options={"num_predict": 100}),
    OllamaRequest(model="qwen2.5:7b", prompt="检测异常", options={"num_predict": 150}),
    OllamaRequest(model="llama3.1:latest", prompt="优化建议", options={"num_predict": 200}),
    OllamaRequest(model="qwen2.5:7b", prompt="分析温度数据", options={"num_predict": 100}),  # 重复请求
]

print("执行监控测试...")
for i, request in enumerate(test_requests):
    print(f"\n处理请求 {i+1}:")
    response = monitored_client.generate(request)
    print(f"响应长度: {len(response)}")

# 显示监控仪表板
monitored_client.print_dashboard()

# 导出指标
monitored_client.monitor.export_metrics()
```

## 最佳实践总结

### 性能优化检查清单

```markdown
## 🎯 性能优化检查清单

### 模型选择
- [ ] 根据任务复杂度选择合适大小的模型
- [ ] 为不同场景配置不同模型
- [ ] 定期评估模型性能

### 请求优化
- [ ] 优化提示词长度和结构
- [ ] 调整模型参数(temperature、num_predict等)
- [ ] 实施提示词模板化

### 缓存策略
- [ ] 启用智能响应缓存
- [ ] 配置合适的缓存大小和TTL
- [ ] 监控缓存命中率

### 并发处理
- [ ] 使用异步处理批量请求
- [ ] 配置合适的并发数量
- [ ] 实施请求队列管理

### 资源管理
- [ ] 监控内存和CPU使用
- [ ] 实施资源清理机制
- [ ] 配置资源使用阈值

### 网络优化
- [ ] 使用连接池
- [ ] 配置重试机制
- [ ] 优化超时设置

### 监控分析
- [ ] 实施性能监控
- [ ] 定期分析性能指标
- [ ] 建立性能基线
```

### 性能调优建议

1. **渐进式优化**:从最影响性能的因素开始优化
2. **基准测试**:建立性能基线,量化优化效果
3. **监控驱动**:基于实际监控数据进行优化决策
4. **场景适配**:针对不同应用场景采用不同优化策略
5. **持续改进**:定期评估和调整优化策略

### 常见性能瓶颈及解决方案

| 瓶颈类型 | 症状 | 解决方案 |
|---------|------|----------|
| 模型过大 | 响应时间长 | 使用更小模型或优化参数 |
| 提示词过长 | 处理缓慢 | 压缩提示词或分段处理 |
| 网络延迟 | 连接超时 | 优化网络配置或使用本地部署 |
| 内存不足 | 系统卡顿 | 增加内存或优化内存使用 |
| 并发冲突 | 请求失败 | 限制并发数或使用队列 |

通过遵循本指南的优化策略,您可以显著提升industrytslib LLM功能的性能和用户体验。记住,性能优化是一个持续的过程,需要根据实际使用情况不断调整和改进。