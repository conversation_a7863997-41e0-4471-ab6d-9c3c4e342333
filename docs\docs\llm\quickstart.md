# LLM快速入门指南# LLM快速开始

本指南帮助你在5分钟内快速上手industrytslib的LLM功能,支持Ollama和SiliconFlow两种服务。

## 🚀 快速开始

### 选择LLM服务

你可以选择以下任一种服务:

#### 选项1:<PERSON><PERSON><PERSON>(本地部署)

```bash
# 安装Ollama(如果还没有安装)
curl -fsSL https://ollama.ai/install.sh | sh

# 启动Ollama服务
ollama serve

# 下载推荐模型
ollama pull qwen2.5:7b
```

#### 选项2:SiliconFlow(云端服务)

1. 访问 [SiliconFlow官网](https://siliconflow.cn) 注册账号
2. 获取API密钥
3. 设置环境变量(可选):
   ```bash
   export SILICONFLOW_API_KEY="your-api-key-here"
   ```

### 验证连接

#### Ollama验证

```bash
# 运行快速测试确保一切正常
uv run examples/experiment/exp_llm/quick_test_remote.py
```

#### SiliconFlow验证

```python
from industrytslib.utils.llm import sf_quick_generate

# 测试连接
try:
    response = sf_quick_generate(
        prompt="Hello, SiliconFlow!",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key-here",
        stream=False
    )
    print("✅ SiliconFlow连接成功！")
except Exception as e:
    print(f"❌ 连接失败: {e}")
```

### 第一个LLM调用

#### Ollama调用

```python
from industrytslib.utils.llm import quick_generate

# 最简单的调用
response = quick_generate(
    prompt="请简单介绍工业4.0的核心特征",
    model="qwen2.5:7b",
    stream=False
)
print(response)
```

#### SiliconFlow调用

```python
from industrytslib.utils.llm import sf_quick_generate

# 云端LLM调用
response = sf_quick_generate(
    prompt="请简单介绍工业4.0的核心特征",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    stream=False
)
print(response)
```

## 📝 常用场景示例

### 工业数据分析

#### 使用Ollama

```python
from industrytslib.utils.llm import OllamaClient, OllamaRequest

client = OllamaClient()

# 分析时间序列数据
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="""
    我有一个化工反应器的温度数据,采样频率1分钟,
    最近发现温度波动异常增大。请分析可能的原因。
    """,
    stream=False,
    options={"temperature": 0.2}  # 专业分析使用低随机性
)

analysis = client.generate(request)
print(analysis)
```

#### 使用SiliconFlow

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowRequest, SiliconFlowMessage

client = SiliconFlowClient(api_key="your-api-key-here")

# 分析时间序列数据
request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=[
        SiliconFlowMessage(
            role="user",
            content="""
            我有一个化工反应器的温度数据,采样频率1分钟,
            最近发现温度波动异常增大。请分析可能的原因。
            """
        )
    ],
    temperature=0.2,  # 专业分析使用低随机性
    stream=False
)

analysis = client.chat_completions(request)
print(analysis.choices[0].message.content)
```

### 模型选择建议

#### 使用Ollama

```python
# 获取模型架构建议
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="""
    我需要预测未来24小时的产品质量指标,
    数据特征:10个传感器变量,5分钟采样,包含明显的日周期性。
    请推荐最适合的深度学习模型。
    """,
    stream=False
)

recommendation = client.generate(request)
print(recommendation)
```

#### 使用SiliconFlow(推理模型)

```python
from industrytslib.utils.llm import sf_quick_generate, is_reasoning_model

# 使用推理模型获取更深入的分析
model = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
print(f"使用推理模型: {is_reasoning_model(model)}")

recommendation = sf_quick_generate(
    prompt="""
    我需要预测未来24小时的产品质量指标,
    数据特征:10个传感器变量,5分钟采样,包含明显的日周期性。
    请推荐最适合的深度学习模型,并详细分析选择理由。
    """,
    model=model,
    api_key="your-api-key-here",
    temperature=0.1,  # 推理模型使用更低的随机性
    stream=False
)
print(recommendation)
```

### 流式对话

#### 使用Ollama

```python
# 实时流式输出
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="详细解释LSTM在工业预测中的优势和局限性",
    stream=True
)

print("🤖 AI回答:")
for chunk in client.generate(request):
    print(chunk, end="", flush=True)
print("\n")
```

#### 使用SiliconFlow

```python
from industrytslib.utils.llm import sf_quick_generate

# 实时流式输出
print("🤖 AI回答:")
for chunk in sf_quick_generate(
    prompt="详细解释LSTM在工业预测中的优势和局限性",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    stream=True
):
    print(chunk, end="", flush=True)
print("\n")
```

### 聊天对话

#### 使用Ollama

```python
# 多轮对话
messages = [
    {
        "role": "system",
        "content": "你是一个专业的工业AI助手,专门帮助解决时间序列预测问题。"
    },
    {
        "role": "user",
        "content": "我的LSTM模型在验证集上表现很好,但在测试集上性能下降,可能是什么原因？"
    }
]

request = OllamaRequest(
    model="qwen2.5:7b",
    messages=messages,
    stream=False
)

response = client.chat(request)
print(response)
```

#### 使用SiliconFlow

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowRequest, SiliconFlowMessage

client = SiliconFlowClient(api_key="your-api-key-here")

# 多轮对话
messages = [
    SiliconFlowMessage(
        role="system",
        content="你是一个专业的工业AI助手,专门帮助解决时间序列预测问题。"
    ),
    SiliconFlowMessage(
        role="user",
        content="我的LSTM模型在验证集上表现很好,但在测试集上性能下降,可能是什么原因？"
    )
]

request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=messages,
    stream=False
)

response = client.chat_completions(request)
print(response.choices[0].message.content)

# 继续对话
messages.append(SiliconFlowMessage(
    role="assistant",
    content=response.choices[0].message.content
))
messages.append(SiliconFlowMessage(
    role="user",
    content="那我应该如何改进模型的泛化能力？"
))

# 发送新的请求
request.messages = messages
response = client.chat_completions(request)
print(response.choices[0].message.content)
```

## 🔧 常用配置

### Ollama配置

#### 连接远程服务器

```python
from industrytslib.utils.llm import create_ollama_client

# 连接远程Ollama服务器
client = create_ollama_client(
    host="*************",  # 远程服务器IP
    port=11434,
    timeout=60
)
```

### SiliconFlow配置

#### 客户端配置

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowConfig

# 基础配置
config = SiliconFlowConfig(
    api_key="your-api-key-here",
    base_url="https://api.siliconflow.cn/v1",  # 可选,默认值
    timeout=60,
    max_retries=3,
    retry_delay=1.0
)

client = SiliconFlowClient(config=config)
```

#### 环境变量配置

```python
import os
from industrytslib.utils.llm import create_siliconflow_client

# 设置环境变量
os.environ["SILICONFLOW_API_KEY"] = "your-api-key-here"

# 自动从环境变量读取配置
client = create_siliconflow_client()
```

### 模型参数调优

#### Ollama参数配置

```python
# 不同场景的参数配置

# 专业技术分析(高准确性)
technical_options = {
    "temperature": 0.1,  # 极低随机性
    "top_p": 0.7,
    "num_predict": 300
}

# 创意性建议(平衡性)
creative_options = {
    "temperature": 0.5,
    "top_p": 0.9,
    "num_predict": 400
}

# 快速响应(限制长度)
quick_options = {
    "temperature": 0.3,
    "top_p": 0.8,
    "num_predict": 150
}

# 使用参数
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析工业数据趋势",
    options=technical_options
)
```

#### SiliconFlow参数配置

```python
from industrytslib.utils.llm import sf_quick_generate

# 专业技术分析(高准确性)
response = sf_quick_generate(
    prompt="分析工业数据趋势",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    temperature=0.1,  # 极低随机性
    top_p=0.7,
    max_tokens=300,
    stream=False
)

# 推理模型配置(更深入分析)
reasoning_response = sf_quick_generate(
    prompt="详细分析LSTM模型的优化策略",
    model="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
    api_key="your-api-key-here",
    temperature=0.05,  # 推理模型使用更低温度
    max_tokens=1000,
    stream=False
)

# 创意性建议(平衡性)
creative_response = sf_quick_generate(
    prompt="为工业4.0提出创新解决方案",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    temperature=0.7,
    top_p=0.9,
    max_tokens=500,
    stream=False
 )

## 🛠️ 实用工具函数

### 检查连接状态

#### Ollama状态检查

```python
def check_ollama_status():
    """检查Ollama服务状态"""
    try:
        from industrytslib.utils.llm import OllamaClient
        client = OllamaClient()
        models = client.list_models()
        print(f"✅ Ollama服务正常,可用模型: {len(models)} 个")
        for model in models:
            print(f"  - {model}")
        return True
    except Exception as e:
        print(f"❌ Ollama服务异常: {e}")
        return False

# 使用
check_ollama_status()
```

#### SiliconFlow状态检查

```python
def check_siliconflow_status(api_key):
    """检查SiliconFlow服务状态"""
    try:
        from industrytslib.utils.llm import sf_quick_generate, get_supported_models
        
        # 测试连接
        response = sf_quick_generate(
            prompt="Hello",
            model="Qwen/Qwen2.5-7B-Instruct",
            api_key=api_key,
            max_tokens=10,
            stream=False
        )
        
        # 获取支持的模型
        models = get_supported_models()
        print(f"✅ SiliconFlow服务正常,支持模型: {len(models)} 个")
        print(f"测试响应: {response[:50]}...")
        return True
    except Exception as e:
        print(f"❌ SiliconFlow服务异常: {e}")
        return False

# 使用
check_siliconflow_status("your-api-key-here")
```

### 批量分析

#### 使用Ollama

```python
import asyncio
from industrytslib.utils.llm import OllamaClient, OllamaRequest

async def batch_analysis_ollama(questions):
    """使用Ollama批量分析多个问题"""
    client = OllamaClient()
    tasks = []
    
    for question in questions:
        request = OllamaRequest(
            model="qwen2.5:7b",
            prompt=question,
            stream=False,
            options={"temperature": 0.2}
        )
        task = client.async_generate(request)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results
```

#### 使用SiliconFlow

```python
import asyncio
from industrytslib.utils.llm import sf_async_quick_generate

async def batch_analysis_siliconflow(questions, api_key):
    """使用SiliconFlow批量分析多个问题"""
    tasks = []
    
    for question in questions:
        task = sf_async_quick_generate(
            prompt=question,
            model="Qwen/Qwen2.5-7B-Instruct",
            api_key=api_key,
            temperature=0.2,
            max_tokens=500,
            stream=False
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results

# 使用示例
questions = [
    "解释传感器数据预处理的重要性",
    "分析工业4.0中的数字化转型",
    "说明预测性维护的核心技术"
]

# 选择使用Ollama或SiliconFlow
# results = asyncio.run(batch_analysis_ollama(questions))
results = asyncio.run(batch_analysis_siliconflow(questions, "your-api-key-here"))

for i, result in enumerate(results):
    print(f"问题 {i+1} 的回答: {result[:100]}...")
```

## 🚨 常见问题解决

### Ollama相关问题

#### 连接失败

```bash
# 检查Ollama服务是否运行
ps aux | grep ollama

# 重启Ollama服务
killall ollama
ollama serve
```

#### 模型不存在

```bash
# 查看可用模型
ollama list

# 下载推荐模型
ollama pull qwen2.5:7b
ollama pull llama3.1:latest
```

### SiliconFlow相关问题

#### API密钥错误

```python
# 检查API密钥是否正确
from industrytslib.utils.llm import sf_quick_generate

try:
    response = sf_quick_generate(
        prompt="test",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key-here",
        max_tokens=5
    )
    print("✅ API密钥有效")
except Exception as e:
    if "401" in str(e) or "Unauthorized" in str(e):
        print("❌ API密钥无效,请检查密钥是否正确")
    else:
        print(f"❌ 其他错误: {e}")
```

#### 模型不支持

```python
from industrytslib.utils.llm import get_supported_models, is_reasoning_model

# 查看支持的模型
models = get_supported_models()
print("支持的模型:")
for model in models[:10]:  # 显示前10个
    reasoning = "(推理模型)" if is_reasoning_model(model) else ""
    print(f"  - {model} {reasoning}")
```

#### 请求频率限制

```python
import time
from industrytslib.utils.llm import sf_quick_generate

def safe_generate(prompt, model, api_key, **kwargs):
    """带重试的安全生成函数"""
    max_retries = 3
    for i in range(max_retries):
        try:
            return sf_quick_generate(
                prompt=prompt,
                model=model,
                api_key=api_key,
                **kwargs
            )
        except Exception as e:
            if "rate limit" in str(e).lower() and i < max_retries - 1:
                wait_time = (i + 1) * 2  # 指数退避
                print(f"遇到频率限制,等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise e
    return None
```

### 通用性能优化

#### 响应太慢

```python
# Ollama优化
request = OllamaRequest(
    model="qwen2.5:7b",  # 或使用更小的模型
    prompt="你的问题",
    options={
        "num_predict": 100,  # 限制输出长度
        "temperature": 0.3
    }
)

# SiliconFlow优化
response = sf_quick_generate(
    prompt="你的问题",
    model="Qwen/Qwen2.5-7B-Instruct",  # 选择合适的模型大小
    api_key="your-api-key-here",
    max_tokens=100,  # 限制输出长度
    temperature=0.3,
    stream=True  # 使用流式输出提升用户体验
)
```

## 📚 下一步

- 查看[完整教程](index.md)了解更多高级功能
- 运行`examples/experiment/exp_llm/`中的示例代码
- 查看[API参考](index.md#api参考)了解详细接口

## 💡 最佳实践提示

### 通用建议
1. **专业分析使用低temperature**(0.1-0.3)
2. **创意性任务使用中等temperature**(0.5-0.7)
3. **生产环境启用错误重试机制**
4. **大批量任务使用异步处理**
5. **频繁调用考虑启用缓存**

### Ollama特定建议
6. **本地部署确保足够的GPU内存**
7. **多用户场景考虑模型并发限制**
8. **定期更新模型版本获得更好性能**

### SiliconFlow特定建议
9. **推理任务优先选择推理模型**(DeepSeek-R1、QwQ等)
10. **API密钥安全存储,避免硬编码**
11. **监控API使用量,避免超出配额**
12. **网络不稳定时启用重试机制**
13. **流式输出提升用户体验**

---

🎉 恭喜！你已经掌握了industrytslib LLM模块的基础使用。现在可以开始在你的工业AI项目中集成智能对话功能了！