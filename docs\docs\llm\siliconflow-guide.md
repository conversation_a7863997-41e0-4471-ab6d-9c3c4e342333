# SiliconFlow客户端详细指南

本指南详细介绍如何使用industrytslib中的SiliconFlow客户端,包括配置、使用方法、最佳实践和故障排除。

## 📋 目录

- [快速开始](#快速开始)
- [配置选项](#配置选项)
- [基础使用](#基础使用)
- [高级功能](#高级功能)
- [支持的模型](#支持的模型)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [API参考](#api参考)

## <a name="快速开始"></a>🚀 快速开始

### 1. 获取API密钥

1. 访问 [SiliconFlow官网](https://siliconflow.cn)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 保存密钥到安全位置

### 2. 基础配置

```python
from industrytslib.utils.llm import sf_quick_generate

# 最简单的使用方式
response = sf_quick_generate(
    prompt="请介绍工业4.0的核心技术",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here"
)
print(response)
```

## <a name="配置选项"></a>⚙️ 配置选项

### 环境变量配置

```bash
# 设置环境变量(推荐)
export SILICONFLOW_API_KEY="your-api-key-here"
export SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"  # 可选
```

### 代码配置

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowConfig

# 详细配置
config = SiliconFlowConfig(
    api_key="your-api-key-here",
    base_url="https://api.siliconflow.cn/v1",  # 默认值
    timeout=60,  # 请求超时时间(秒)
    max_retries=3,  # 最大重试次数
    retry_delay=1.0  # 重试延迟(秒)
)

client = SiliconFlowClient(config=config)
```

## <a name="基础使用"></a>📖 基础使用

### 快速文本生成

```python
from industrytslib.utils.llm import sf_quick_generate

# 非流式生成
response = sf_quick_generate(
    prompt="分析化工过程中的温度控制策略",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    temperature=0.2,
    max_tokens=500,
    stream=False
)
print(response)

# 流式生成
print("AI回答:")
for chunk in sf_quick_generate(
    prompt="详细解释LSTM在时间序列预测中的应用",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key-here",
    stream=True
):
    print(chunk, end="", flush=True)
print("\n")
```

### 聊天对话

```python
from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowRequest, SiliconFlowMessage

client = SiliconFlowClient(api_key="your-api-key-here")

# 构建对话
messages = [
    SiliconFlowMessage(
        role="system",
        content="你是一个专业的工业AI助手,专门帮助解决时间序列预测和工艺优化问题。"
    ),
    SiliconFlowMessage(
        role="user",
        content="我的预测模型在训练集上表现很好,但在测试集上效果不佳,可能是什么原因？"
    )
]

# 发送请求
request = SiliconFlowRequest(
    model="Qwen/Qwen2.5-7B-Instruct",
    messages=messages,
    temperature=0.3,
    max_tokens=800,
    stream=False
)

response = client.chat_completions(request)
print(response.choices[0].message.content)

# 继续对话
messages.append(SiliconFlowMessage(
    role="assistant",
    content=response.choices[0].message.content
))
messages.append(SiliconFlowMessage(
    role="user",
    content="那我应该如何改进模型的泛化能力？"
))

# 更新请求并发送
request.messages = messages
response = client.chat_completions(request)
print(response.choices[0].message.content)
```

## <a name="高级功能"></a>🔬 高级功能

### 异步处理

```python
import asyncio
from industrytslib.utils.llm import sf_async_quick_generate

async def async_analysis():
    """异步分析示例"""
    tasks = []
    
    questions = [
        "解释PID控制器的工作原理",
        "分析工业物联网的安全挑战",
        "描述预测性维护的实施步骤"
    ]
    
    for question in questions:
        task = sf_async_quick_generate(
            prompt=question,
            model="Qwen/Qwen2.5-7B-Instruct",
            api_key="your-api-key-here",
            temperature=0.2,
            max_tokens=300
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results

# 运行异步任务
results = asyncio.run(async_analysis())
for i, result in enumerate(results):
    print(f"问题 {i+1} 的回答: {result[:100]}...\n")
```

### 推理模型使用

```python
from industrytslib.utils.llm import sf_quick_generate, is_reasoning_model

# 使用推理模型进行深度分析
reasoning_model = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
print(f"是否为推理模型: {is_reasoning_model(reasoning_model)}")

response = sf_quick_generate(
    prompt="""
    请深入分析以下工业场景:
    一个化工反应器的温度控制系统出现异常,温度波动范围从正常的±2°C增加到±8°C。
    传感器数据显示没有硬件故障,PID参数也没有改变。
    请分析可能的原因并提出解决方案。
    """,
    model=reasoning_model,
    api_key="your-api-key-here",
    temperature=0.1,  # 推理模型使用更低的温度
    max_tokens=1500,
    stream=False
)
print(response)
```

### 上下文管理器

```python
from industrytslib.utils.llm import SiliconFlowClient

# 使用上下文管理器自动管理资源
with SiliconFlowClient(api_key="your-api-key-here") as client:
    request = SiliconFlowRequest(
        model="Qwen/Qwen2.5-7B-Instruct",
        messages=[
            SiliconFlowMessage(
                role="user",
                content="解释机器学习在工业预测中的应用"
            )
        ],
        temperature=0.3
    )
    
    response = client.chat_completions(request)
    print(response.choices[0].message.content)
```

## <a name="支持的模型"></a>🤖 支持的模型

### 获取模型列表

```python
from industrytslib.utils.llm import get_supported_models, is_reasoning_model

# 获取所有支持的模型
models = get_supported_models()
print(f"总共支持 {len(models)} 个模型")

# 分类显示
reasoning_models = []
regular_models = []

for model in models:
    if is_reasoning_model(model):
        reasoning_models.append(model)
    else:
        regular_models.append(model)

print(f"\n推理模型 ({len(reasoning_models)} 个):")
for model in reasoning_models[:5]:  # 显示前5个
    print(f"  - {model}")

print(f"\n常规模型 ({len(regular_models)} 个):")
for model in regular_models[:10]:  # 显示前10个
    print(f"  - {model}")
```

### 主要模型系列

| 模型系列 | 特点 | 推荐用途 |
|---------|------|----------|
| **Qwen系列** | 通用能力强,中文优化 | 工业分析、技术咨询 |
| **DeepSeek系列** | 代码能力强,推理能力优秀 | 算法设计、深度分析 |
| **GLM系列** | 对话能力强,多轮交互 | 技术支持、问答系统 |
| **MiniMax系列** | 创意能力强,文本生成 | 报告生成、方案设计 |
| **推理模型** | 逻辑推理能力强 | 复杂问题分析、决策支持 |

## <a name="最佳实践"></a>💡 最佳实践

### 1. 安全性

```python
import os
from industrytslib.utils.llm import create_siliconflow_client

# ✅ 推荐:使用环境变量
os.environ["SILICONFLOW_API_KEY"] = "your-api-key-here"
client = create_siliconflow_client()

# ❌ 不推荐:硬编码API密钥
# client = SiliconFlowClient(api_key="sk-xxx")  # 避免这样做
```

### 2. 错误处理

```python
from industrytslib.utils.llm import sf_quick_generate, SiliconFlowError
import time

def robust_generate(prompt, model, api_key, max_retries=3, **kwargs):
    """带错误处理的生成函数"""
    for attempt in range(max_retries):
        try:
            return sf_quick_generate(
                prompt=prompt,
                model=model,
                api_key=api_key,
                **kwargs
            )
        except SiliconFlowError as e:
            if "rate limit" in str(e).lower() and attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2
                print(f"遇到频率限制,等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise e
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            print(f"请求失败,重试中... ({attempt + 1}/{max_retries})")
            time.sleep(1)
    
    return None
```

### 3. 参数优化

```python
# 不同场景的参数配置

# 专业技术分析
technical_params = {
    "temperature": 0.1,
    "top_p": 0.7,
    "max_tokens": 800
}

# 创意性任务
creative_params = {
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 1000
}

# 推理任务
reasoning_params = {
    "temperature": 0.05,  # 极低温度
    "top_p": 0.6,
    "max_tokens": 1500
}

# 快速响应
quick_params = {
    "temperature": 0.3,
    "max_tokens": 200
}
```

### 4. 性能优化

```python
import asyncio
from industrytslib.utils.llm import sf_async_quick_generate

async def batch_process(prompts, model, api_key, batch_size=5):
    """批量处理,控制并发数"""
    results = []
    
    for i in range(0, len(prompts), batch_size):
        batch = prompts[i:i + batch_size]
        tasks = []
        
        for prompt in batch:
            task = sf_async_quick_generate(
                prompt=prompt,
                model=model,
                api_key=api_key,
                temperature=0.2,
                max_tokens=300
            )
            tasks.append(task)
        
        batch_results = await asyncio.gather(*tasks)
        results.extend(batch_results)
        
        # 批次间延迟,避免频率限制
        if i + batch_size < len(prompts):
            await asyncio.sleep(1)
    
    return results
```

## <a name="故障排除"></a>🔧 故障排除

### 常见错误及解决方案

#### 1. 认证错误 (401)

```python
# 错误信息:Unauthorized
# 解决方案:检查API密钥

from industrytslib.utils.llm import sf_quick_generate

try:
    response = sf_quick_generate(
        prompt="test",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key-here",
        max_tokens=5
    )
    print("✅ API密钥有效")
except Exception as e:
    if "401" in str(e):
        print("❌ API密钥无效,请检查:")
        print("  1. 密钥是否正确")
        print("  2. 密钥是否已激活")
        print("  3. 账户是否有余额")
    else:
        print(f"其他错误: {e}")
```

#### 2. 频率限制 (429)

```python
# 错误信息:Rate limit exceeded
# 解决方案:实现重试机制

import time
import random

def exponential_backoff(attempt, base_delay=1, max_delay=60):
    """指数退避算法"""
    delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
    return delay

def generate_with_backoff(prompt, model, api_key, max_retries=5):
    for attempt in range(max_retries):
        try:
            return sf_quick_generate(
                prompt=prompt,
                model=model,
                api_key=api_key
            )
        except Exception as e:
            if "rate limit" in str(e).lower() and attempt < max_retries - 1:
                delay = exponential_backoff(attempt)
                print(f"频率限制,等待 {delay:.1f} 秒...")
                time.sleep(delay)
            else:
                raise e
    return None
```

#### 3. 模型不存在 (404)

```python
# 错误信息:Model not found
# 解决方案:检查模型名称

from industrytslib.utils.llm import get_supported_models

def validate_model(model_name):
    """验证模型是否支持"""
    supported_models = get_supported_models()
    
    if model_name in supported_models:
        print(f"✅ 模型 {model_name} 支持")
        return True
    else:
        print(f"❌ 模型 {model_name} 不支持")
        print("支持的类似模型:")
        
        # 查找相似模型
        similar = [m for m in supported_models if model_name.split('/')[0] in m]
        for model in similar[:5]:
            print(f"  - {model}")
        
        return False

# 使用示例
validate_model("Qwen/Qwen2.5-7B-Instruct")
validate_model("invalid-model-name")
```

#### 4. 网络连接问题

```python
# 解决方案:增加超时和重试

from industrytslib.utils.llm import SiliconFlowClient, SiliconFlowConfig

# 配置更长的超时时间
config = SiliconFlowConfig(
    api_key="your-api-key-here",
    timeout=120,  # 增加超时时间
    max_retries=5,  # 增加重试次数
    retry_delay=2.0  # 增加重试延迟
)

client = SiliconFlowClient(config=config)
```

### 调试技巧

```python
import logging
from industrytslib.utils.llm import sf_quick_generate

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

# 测试连接
def debug_connection(api_key):
    """调试连接问题"""
    try:
        print("🔍 测试基础连接...")
        response = sf_quick_generate(
            prompt="Hello",
            model="Qwen/Qwen2.5-7B-Instruct",
            api_key=api_key,
            max_tokens=5,
            stream=False
        )
        print(f"✅ 连接成功,响应: {response}")
        
        print("🔍 测试流式输出...")
        chunks = list(sf_quick_generate(
            prompt="Count to 3",
            model="Qwen/Qwen2.5-7B-Instruct",
            api_key=api_key,
            max_tokens=10,
            stream=True
        ))
        print(f"✅ 流式输出成功,收到 {len(chunks)} 个块")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

# 运行调试
debug_connection("your-api-key-here")
```

## <a name="api参考"></a>📚 API参考

### 核心类

#### SiliconFlowClient

```python
class SiliconFlowClient:
    """SiliconFlow API客户端"""
    
    def __init__(self, api_key: str = None, config: SiliconFlowConfig = None):
        """初始化客户端"""
        pass
    
    def chat_completions(self, request: SiliconFlowRequest) -> SiliconFlowResponse:
        """同步聊天完成"""
        pass
    
    async def async_chat_completions(self, request: SiliconFlowRequest) -> SiliconFlowResponse:
        """异步聊天完成"""
        pass
    
    def generate(self, prompt: str, model: str, **kwargs) -> str:
        """便捷文本生成"""
        pass
    
    async def async_generate(self, prompt: str, model: str, **kwargs) -> str:
        """异步文本生成"""
        pass
```

#### SiliconFlowConfig

```python
class SiliconFlowConfig:
    """SiliconFlow配置"""
    
    api_key: str  # API密钥
    base_url: str = "https://api.siliconflow.cn/v1"  # 基础URL
    timeout: int = 60  # 超时时间(秒)
    max_retries: int = 3  # 最大重试次数
    retry_delay: float = 1.0  # 重试延迟(秒)
```

#### SiliconFlowRequest

```python
class SiliconFlowRequest:
    """SiliconFlow请求"""
    
    model: str  # 模型名称
    messages: List[SiliconFlowMessage]  # 消息列表
    temperature: float = 0.7  # 温度参数
    top_p: float = 1.0  # Top-p参数
    max_tokens: int = 1024  # 最大令牌数
    stream: bool = False  # 是否流式输出
    stop: List[str] = None  # 停止词
```

### 便捷函数

```python
# 快速生成
sf_quick_generate(
    prompt: str,
    model: str,
    api_key: str = None,
    temperature: float = 0.7,
    top_p: float = 1.0,
    max_tokens: int = 1024,
    stream: bool = False,
    **kwargs
) -> Union[str, Iterator[str]]

# 异步快速生成
async sf_async_quick_generate(
    prompt: str,
    model: str,
    api_key: str = None,
    **kwargs
) -> Union[str, AsyncIterator[str]]

# 创建客户端
create_siliconflow_client(
    api_key: str = None,
    **config_kwargs
) -> SiliconFlowClient

# 获取支持的模型
get_supported_models() -> List[str]

# 判断是否为推理模型
is_reasoning_model(model: str) -> bool
```

---

🎯 **总结**:SiliconFlow客户端为工业AI应用提供了强大的云端LLM能力,支持多种模型、流式输出、异步处理等高级功能。通过合理配置和最佳实践,可以构建稳定、高效的智能应用系统。