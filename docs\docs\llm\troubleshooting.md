# 故障排除指南

本指南帮助您解决使用industrytslib LLM功能时可能遇到的常见问题。

## 📋 目录

- [连接问题](#连接问题)
- [性能问题](#性能问题)
- [模型问题](#模型问题)
- [数据处理问题](#数据处理问题)
- [错误代码参考](#错误代码参考)
- [调试技巧](#调试技巧)
- [常见问题FAQ](#常见问题faq)

## <a name="连接问题"></a>连接问题

### 问题1:无法连接到Ollama服务器

**症状**:
```
ConnectionError: Failed to connect to Ollama server at http://localhost:11434
```

**可能原因和解决方案**:

1. **Ollama服务未启动**
   ```bash
   # 检查Ollama是否运行
   ps aux | grep ollama
   
   # 启动Ollama服务
   ollama serve
   ```

2. **端口被占用或配置错误**
   ```python
   # 检查端口是否可用
   import socket
   
   def check_port(host, port):
       sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
       result = sock.connect_ex((host, port))
       sock.close()
       return result == 0
   
   print(f"Port 11434 available: {check_port('localhost', 11434)}")
   
   # 使用自定义配置
   from industrytslib.utils.llm import OllamaConfig, OllamaClient
   
   config = OllamaConfig(
       host="*************",  # 远程服务器地址
       port=11434,
       timeout=30
   )
   client = OllamaClient(config)
   ```

3. **防火墙阻止连接**
   ```bash
   # Linux检查防火墙状态
   sudo ufw status
   
   # 允许端口11434
   sudo ufw allow 11434
   
   # Windows检查防火墙
   netsh advfirewall firewall show rule name="Ollama"
   ```

4. **网络代理问题**
   ```python
   # 设置代理(如果需要)
   import os
   os.environ['HTTP_PROXY'] = 'http://proxy.company.com:8080'
   os.environ['HTTPS_PROXY'] = 'http://proxy.company.com:8080'
   
   # 或在代码中配置
   config = OllamaConfig(
       host="localhost",
       port=11434,
       proxies={
           'http': 'http://proxy.company.com:8080',
           'https': 'http://proxy.company.com:8080'
       }
   )
   ```

### 问题2:连接超时

**症状**:
```
TimeoutError: Request timed out after 30 seconds
```

**解决方案**:

```python
# 增加超时时间
config = OllamaConfig(
    host="localhost",
    port=11434,
    timeout=120  # 增加到120秒
)

# 或在请求中设置
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析数据...",
    options={
        "timeout": 180  # 3分钟超时
    }
)
```

### 问题3:SSL证书验证失败

**症状**:
```
SSLError: certificate verify failed
```

**解决方案**:

```python
# 禁用SSL验证(仅用于开发环境)
config = OllamaConfig(
    host="https://ollama.company.com",
    port=443,
    verify_ssl=False
)

# 或指定证书文件
config = OllamaConfig(
    host="https://ollama.company.com",
    port=443,
    ssl_cert_path="/path/to/certificate.pem"
)
```

## <a name="性能问题"></a>性能问题

### 问题1:响应速度慢

**症状**:LLM响应时间超过预期

**诊断和优化**:

```python
import time
from industrytslib.utils.llm import OllamaClient, OllamaRequest

def benchmark_performance():
    """性能基准测试"""
    client = OllamaClient()
    
    # 测试不同模型大小
    models = ["qwen2.5:7b", "llama3.1:latest"]
    prompts = [
        "简单分析:当前温度是25°C",
        "复杂分析:" + "数据点," * 100  # 长提示词
    ]
    
    for model in models:
        for i, prompt in enumerate(prompts):
            start_time = time.time()
            
            request = OllamaRequest(
                model=model,
                prompt=prompt,
                stream=False,
                options={
                    "num_predict": 100 if i == 0 else 500,
                    "temperature": 0.1
                }
            )
            
            try:
                response = client.generate(request)
                end_time = time.time()
                
                print(f"模型: {model}")
                print(f"提示词长度: {len(prompt)}")
                print(f"响应时间: {end_time - start_time:.2f}秒")
                print(f"响应长度: {len(response)}")
                print("-" * 50)
                
            except Exception as e:
                print(f"错误: {e}")

# 运行基准测试
benchmark_performance()
```

**优化策略**:

1. **选择合适的模型大小**
   ```python
   # 对于简单任务使用小模型
   simple_request = OllamaRequest(
       model="qwen2.5:1.5b",  # 更小的模型
       prompt="简单分析任务",
       options={"num_predict": 100}
   )
   
   # 对于复杂任务使用大模型
   complex_request = OllamaRequest(
       model="qwen2.5:14b",  # 更大的模型
       prompt="复杂分析任务",
       options={"num_predict": 500}
   )
   ```

2. **优化提示词长度**
   ```python
   # 避免过长的提示词
   def optimize_prompt(data_dict, max_length=2000):
       """优化提示词长度"""
       prompt = f"分析以下数据:{data_dict}"
       
       if len(prompt) > max_length:
           # 截断或摘要数据
           summary = {
               "数据点数量": len(data_dict),
               "主要指标": list(data_dict.keys())[:5],
               "数值范围": "详见附件"
           }
           prompt = f"分析以下数据摘要:{summary}"
       
       return prompt
   ```

3. **使用流式输出**
   ```python
   # 流式输出可以更快看到初始结果
   request = OllamaRequest(
       model="qwen2.5:7b",
       prompt="分析工业数据...",
       stream=True
   )
   
   print("分析结果:")
   for chunk in client.generate(request):
       print(chunk, end="", flush=True)
   ```

### 问题2:内存使用过高

**症状**:系统内存占用过高,可能导致OOM错误

**解决方案**:

```python
# 监控内存使用
import psutil
import gc

def monitor_memory_usage():
    """监控内存使用情况"""
    process = psutil.Process()
    memory_info = process.memory_info()
    
    print(f"RSS内存: {memory_info.rss / 1024 / 1024:.2f} MB")
    print(f"VMS内存: {memory_info.vms / 1024 / 1024:.2f} MB")
    print(f"内存百分比: {process.memory_percent():.2f}%")

# 在LLM调用前后监控
monitor_memory_usage()
response = client.generate(request)
monitor_memory_usage()

# 手动垃圾回收
gc.collect()
```

**内存优化策略**:

```python
class MemoryOptimizedLLMClient:
    """内存优化的LLM客户端"""
    
    def __init__(self, max_cache_size=100):
        self.client = OllamaClient()
        self.response_cache = {}
        self.max_cache_size = max_cache_size
    
    def generate_with_cache(self, request):
        """带缓存的生成"""
        # 创建缓存键
        cache_key = f"{request.model}_{hash(request.prompt)}_{request.options}"
        
        # 检查缓存
        if cache_key in self.response_cache:
            return self.response_cache[cache_key]
        
        # 生成响应
        response = self.client.generate(request)
        
        # 缓存管理
        if len(self.response_cache) >= self.max_cache_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self.response_cache))
            del self.response_cache[oldest_key]
        
        self.response_cache[cache_key] = response
        return response
    
    def clear_cache(self):
        """清理缓存"""
        self.response_cache.clear()
        gc.collect()
```

## <a name="模型问题"></a>模型问题

### 问题1:模型未找到

**症状**:
```
ModelNotFoundError: Model 'qwen2.5:7b' not found
```

**解决方案**:

```python
# 检查可用模型
def check_available_models():
    """检查可用模型"""
    client = OllamaClient()
    
    try:
        models = client.list_models()
        print("可用模型:")
        for model in models:
            print(f"- {model['name']} ({model['size']})")
        return models
    except Exception as e:
        print(f"获取模型列表失败: {e}")
        return []

# 自动下载模型
def ensure_model_available(model_name):
    """确保模型可用"""
    client = OllamaClient()
    
    try:
        models = client.list_models()
        model_names = [m['name'] for m in models]
        
        if model_name not in model_names:
            print(f"模型 {model_name} 不存在,尝试下载...")
            # 注意:实际的下载需要使用ollama命令行工具
            print(f"请运行: ollama pull {model_name}")
            return False
        
        return True
    except Exception as e:
        print(f"检查模型失败: {e}")
        return False

# 使用示例
if ensure_model_available("qwen2.5:7b"):
    request = OllamaRequest(model="qwen2.5:7b", prompt="测试")
    response = client.generate(request)
else:
    print("请先下载所需模型")
```

### 问题2:模型响应质量差

**症状**:模型输出不符合预期,质量较低

**优化策略**:

```python
def optimize_model_parameters():
    """优化模型参数"""
    
    # 针对不同任务的参数配置
    task_configs = {
        "数据分析": {
            "temperature": 0.1,  # 低随机性,确保准确性
            "top_p": 0.8,
            "top_k": 20,
            "repeat_penalty": 1.1
        },
        "创意生成": {
            "temperature": 0.7,  # 高随机性,增加创意
            "top_p": 0.9,
            "top_k": 40,
            "repeat_penalty": 1.0
        },
        "技术文档": {
            "temperature": 0.2,  # 中低随机性
            "top_p": 0.85,
            "top_k": 30,
            "repeat_penalty": 1.05
        }
    }
    
    return task_configs

# 使用优化参数
task_type = "数据分析"
optimal_params = optimize_model_parameters()[task_type]

request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="分析以下工业数据...",
    options=optimal_params
)
```

**提示词工程优化**:

```python
def create_optimized_prompt(task, data, context=None):
    """创建优化的提示词"""
    
    # 基础模板
    base_template = """
    你是一个专业的工业数据分析专家,具有以下专长:
    - 时间序列分析
    - 工艺优化
    - 异常检测
    - 质量控制
    
    任务:{task}
    
    数据:
    {data}
    
    {context_section}
    
    请提供:
    1. 详细的分析结果
    2. 具体的数值和指标
    3. 实用的建议和措施
    4. 风险评估和注意事项
    
    分析结果:
    """
    
    # 添加上下文信息
    context_section = ""
    if context:
        context_section = f"""
        背景信息:
        {context}
        """
    
    return base_template.format(
        task=task,
        data=data,
        context_section=context_section
    ).strip()

# 使用示例
optimized_prompt = create_optimized_prompt(
    task="分析反应器温度异常",
    data="温度: 215°C (正常范围: 180-200°C)",
    context="化工聚合反应过程,刚完成设备维护"
)

request = OllamaRequest(
    model="qwen2.5:7b",
    prompt=optimized_prompt,
    options={"temperature": 0.1, "num_predict": 400}
)
```

## <a name="数据处理问题"></a>数据处理问题

### 问题1:数据格式错误

**症状**:
```
ValidationError: Invalid data format in request
```

**解决方案**:

```python
import json
from pydantic import ValidationError

def validate_and_format_data(data):
    """验证和格式化数据"""
    try:
        # 确保数据可以JSON序列化
        json_str = json.dumps(data, ensure_ascii=False, default=str)
        
        # 检查数据大小
        if len(json_str) > 10000:  # 10KB限制
            print("警告:数据过大,建议压缩")
            # 数据压缩逻辑
            data = compress_data(data)
        
        return data
    
    except (TypeError, ValueError) as e:
        print(f"数据格式错误: {e}")
        return None

def compress_data(data):
    """压缩数据"""
    if isinstance(data, dict):
        # 只保留关键字段
        key_fields = ['timestamp', 'value', 'status', 'alarm']
        compressed = {k: v for k, v in data.items() if k in key_fields}
        return compressed
    elif isinstance(data, list):
        # 采样数据点
        if len(data) > 100:
            step = len(data) // 100
            return data[::step]
    return data

# 使用示例
raw_data = {
    "sensor_readings": [1, 2, 3] * 1000,  # 大量数据
    "metadata": {"location": "reactor_1"}
}

validated_data = validate_and_format_data(raw_data)
if validated_data:
    prompt = f"分析数据:{validated_data}"
else:
    print("数据验证失败")
```

### 问题2:中文编码问题

**症状**:中文字符显示为乱码或错误

**解决方案**:

```python
import json

def handle_chinese_encoding(text):
    """处理中文编码"""
    try:
        # 确保文本是UTF-8编码
        if isinstance(text, bytes):
            text = text.decode('utf-8')
        
        # 验证JSON序列化
        json.dumps(text, ensure_ascii=False)
        
        return text
    except UnicodeDecodeError:
        print("编码错误,尝试其他编码")
        for encoding in ['gbk', 'gb2312', 'latin1']:
            try:
                return text.decode(encoding)
            except:
                continue
        return str(text, errors='ignore')
    except Exception as e:
        print(f"处理编码错误: {e}")
        return str(text)

# 创建支持中文的请求
def create_chinese_request(prompt_text):
    """创建支持中文的请求"""
    # 处理中文编码
    clean_prompt = handle_chinese_encoding(prompt_text)
    
    request = OllamaRequest(
        model="qwen2.5:7b",  # 使用支持中文的模型
        prompt=clean_prompt,
        options={
            "temperature": 0.2,
            "num_predict": 500
        }
    )
    
    return request

# 使用示例
chinese_prompt = "请分析反应器温度数据:温度范围180-220°C,当前值215°C"
request = create_chinese_request(chinese_prompt)
```

## <a name="错误代码参考"></a>错误代码参考

### 连接错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| CONN_001 | 连接超时 | 增加timeout设置,检查网络 |
| CONN_002 | 连接被拒绝 | 检查服务器状态和端口 |
| CONN_003 | DNS解析失败 | 检查主机名和DNS设置 |
| CONN_004 | SSL握手失败 | 检查证书和SSL配置 |

### 请求错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| REQ_001 | 请求格式错误 | 检查OllamaRequest参数 |
| REQ_002 | 模型不存在 | 下载或选择其他模型 |
| REQ_003 | 参数验证失败 | 检查prompt和messages |
| REQ_004 | 请求过大 | 减少数据量或分批处理 |

### 服务器错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| SRV_001 | 内部服务器错误 | 检查服务器日志 |
| SRV_002 | 服务不可用 | 等待服务恢复 |
| SRV_003 | 资源不足 | 释放内存或升级硬件 |
| SRV_004 | 模型加载失败 | 重启服务或重新下载模型 |

## <a name="调试技巧"></a>调试技巧

### 1. 启用详细日志

```python
import logging
from industrytslib.utils.llm import OllamaClient

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 启用LLM模块日志
logger = logging.getLogger('industrytslib.utils.llm')
logger.setLevel(logging.DEBUG)

# 使用客户端
client = OllamaClient()
# 现在会输出详细的调试信息
```

### 2. 请求响应调试

```python
def debug_llm_request(client, request):
    """调试LLM请求"""
    print("=== 请求调试信息 ===")
    print(f"模型: {request.model}")
    print(f"提示词长度: {len(request.prompt)}")
    print(f"流式输出: {request.stream}")
    print(f"选项: {request.options}")
    
    try:
        start_time = time.time()
        response = client.generate(request)
        end_time = time.time()
        
        print(f"\n=== 响应调试信息 ===")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        print(f"响应长度: {len(response)}")
        print(f"响应预览: {response[:200]}...")
        
        return response
    
    except Exception as e:
        print(f"\n=== 错误信息 ===")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误消息: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# 使用调试函数
request = OllamaRequest(
    model="qwen2.5:7b",
    prompt="测试提示词",
    stream=False
)

response = debug_llm_request(client, request)
```

### 3. 网络连接测试

```python
import requests
import socket

def test_network_connectivity(host="localhost", port=11434):
    """测试网络连接"""
    print(f"测试连接到 {host}:{port}")
    
    # 1. TCP连接测试
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✓ TCP连接成功")
        else:
            print("✗ TCP连接失败")
            return False
    except Exception as e:
        print(f"✗ TCP连接错误: {e}")
        return False
    
    # 2. HTTP请求测试
    try:
        url = f"http://{host}:{port}/api/tags"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✓ HTTP请求成功")
            models = response.json().get('models', [])
            print(f"可用模型数量: {len(models)}")
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ HTTP请求错误: {e}")
        return False
    
    return True

# 运行连接测试
if test_network_connectivity():
    print("网络连接正常")
else:
    print("网络连接存在问题")
```

## <a name="常见问题faq"></a>常见问题FAQ

### Q1: 如何选择合适的模型？

**A**: 根据任务复杂度和性能要求选择:

- **简单任务**(数据摘要、状态检查):使用小模型如`qwen2.5:1.5b`
- **中等任务**(数据分析、趋势预测):使用中等模型如`qwen2.5:7b`
- **复杂任务**(深度分析、决策支持):使用大模型如`qwen2.5:14b`

### Q2: 如何提高响应速度？

**A**: 多种优化策略:

1. 使用更小的模型
2. 减少`num_predict`参数
3. 使用流式输出
4. 实施响应缓存
5. 优化提示词长度

### Q3: 如何处理大量数据？

**A**: 数据处理策略:

1. 数据分批处理
2. 数据摘要和压缩
3. 异步处理
4. 使用数据库存储中间结果

### Q4: 如何确保数据安全？

**A**: 安全措施:

1. 数据脱敏处理
2. 使用HTTPS连接
3. 实施访问控制
4. 定期安全审计
5. 遵循数据保护法规

### Q5: 如何监控LLM使用情况？

**A**: 监控方案:

```python
class LLMMonitor:
    """LLM使用监控"""
    
    def __init__(self):
        self.request_count = 0
        self.total_tokens = 0
        self.error_count = 0
        self.response_times = []
    
    def log_request(self, request, response_time, success=True):
        """记录请求"""
        self.request_count += 1
        self.response_times.append(response_time)
        
        if not success:
            self.error_count += 1
        
        # 估算token数量
        estimated_tokens = len(request.prompt) // 4
        self.total_tokens += estimated_tokens
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.response_times:
            return {}
        
        return {
            "总请求数": self.request_count,
            "错误率": f"{self.error_count/self.request_count*100:.2f}%",
            "平均响应时间": f"{sum(self.response_times)/len(self.response_times):.2f}秒",
            "总Token数": self.total_tokens
        }

# 使用监控
monitor = LLMMonitor()

# 在每次请求后记录
start_time = time.time()
try:
    response = client.generate(request)
    monitor.log_request(request, time.time() - start_time, True)
except Exception as e:
    monitor.log_request(request, time.time() - start_time, False)

# 查看统计
print(monitor.get_statistics())
```

---

如果您遇到本指南未涵盖的问题,请:

1. 查看[API参考文档](api-reference.md)
2. 检查[GitHub Issues](https://github.com/your-repo/industrytslib/issues)
3. 提交新的Issue描述问题
4. 联系技术支持团队

*本故障排除指南会根据用户反馈持续更新。*