# 通用LLM客户端使用指南

本指南详细介绍industrytslib中的通用LLM客户端(UniversalLLMClient)的使用方法,这是一个统一的接口,支持多个主流LLM服务提供商。

## 📋 目录

- [概述](#概述)
- [核心特性](#核心特性)
- [快速开始](#快速开始)
- [配置管理](#配置管理)
- [基础使用](#基础使用)
- [高级功能](#高级功能)
- [工业场景应用](#工业场景应用)
- [性能优化](#性能优化)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## <a name="概述"></a>概述

**UniversalLLMClient** 是industrytslib LLM模块的核心组件,提供了统一的接口来访问多个LLM服务提供商。它专门为工业时间序列AI应用场景设计,支持智能分析、异常检测、工艺优化等工业应用。

### 🎯 设计理念

- **统一接口**:所有LLM服务提供商使用相同的API接口
- **运行时切换**:可在程序运行时动态切换不同的服务提供商
- **工业优化**:针对工业场景进行了专门优化
- **类型安全**:完整的类型提示和数据验证
- **生产就绪**:内置错误处理、重试机制和性能优化

## <a name="核心特性"></a>核心特性

### 🚀 多厂商支持

目前支持的LLM服务提供商:

- **SiliconFlow**:云端LLM服务,支持Qwen、DeepSeek、GLM等模型
- **Ollama**:本地部署的开源LLM服务
- **OpenAI**:GPT系列模型(规划中)
- **Claude**:Anthropic的Claude模型(规划中)
- **Gemini**:Google的Gemini模型(规划中)

### 🔧 核心功能

- ✅ **统一接口**:所有厂商使用相同的API
- ✅ **运行时切换**:动态切换LLM服务提供商
- ✅ **配置灵活**:支持环境变量、配置文件等多种配置方式
- ✅ **异步支持**:原生支持异步操作,提高并发性能
- ✅ **流式输出**:支持实时流式文本生成
- ✅ **错误处理**:统一的错误处理和重试机制
- ✅ **类型安全**:完整的类型提示和数据验证
- ✅ **扩展性强**:易于添加新的LLM服务提供商

## <a name="快速开始"></a>快速开始

### 安装依赖

```bash
# 确保已安装industrytslib
uv add industrytslib

# 安装可选依赖
uv add requests pydantic loguru
```

### 第一个调用

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 创建客户端
client = UniversalLLMClient()

# 配置SiliconFlow服务
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

# 创建消息
messages = [
    LLMMessage(role="user", content="请简单介绍工业4.0的核心特征")
]

# 生成响应
response = client.chat_completions(messages)
print(response.content)
```

### 验证连接

```python
from industrytslib.utils.llm import quick_chat

# 快速测试连接
try:
    response = quick_chat(
        messages=[{"role": "user", "content": "Hello!"}],
        provider="siliconflow",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key"
    )
    print("✅ 连接成功！")
except Exception as e:
    print(f"❌ 连接失败: {e}")
```

## <a name="配置管理"></a>配置管理

### 配置文件方式

创建 `llm_config.toml` 配置文件:

```toml
# SiliconFlow配置
[siliconflow]
provider = "siliconflow"
api_key = "your-siliconflow-api-key"
base_url = "https://api.siliconflow.cn/v1"
model = "Qwen/Qwen2.5-7B-Instruct"
timeout = 60
max_retries = 3
retry_delay = 1
temperature = 0.7
max_tokens = 2048

# Ollama配置
[ollama]
provider = "ollama"
host = "localhost"
port = 11434
model = "qwen2.5:7b"
timeout = 60
max_retries = 3

# 默认配置
[default]
provider = "siliconflow"
model = "Qwen/Qwen2.5-7B-Instruct"
temperature = 0.7
max_tokens = 1024

# 工业场景专用配置
[industrial_scenarios.process_analysis]
model = "Qwen/Qwen2.5-7B-Instruct"
temperature = 0.2
max_tokens = 1024
system_prompt = "你是一个专业的工业过程分析专家,能够分析工艺参数、识别瓶颈并提供优化建议。"

[industrial_scenarios.anomaly_detection]
model = "Qwen/Qwen2.5-7B-Instruct"
temperature = 0.1
max_tokens = 512
system_prompt = "你是一个工业异常检测专家,能够识别设备异常、分析故障原因并提供解决方案。"

[industrial_scenarios.optimization]
model = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
temperature = 0.3
max_tokens = 2048
system_prompt = "你是一个工业优化专家,能够分析生产数据、识别优化机会并提供具体的改进建议。"
```

### 环境变量配置

```bash
# 设置环境变量
export LLM_PROVIDER=siliconflow
export LLM_MODEL=Qwen/Qwen2.5-7B-Instruct
export SILICONFLOW_API_KEY=your-api-key
export LLM_BASE_URL=https://api.siliconflow.cn/v1
```

### 代码中配置

```python
from industrytslib.utils.llm import LLMConfig

# 详细配置
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    base_url="https://api.siliconflow.cn/v1",
    timeout=60,
    max_retries=3,
    retry_delay=1.0,
    temperature=0.7,
    max_tokens=2048,
    top_p=0.9,
    stream=False
)
```

## <a name="基础使用"></a>基础使用

### 同步调用

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 创建客户端和配置
client = UniversalLLMClient()
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

# 单轮对话
messages = [
    LLMMessage(role="user", content="请解释PID控制器的工作原理")
]

response = client.chat_completions([msg.dict() for msg in messages])
print(f"响应: {response.content}")
print(f"使用令牌: {response.usage.total_tokens if response.usage else 'N/A'}")
```

### 异步调用

```python
import asyncio
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

async def async_example():
    client = UniversalLLMClient()
    config = LLMConfig(
        provider="siliconflow",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key"
    )
    
    messages = [
        LLMMessage(role="user", content="分析工业数据预处理的关键步骤")
    ]
    
    response = await client.achat_completions([msg.dict() for msg in messages])
    print(f"异步响应: {response.content}")

# 运行异步函数
asyncio.run(async_example())
```

### 流式输出

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 启用流式输出
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    stream=True
)

client = UniversalLLMClient(config)
messages = [
    LLMMessage(role="user", content="详细解释机器学习在工业预测性维护中的应用")
]

print("流式响应:")
for chunk in client.stream_chat_completions([msg.dict() for msg in messages]):
    if chunk:
        print(chunk, end="", flush=True)
print("\n流式输出完成")
```

### 多轮对话

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

client = UniversalLLMClient()
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

# 初始化对话历史
conversation = [
    LLMMessage(role="system", content="你是一个专业的工业AI助手,专门帮助解决时间序列预测和工艺优化问题。"),
    LLMMessage(role="user", content="我的LSTM模型在训练集上表现很好,但在测试集上效果不佳,可能是什么原因？")
]

# 第一轮对话
response1 = client.chat_completions([msg.dict() for msg in conversation])
print(f"助手: {response1.content}")

# 添加助手回复到对话历史
conversation.append(LLMMessage(role="assistant", content=response1.content))

# 继续对话
conversation.append(LLMMessage(role="user", content="那我应该如何改进模型的泛化能力？"))

response2 = client.chat_completions([msg.dict() for msg in conversation])
print(f"助手: {response2.content}")
```

## <a name="高级功能"></a>高级功能

### 运行时切换服务提供商

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

client = UniversalLLMClient()
messages = [LLMMessage(role="user", content="解释数字孪生在制造业中的价值")]

# 使用SiliconFlow
sf_config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-siliconflow-key"
)
response1 = client.chat_completions([msg.dict() for msg in messages])
print(f"SiliconFlow响应: {response1.content[:100]}...")

# 切换到Ollama
ollama_config = LLMConfig(
    provider="ollama",
    model="qwen2.5:7b",
    host="localhost",
    port=11434
)
client = UniversalLLMClient(ollama_config)  # 重新初始化客户端
response2 = client.chat_completions([msg.dict() for msg in messages])
print(f"Ollama响应: {response2.content[:100]}...")
```

### 并发处理

```python
import asyncio
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

async def concurrent_requests():
    client = UniversalLLMClient()
    config = LLMConfig(
        provider="siliconflow",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key"
    )
    
    # 准备多个问题
    questions = [
        "什么是软测量技术？",
        "时间序列预测在工业中的应用",
        "异常检测算法的选择原则",
        "工业4.0的核心技术有哪些？"
    ]
    
    # 创建并发任务
    tasks = []
    for question in questions:
        messages = [LLMMessage(role="user", content=question)]
        task = client.achat_completions([msg.dict() for msg in messages])
        tasks.append(task)
    
    # 并发执行
    responses = await asyncio.gather(*tasks)
    
    # 输出结果
    for i, response in enumerate(responses):
        print(f"问题 {i+1}: {questions[i]}")
        print(f"回答: {response.content[:100]}...\n")

# 运行并发示例
asyncio.run(concurrent_requests())
```

### 便捷函数

```python
from industrytslib.utils.llm import quick_chat, aquick_chat
import asyncio

# 同步快速调用
response = quick_chat(
    messages=[{"role": "user", "content": "简述工业4.0的核心技术"}],
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.3
)
print(f"快速调用结果: {response}")

# 异步快速调用
async def quick_async_example():
    response = await aquick_chat(
        messages=[{"role": "user", "content": "解释数字孪生在制造业中的价值"}],
        provider="siliconflow",
        model="Qwen/Qwen2.5-7B-Instruct",
        api_key="your-api-key",
        temperature=0.3
    )
    print(f"异步快速调用结果: {response}")

asyncio.run(quick_async_example())
```

## <a name="工业场景应用"></a>工业场景应用

### 时间序列数据分析

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 专门用于时间序列分析的配置
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.2,  # 低温度,更精确的分析
    max_tokens=1024
)

client = UniversalLLMClient(config)

# 系统提示词
system_prompt = """你是一个专业的工业时间序列数据分析专家,具备以下能力:
1. 识别时间序列数据的模式和趋势
2. 分析数据质量问题(缺失值、异常值、噪声)
3. 推荐合适的预处理方法
4. 建议最适合的预测模型
5. 解释模型结果和性能指标

请基于提供的数据特征,给出专业的分析建议。"""

messages = [
    LLMMessage(role="system", content=system_prompt),
    LLMMessage(
        role="user",
        content="""我有一个化工反应器的温度数据,采样频率1分钟,数据特征如下:
- 数据长度:30天,共43200个数据点
- 温度范围:80-120°C
- 缺失值:约2%
- 明显的周期性模式:24小时周期
- 存在一些突变点(设备启停)
- 目标:预测未来6小时的温度变化

请分析这个数据的特点,并推荐合适的预测模型和预处理策略。"""
    )
]

response = client.chat_completions([msg.dict() for msg in messages])
print("时间序列分析建议:")
print(response.content)
```

### 异常检测诊断

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 异常检测专用配置
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.1,  # 极低温度,确保诊断的准确性
    max_tokens=512
)

client = UniversalLLMClient(config)

# 异常检测系统提示词
system_prompt = """你是一个工业异常检测专家,能够:
1. 识别和分析工业生产过程中的异常模式
2. 诊断可能的异常原因
3. 提供具体的处理建议和预防措施
4. 评估异常的严重程度和紧急性

请基于提供的异常数据,进行专业的诊断分析。"""

messages = [
    LLMMessage(role="system", content=system_prompt),
    LLMMessage(
        role="user",
        content="""在化工生产过程中,检测到以下异常:

**传感器数据异常:**
- 温度传感器:正常范围80-85°C,当前读数92°C,且持续上升
- 压力传感器:正常范围2.0-2.5 bar,当前读数2.1 bar(正常)
- 流量传感器:正常范围100-120 L/min,当前读数85 L/min(下降)
- pH传感器:正常范围6.5-7.5,当前读数6.8(正常)

**时间信息:**
- 异常开始时间:2小时前
- 异常发展趋势:温度持续上升,流量持续下降
- 操作状态:自动控制模式,无人工干预

请分析可能的异常原因并提供处理建议。"""
    )
]

response = client.chat_completions([msg.dict() for msg in messages])
print("异常诊断结果:")
print(response.content)
```

### 工艺优化建议

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 工艺优化专用配置
config = LLMConfig(
    provider="siliconflow",
    model="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",  # 使用推理模型
    api_key="your-api-key",
    temperature=0.3,  # 中等温度,平衡创新性和准确性
    max_tokens=2048
)

client = UniversalLLMClient(config)

# 工艺优化系统提示词
system_prompt = """你是一个工业工艺优化专家,具备以下专业能力:
1. 分析生产数据,识别优化机会
2. 提供具体的工艺参数调整建议
3. 评估优化方案的可行性和风险
4. 预测优化效果和投资回报
5. 制定实施计划和监控策略

请基于提供的生产数据和目标,给出专业的优化建议。"""

messages = [
    LLMMessage(role="system", content=system_prompt),
    LLMMessage(
        role="user",
        content="""**生产线现状:**
- 产品:聚合物生产
- 当前产量:95 吨/天(设计产能:120 吨/天)
- 能耗:8.5 MWh/吨产品
- 原料转化率:92%
- 产品质量合格率:96%

**关键工艺参数:**
- 反应温度:185°C(可调范围:180-200°C)
- 反应压力:15 bar(可调范围:12-18 bar)
- 催化剂浓度:0.8%(可调范围:0.5-1.2%)
- 停留时间:4.5小时(可调范围:3-6小时)

**优化目标:**
1. 提高产量至110吨/天
2. 降低能耗至7.5 MWh/吨
3. 提高原料转化率至95%
4. 保持产品质量合格率≥95%

请分析当前工艺的瓶颈,并提供具体的优化方案。"""
    )
]

response = client.chat_completions([msg.dict() for msg in messages])
print("工艺优化建议:")
print(response.content)
```

### 模型选择建议

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage

# 模型选择咨询配置
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    temperature=0.4,
    max_tokens=1536
)

client = UniversalLLMClient(config)

# 模型选择专家系统提示词
system_prompt = """你是一个机器学习模型选择专家,专门为工业时间序列预测任务提供建议。你具备以下专业知识:
1. 各种时间序列预测模型的特点和适用场景
2. 模型性能评估和比较方法
3. 工业数据的特殊性和挑战
4. 模型部署和维护的实际考虑

请基于数据特征和业务需求,推荐最合适的模型架构。"""

messages = [
    LLMMessage(role="system", content=system_prompt),
    LLMMessage(
        role="user",
        content="""**项目背景:**
钢铁厂高炉铁水温度预测

**数据特征:**
- 数据类型:多变量时间序列
- 采样频率:每5分钟一个数据点
- 历史数据:2年(约210,000个数据点)
- 输入变量:15个(温度、压力、流量、成分等)
- 目标变量:铁水温度
- 预测窗口:未来30分钟(6个时间步)

**数据质量:**
- 缺失值:约5%(主要由传感器故障导致)
- 异常值:约2%(设备维护期间)
- 噪声水平:中等
- 季节性:无明显季节性,但有生产周期性

**业务需求:**
- 预测精度:MAPE < 2%
- 实时性:预测延迟 < 30秒
- 可解释性:需要了解关键影响因素
- 部署环境:边缘计算设备(资源有限)

请推荐合适的模型架构,并说明选择理由。"""
    )
]

response = client.chat_completions([msg.dict() for msg in messages])
print("模型选择建议:")
print(response.content)
```

## <a name="性能优化"></a>性能优化

### 连接池和会话复用

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig
import asyncio

# 创建长期使用的客户端实例
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    timeout=30,  # 适当的超时时间
    max_retries=2  # 减少重试次数以提高响应速度
)

# 全局客户端实例,避免重复创建
global_client = UniversalLLMClient(config)

async def optimized_batch_processing(questions):
    """优化的批量处理"""
    tasks = []
    
    for question in questions:
        messages = [{"role": "user", "content": question}]
        task = global_client.achat_completions(messages)
        tasks.append(task)
    
    # 使用asyncio.gather进行并发处理
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果和异常
    successful_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"问题 {i+1} 处理失败: {result}")
        else:
            successful_results.append(result)
    
    return successful_results

# 使用示例
questions = [
    "什么是工业4.0？",
    "解释PID控制器",
    "时间序列预测方法"
]

results = asyncio.run(optimized_batch_processing(questions))
print(f"成功处理 {len(results)} 个问题")
```

### 缓存策略

```python
from functools import lru_cache
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig
import hashlib
import json

class CachedLLMClient:
    """带缓存的LLM客户端"""
    
    def __init__(self, config: LLMConfig):
        self.client = UniversalLLMClient(config)
        self._cache = {}
    
    def _get_cache_key(self, messages, **kwargs):
        """生成缓存键"""
        content = {
            "messages": messages,
            "kwargs": kwargs
        }
        content_str = json.dumps(content, sort_keys=True)
        return hashlib.md5(content_str.encode()).hexdigest()
    
    def chat_with_cache(self, messages, cache_ttl=3600, **kwargs):
        """带缓存的聊天"""
        cache_key = self._get_cache_key(messages, **kwargs)
        
        # 检查缓存
        if cache_key in self._cache:
            cached_result, timestamp = self._cache[cache_key]
            if time.time() - timestamp < cache_ttl:
                print("使用缓存结果")
                return cached_result
        
        # 调用API
        result = self.client.chat_completions(messages, **kwargs)
        
        # 存储到缓存
        self._cache[cache_key] = (result, time.time())
        
        return result

# 使用示例
import time

config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

cached_client = CachedLLMClient(config)

# 第一次调用
messages = [{"role": "user", "content": "什么是工业4.0？"}]
result1 = cached_client.chat_with_cache(messages)
print("第一次调用完成")

# 第二次调用(使用缓存)
result2 = cached_client.chat_with_cache(messages)
print("第二次调用完成(应该使用缓存)")
```

### 流式处理优化

```python
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig, LLMMessage
import time

def optimized_streaming_chat(client, messages, chunk_callback=None):
    """优化的流式聊天处理"""
    start_time = time.time()
    full_content = ""
    chunk_count = 0
    
    try:
        for chunk in client.stream_chat_completions(messages):
            if chunk:
                full_content += chunk
                chunk_count += 1
                
                # 可选的回调处理
                if chunk_callback:
                    chunk_callback(chunk, chunk_count)
                
                # 实时显示(可选)
                print(chunk, end="", flush=True)
        
        end_time = time.time()
        
        print(f"\n\n流式处理完成:")
        print(f"总时间: {end_time - start_time:.2f}秒")
        print(f"总块数: {chunk_count}")
        print(f"内容长度: {len(full_content)}字符")
        print(f"平均速度: {len(full_content)/(end_time - start_time):.1f}字符/秒")
        
        return full_content
        
    except Exception as e:
        print(f"\n流式处理出错: {e}")
        return full_content  # 返回已接收的部分内容

# 使用示例
config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    stream=True,
    max_tokens=1000
)

client = UniversalLLMClient(config)
messages = [{
    "role": "user",
    "content": "详细解释机器学习在工业预测性维护中的应用,包括数据收集、特征工程、模型选择和部署等各个环节。"
}]

# 定义块处理回调
def chunk_handler(chunk, count):
    if count % 10 == 0:  # 每10个块显示一次进度
        print(f"\n[已接收 {count} 个块]\n", end="")

result = optimized_streaming_chat(client, messages, chunk_handler)
```

## <a name="错误处理"></a>错误处理

### 异常类型

```python
from industrytslib.utils.llm import (
    UniversalLLMError,
    LLMConfigError,
    LLMConnectionError,
    LLMAPIError,
    LLMTimeoutError
)

# 具体的异常处理示例
def robust_llm_call(client, messages, max_retries=3):
    """健壮的LLM调用,包含完整的错误处理"""
    
    for attempt in range(max_retries):
        try:
            response = client.chat_completions(messages)
            return response
            
        except LLMConfigError as e:
            print(f"配置错误: {e}")
            print("请检查API密钥、模型名称等配置信息")
            break  # 配置错误不需要重试
            
        except LLMConnectionError as e:
            print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print("连接失败,请检查网络连接和服务状态")
                
        except LLMTimeoutError as e:
            print(f"请求超时 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                print("增加超时时间后重试...")
                # 可以动态调整超时时间
            else:
                print("请求超时,请尝试减少输入长度或增加超时时间")
                
        except LLMAPIError as e:
            print(f"API错误: {e}")
            if "rate limit" in str(e).lower():
                print("遇到频率限制,等待后重试...")
                time.sleep(60)  # 等待1分钟
            elif "quota" in str(e).lower():
                print("配额不足,请检查账户余额")
                break
            else:
                print("API调用失败,请检查请求参数")
                
        except UniversalLLMError as e:
            print(f"通用LLM错误: {e}")
            break
            
        except Exception as e:
            print(f"未知错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                print("所有重试均失败")
    
    return None

# 使用示例
import time
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig

config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key",
    timeout=30
)

client = UniversalLLMClient(config)
messages = [{"role": "user", "content": "解释工业4.0的核心概念"}]

result = robust_llm_call(client, messages)
if result:
    print(f"调用成功: {result.content}")
else:
    print("调用失败")
```

### 日志记录

```python
import logging
from industrytslib.utils.llm import UniversalLLMClient, LLMConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('llm_calls.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('LLMClient')

class LoggedLLMClient:
    """带日志记录的LLM客户端包装器"""
    
    def __init__(self, config: LLMConfig):
        self.client = UniversalLLMClient(config)
        self.config = config
        logger.info(f"初始化LLM客户端: {config.provider} - {config.model}")
    
    def chat_completions(self, messages, **kwargs):
        """带日志的聊天完成"""
        start_time = time.time()
        
        # 记录请求信息
        logger.info(f"发送请求: {len(messages)}条消息, 模型: {self.config.model}")
        logger.debug(f"请求内容: {messages}")
        
        try:
            response = self.client.chat_completions(messages, **kwargs)
            
            # 记录成功响应
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"请求成功: 耗时{duration:.2f}秒, 响应长度{len(response.content)}字符")
            
            if response.usage:
                logger.info(f"令牌使用: {response.usage.total_tokens}总计 "
                          f"({response.usage.prompt_tokens}输入 + {response.usage.completion_tokens}输出)")
            
            return response
            
        except Exception as e:
            # 记录错误
            end_time = time.time()
            duration = end_time - start_time
            
            logger.error(f"请求失败: 耗时{duration:.2f}秒, 错误: {e}")
            logger.debug(f"失败的请求内容: {messages}")
            
            raise

# 使用示例
import time

config = LLMConfig(
    provider="siliconflow",
    model="Qwen/Qwen2.5-7B-Instruct",
    api_key="your-api-key"
)

logged_client = LoggedLLMClient(config)

try:
    messages = [{"role": "user", "content": "什么是数字孪生技术？"}]
    response = logged_client.chat_completions(messages)
    print(f"响应: {response.content}")
except Exception as e:
    print(f"调用失败: {e}")
```

## <a name="最佳实践"></a>最佳实践

### 1. 配置管理

```python
# ✅ 推荐:使用配置文件
from industrytslib.utils.llm import create_universal_client

# 从配置文件创建客户端
client = create_universal_client(config_file="llm_config.toml")

# ❌ 不推荐:硬编码配置
client = UniversalLLMClient(LLMConfig(
    provider="siliconflow",
    api_key="sk-hardcoded-key",  # 不要硬编码密钥
    model="Qwen/Qwen2.5-7B-Instruct"
))
```

### 2. 错误处理

```python
# ✅ 推荐:完整的错误处理
try:
    response = client.chat_completions(messages)
    return response.content
except LLMConfigError:
    logger.error("配置错误,请检查API密钥和模型设置")
    return None
except LLMConnectionError:
    logger.error("网络连接失败,请检查网络状态")
    return None
except Exception as e:
    logger.error(f"未知错误: {e}")
    return None

# ❌ 不推荐:忽略异常
response = client.chat_completions(messages)  # 可能抛出异常
```

### 3. 性能优化

```python
# ✅ 推荐:复用客户端实例
global_client = UniversalLLMClient(config)

def process_multiple_requests(requests):
    results = []
    for request in requests:
        result = global_client.chat_completions(request)
        results.append(result)
    return results

# ❌ 不推荐:重复创建客户端
def process_request(request):
    client = UniversalLLMClient(config)  # 每次都创建新实例
    return client.chat_completions(request)
```

### 4. 异步处理

```python
# ✅ 推荐:使用异步处理并发请求
async def process_concurrent_requests(requests):
    tasks = [client.achat_completions(req) for req in requests]
    return await asyncio.gather(*tasks)

# ❌ 不推荐:同步处理多个请求
def process_sequential_requests(requests):
    results = []
    for request in requests:
        result = client.chat_completions(request)  # 阻塞调用
        results.append(result)
    return results
```

### 5. 提示词设计

```python
# ✅ 推荐:结构化的提示词
system_prompt = """你是一个专业的工业AI助手,具备以下能力:
1. 时间序列数据分析
2. 异常检测和诊断
3. 工艺优化建议
4. 模型选择指导

请基于用户提供的信息,给出专业、准确的建议。"""

user_prompt = """**问题描述:**
我的LSTM模型在训练集上RMSE=0.05,但在测试集上RMSE=0.15。

**数据信息:**
- 训练集:6个月数据
- 测试集:1个月数据
- 特征数:12个
- 采样频率:每小时

**请分析可能的原因并提供解决方案。**"""

# ❌ 不推荐:模糊的提示词
user_prompt = "我的模型效果不好,怎么办？"  # 信息不足
```

### 6. 资源管理

```python
# ✅ 推荐:使用上下文管理器
from contextlib import contextmanager

@contextmanager
def llm_client_context(config):
    client = UniversalLLMClient(config)
    try:
        yield client
    finally:
        # 清理资源(如果需要)
        pass

# 使用示例
with llm_client_context(config) as client:
    response = client.chat_completions(messages)
    print(response.content)

# ❌ 不推荐:不管理资源生命周期
client = UniversalLLMClient(config)
response = client.chat_completions(messages)
# 没有明确的资源清理
```

### 7. 监控和指标

```python
import time
from collections import defaultdict

class LLMMetrics:
    """LLM调用指标收集器"""
    
    def __init__(self):
        self.call_count = 0
        self.total_time = 0
        self.error_count = 0
        self.token_usage = defaultdict(int)
    
    def record_call(self, duration, tokens=None, error=None):
        self.call_count += 1
        self.total_time += duration
        
        if error:
            self.error_count += 1
        
        if tokens:
            self.token_usage['total'] += tokens.total_tokens
            self.token_usage['prompt'] += tokens.prompt_tokens
            self.token_usage['completion'] += tokens.completion_tokens
    
    def get_stats(self):
        if self.call_count == 0:
            return "无调用记录"
        
        avg_time = self.total_time / self.call_count
        error_rate = self.error_count / self.call_count * 100
        
        return f"""
LLM调用统计:
- 总调用次数: {self.call_count}
- 平均响应时间: {avg_time:.2f}秒
- 错误率: {error_rate:.1f}%
- 总令牌使用: {self.token_usage['total']}
- 平均令牌/调用: {self.token_usage['total'] / self.call_count:.1f}
"""

# 使用示例
metrics = LLMMetrics()

def monitored_llm_call(client, messages):
    start_time = time.time()
    error = None
    
    try:
        response = client.chat_completions(messages)
        duration = time.time() - start_time
        metrics.record_call(duration, response.usage)
        return response
    except Exception as e:
        duration = time.time() - start_time
        metrics.record_call(duration, error=e)
        raise

# 定期输出统计信息
print(metrics.get_stats())
```

## 总结

通用LLM客户端(UniversalLLMClient)为工业时间序列AI应用提供了强大而灵活的LLM集成能力。通过统一的接口、丰富的配置选项和完善的错误处理机制,它能够满足各种工业场景的需求。

### 主要优势

1. **统一接口**:支持多个LLM服务提供商,使用相同的API
2. **工业专用**:针对工业场景进行了专门优化
3. **配置灵活**:支持多种配置方式,适应不同部署环境
4. **类型安全**:完整的类型提示和数据验证
5. **生产就绪**:内置错误处理、重试机制和性能优化

### 适用场景

- 时间序列数据分析和预测
- 工业异常检测和诊断
- 工艺参数优化建议
- 模型选择和架构推荐
- 智能决策支持系统

### 下一步

- 查看 [API参考文档](api-reference.md) 了解详细的API说明
- 阅读 [工业应用场景](industrial-applications.md) 了解更多实际应用案例
- 参考 [性能优化指南](performance-optimization.md) 提升系统性能
- 查看 [故障排除指南](troubleshooting.md) 解决常见问题