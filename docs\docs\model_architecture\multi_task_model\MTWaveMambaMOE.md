# MTWaveMambaMOE: Multi-Task Wavelet Mamba Mixture of Experts

## Abstract

The MTWaveMambaMOE (Multi-Task Wavelet Mamba Mixture of Experts) model is a novel hybrid architecture designed for industrial time series forecasting that combines wavelet decomposition, state space models, and sparse mixture of experts. This model effectively captures both local and global temporal patterns while modeling complex spatial dependencies between variables. By integrating these components, MTWaveMambaMOE achieves superior forecasting performance with linear scaling properties, making it particularly suitable for high-dimensional industrial process data with long-range dependencies.

## 1. Introduction

Accurate forecasting of multivariate time series is crucial in industrial applications for process optimization, predictive maintenance, and anomaly detection. Traditional models often struggle with:

1. Capturing multi-scale temporal patterns
2. Modeling variable interdependencies
3. Efficiently handling long sequences
4. Adapting to diverse industrial contexts

MTWaveMambaMOE addresses these challenges through a multi-component architecture that leverages recent advances in wavelets, state space models, and conditional computation.

## 2. Model Architecture

The MTWaveMambaMOE architecture consists of five main components:

1. **Wavelet Decomposition**: Discrete Wavelet Transform (DWT) for multi-scale feature extraction
2. **Graph Processing**: Graph Neural Networks to capture spatial dependencies
3. **Mamba Processing**: State space model for efficient sequence modeling
4. **Mixture of Experts (MoE)**: Sparse gating for specialized prediction
5. **Wavelet Reconstruction**: Inverse Discrete Wavelet Transform (IDWT) for forecast generation

```mermaid
flowchart TB
    subgraph "Input Processing"
        Input["Input Time Series\nx ∈ ℝ^{B×L×N}"] --> RevIN["Reversible Instance Normalization"]
        RevIN --> Transpose["Transpose to ℝ^{B×N×L}"]
    end
    
    subgraph "Wavelet Decomposition"
        Transpose --> DWT["Discrete Wavelet Transform\nyl, yh = DWT(x)"]
        DWT --> WaveletCoefs["Wavelet Coefficients\n[yl, yh₁, yh₂, ..., yhⱼ]"]
    end
    
    subgraph "Graph Processing"
        WaveletCoefs --> GraphConst["Graph Constructor\nAdjacency Matrix A"]
        GraphConst --> GPModules["Graph Processing Modules\n(GPM₁, GPM₂, ..., GPMⱼ₊₁)"]
        GPModules --> NewCoefs["Processed Coefficients\n[yl', yh₁', yh₂', ..., yhⱼ']"]
    end
    
    subgraph "Forecast Generation"
        NewCoefs --> Concat["Concatenate with Original\n[yl+yl', yh₁+yh₁', ...]"]
        Concat --> IDWT["Inverse Discrete Wavelet Transform"]
        IDWT --> ExtractPred["Extract Prediction Window\nx' ∈ ℝ^{B×pred_len×N}"]
    end
    
    subgraph "Multi-Task Enhancement"
        ExtractPred --> MambaBlock["Mamba Block\nStructured State Space Sequence Model"]
        MambaBlock --> Split["Split Processing"]
        Split --> MainOutput["Main Output\n(Denormalized via RevIN)"]
        Split --> MoEBlock["Mixture of Experts Block"]
        MoEBlock --> OutputProj["Output Projection"]
        OutputProj --> ExtraOutput["Extra Output\n(Auxiliary Task)"]
    end
    
    MainOutput --> FinalOutput["Final Output\n(Main & Extra)"]
    ExtraOutput --> FinalOutput
```

## 3. Mathematical Formulation

### 3.1 Wavelet Decomposition

For an input time series $\mathbf{X} \in \mathbb{R}^{B \times L \times N}$ (where $B$ is batch size, $L$ is sequence length, and $N$ is the number of variables), we first apply Reversible Instance Normalization (RevIN):

$$\mathbf{X}_{norm} = \frac{\mathbf{X} - \mu}{\sigma}$$

where $\mu$ and $\sigma$ are the mean and standard deviation computed along the temporal dimension.

The normalized series is transposed to $\mathbf{X}_{norm}^T \in \mathbb{R}^{B \times N \times L}$ and decomposed using the Discrete Wavelet Transform (DWT):

$$\mathbf{Y}_l, \{\mathbf{Y}_{h_j}\}_{j=1}^J = \text{DWT}(\mathbf{X}_{norm}^T)$$

where $\mathbf{Y}_l$ is the low-frequency approximation coefficient and $\{\mathbf{Y}_{h_j}\}_{j=1}^J$ are the high-frequency detail coefficients at $J$ different scales.

### 3.2 Graph Construction and Processing

For each variable, a graph structure is constructed to capture spatial dependencies:

$$\mathbf{A} = f_{GC}(\text{idx}, \mathbf{FE})$$

where $f_{GC}$ is the graph constructor function, idx represents node indices, and $\mathbf{FE}$ are optional node features.

The adjacency matrix $\mathbf{A}$ is computed as:

$$\mathbf{A} = \text{ReLU}(\tanh(\alpha \cdot (\mathbf{n}_1 \mathbf{n}_2^T - \mathbf{n}_2 \mathbf{n}_1^T)))$$

where $\mathbf{n}_1 = \tanh(\alpha \cdot \mathbf{W}_1 \cdot \mathbf{E})$ and $\mathbf{n}_2 = \tanh(\alpha \cdot \mathbf{W}_2 \cdot \mathbf{E})$, with $\mathbf{E}$ being node embeddings and $\alpha$ a scaling parameter.

Each wavelet coefficient is processed through a Graph Processing Module (GPM):

$$\mathbf{Y}_{l}', \{\mathbf{Y}_{h_j}'\}_{j=1}^J = \text{GPM}_1(\mathbf{Y}_l, \mathbf{A}), \{\text{GPM}_{j+1}(\mathbf{Y}_{h_j}, \mathbf{A})\}_{j=1}^J$$

The GPM combines graph convolution, dilated temporal convolution, and skip connections to model spatial-temporal dependencies.

### 3.3 Mamba State Space Processing

The Mamba block implements a selective state space model (SSM) for sequence modeling:

$$\mathbf{x}_t = \mathbf{A}_t \mathbf{x}_{t-1} + \mathbf{B}_t \mathbf{u}_t$$
$$\mathbf{y}_t = \mathbf{C}_t \mathbf{x}_t$$

where $\mathbf{x}_t$ is the hidden state, $\mathbf{u}_t$ is the input, $\mathbf{y}_t$ is the output, and $\mathbf{A}_t, \mathbf{B}_t, \mathbf{C}_t$ are parameter matrices that are input-dependent.

The selective mechanism makes the state space parameters dependent on the input:

$$\mathbf{A}_t, \mathbf{B}_t, \mathbf{C}_t = f_{\text{SSM}}(\mathbf{u}_t)$$

This enables efficient handling of long sequences with linear time complexity $O(L)$.

### 3.4 Mixture of Experts

The Mixture of Experts (MoE) component employs a sparse gating mechanism:

$$\mathbf{g} = \text{softmax}(\mathbf{W}_g \mathbf{x})$$
$$\mathbf{y} = \sum_{i=1}^{E} \mathbf{g}_i \cdot \text{Expert}_i(\mathbf{x})$$

where $\mathbf{g}$ represents gating probabilities, $E$ is the number of experts, and only the top-$k$ experts are activated for each input.

The routing algorithm ensures balanced expert utilization through auxiliary losses:

$$\mathcal{L}_{balance} = \beta \cdot \sum_{i=1}^{E}(f_i - \frac{1}{E})^2$$
$$\mathcal{L}_{router-z} = \gamma \cdot \frac{1}{B} \sum_{b=1}^{B} ||\mathbf{g}_b||^2$$

where $f_i$ is the fraction of tokens routed to expert $i$, and $\mathbf{g}_b$ is the gating vector for batch element $b$.

### 3.5 Wavelet Reconstruction and Output Generation

The processed wavelet coefficients are concatenated with original coefficients to form complete sequences:

$$\mathbf{Y}_{l}^{complete} = [\mathbf{Y}_l, \mathbf{Y}_{l}']$$
$$\mathbf{Y}_{h_j}^{complete} = [\mathbf{Y}_{h_j}, \mathbf{Y}_{h_j}']$$

The Inverse Discrete Wavelet Transform (IDWT) reconstructs the time series:

$$\mathbf{X}_{rec} = \text{IDWT}(\mathbf{Y}_{l}^{complete}, \{\mathbf{Y}_{h_j}^{complete}\}_{j=1}^J)$$

The prediction is extracted from the reconstructed series:

$$\mathbf{X}_{pred} = \mathbf{X}_{rec}[:, -\text{pred\_len}:, :]^T$$

Finally, two outputs are generated:

1. **Main Output**: $\mathbf{X}_{main} = \text{RevIN}^{-1}(\mathbf{X}_{pred})$ (denormalized)
2. **Extra Output**: $\mathbf{X}_{extra} = \text{OutputProj}(\text{MoE}(\mathbf{X}_{pred}))$

## 4. Training and Optimization

The model is trained using a multi-task objective function:

$$\mathcal{L} = \lambda_1 \mathcal{L}_{main} + \lambda_2 \mathcal{L}_{extra} + \lambda_3 \mathcal{L}_{balance} + \lambda_4 \mathcal{L}_{router-z}$$

where $\mathcal{L}_{main}$ and $\mathcal{L}_{extra}$ are task-specific losses (typically MSE or MAE), and $\lambda_i$ are weighting coefficients.

## 5. Model Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| seq_len | Input sequence length | Required |
| pred_len | Prediction horizon length | Required |
| n_points | Number of variables/nodes | Required |
| dropout | Dropout rate for regularization | Required |
| c_out | Output channels for auxiliary prediction | n_points |
| in_channels | Input channels | n_points |
| use_revin | Whether to use RevIN | True |
| wavelet_j | Number of wavelet decomposition levels | Required |
| wavelet | Wavelet type (e.g., 'db4', 'sym4', 'haar') | Required |
| subgraph_size | Number of neighbors for each node | Required |
| node_dim | Dimension of node embeddings | Required |
| n_gnn_layer | Number of GNN layers | Required |
| d_model | Hidden dimension for Mamba | 128 |
| d_state | State dimension for Mamba | 16 |
| d_conv | Convolution kernel size for Mamba | 4 |
| expand | Expansion factor for Mamba | 2 |
| num_experts | Number of experts in MoE | 8 |
| gating_top_n | Number of experts selected per token | 2 |

## 6. Advantages and Applications

### 6.1 Advantages

- **Efficient sequence modeling**: Linear time complexity for long sequences
- **Multi-scale analysis**: Wavelet decomposition captures patterns at different temporal scales
- **Spatial dependency modeling**: Graph neural networks model variable relationships
- **Adaptive specialization**: MoE enables conditional computation for diverse patterns
- **Dual output format**: Supports both direct forecasting and auxiliary tasks

### 6.2 Applications

- Process variable prediction in chemical plants
- Energy consumption forecasting in industrial facilities
- Equipment health monitoring and predictive maintenance
- Quality parameter prediction in manufacturing
- Resource allocation optimization in production systems

## 7. Conclusion

MTWaveMambaMOE represents a significant advancement in industrial time series forecasting by combining the strengths of wavelet analysis, graph neural networks, state space models, and mixture of experts. Its ability to model complex temporal patterns and spatial dependencies while maintaining computational efficiency makes it particularly suitable for real-world industrial applications where accurate forecasting is critical for operational excellence.

## References

1. State Space Models (SSM): Gu, A., et al. (2023). Mamba: Linear-Time Sequence Modeling with Selective State Spaces.
2. Mixture of Experts (MoE): Shazeer, N., et al. (2017). Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer.
3. Wavelet Analysis: Addison, P. S. (2017). The Illustrated Wavelet Transform Handbook.
4. Reversible Instance Normalization: Kim, D., et al. (2022). Reversible Instance Normalization for Accurate Time-Series Forecasting against Distribution Shift.
5. Graph Neural Networks: Wu, Z., et al. (2020). A Comprehensive Survey on Graph Neural Networks.
