# WaveForM: Graph Enhanced Wavelet Learning for Long Sequence Forecasting of Multivariate Time Series

## 网络结构图

```mermaid
graph TD
    subgraph 输入处理
        A[("输入数据 (B, L, N)")] --> B["维度转置<br/>(B, L, N) → (B, N, L)"]
        B --> C["离散小波变换 DWT<br/>多层小波分解"]
    end

    subgraph 小波域处理
        C --> D["低频系数 yl<br/>(B, N, L_低频)"]
        C --> E["高频系数组 yhs<br/>(B, N, L_高频1)<br/>(B, N, L_高频2)<br/>..."]
        
        D --> F["GPModule网络 1<br/>图增强时序建模"]
        E --> G["GPModule网络 2,3,...<br/>图增强时序建模"]
        
        F --> H["低频预测输出<br/>(B, N, pred_len_低频)"]
        G --> I["高频预测输出<br/>(<PERSON>, <PERSON>, pred_len_高频)"]
    end
    
    subgraph 小波域合并与重构
        H --> J["低频与预测拼接<br/>(B, N, L_低频+pred_len_低频)"]
        I --> K["高频与预测拼接<br/>(B, N, L_高频+pred_len_高频)"]
        
        J --> L["逆离散小波变换 IDWT<br/>小波重构"]
        K --> L
    end
    
    subgraph 输出处理
        L --> M["维度转置<br/>(B, N, L+pred_len) → (B, L+pred_len, N)"]
        M --> N["截取预测部分<br/>(B, pred_len, N)"]
        N --> O["线性投影<br/>(B, pred_len, N) → (B, pred_len, c_out)"]
    end
    
    O --> P[("预测输出 (B, pred_len, c_out)")]
    
    style 小波域处理 fill:#f9f,stroke:#333,stroke-width:1px
    style 小波域合并与重构 fill:#fcf,stroke:#333,stroke-width:1px
```

## 1. 模型概述

WaveForM (Graph enhanced **Wave**let learning **for** **M**ultivariate time series forecasting) 是一种结合了小波分析和图神经网络的时序预测模型。该模型通过离散小波变换将时序数据分解为不同频率的子序列,然后使用图增强的时序模型在小波域中进行建模,最后通过逆小波变换生成预测结果。

### 核心特点

1. **小波域分解**:使用离散小波变换(DWT)将原始时序信号分解为多个频率成分
2. **图结构学习**:通过动态构建图结构捕获变量间的依赖关系
3. **多尺度建模**:在不同频率下的子序列上分别建模,能够同时捕获长短期模式
4. **时空结合**:综合考虑时间维度(通过小波变换)和空间维度(通过图结构)的依赖关系

## 2. 模型架构详解

### 2.1 图结构构建器 (GraphConstructor)

GraphConstructor负责学习并构建多变量时间序列之间的依赖关系图:

```python
self._graph_constructor = GraphConstructor(
    nnodes=self.points,  # 节点数
    k=configs.subgraph_size,  # 子图大小
    dim=configs.node_dim,  # 节点维度
    alpha=3.0  # 超参数
)
```

#### 2.1.1 节点嵌入

每个节点(时间序列变量)通过嵌入层编码为高维向量:

$$\text{nodevec1} = \text{Embedding1}(\text{idx})$$
$$\text{nodevec2} = \text{Embedding2}(\text{idx})$$

或使用外部特征(如果提供):

$$\text{nodevec1} = \text{Linear1}(\text{FE}[\text{idx}])$$
$$\text{nodevec2} = \text{Linear2}(\text{FE}[\text{idx}])$$

#### 2.1.2 图构建过程

通过节点嵌入向量构建邻接矩阵:

$$\text{nodevec1} = \tanh(\alpha \cdot \text{Linear1}(\text{nodevec1}))$$
$$\text{nodevec2} = \tanh(\alpha \cdot \text{Linear2}(\text{nodevec2}))$$

计算非对称邻接矩阵:

$$a = \text{nodevec1} \cdot \text{nodevec2}^T - \text{nodevec2} \cdot \text{nodevec1}^T$$
$$A = \text{ReLU}(\tanh(\alpha \cdot a))$$

选择Top-k连接构建稀疏图:

$$\text{mask} = \text{TopK}(A, k, \text{dim}=1)$$
$$A = A \odot \text{mask}$$

### 2.2 离散小波变换 (DWT)

模型使用PyTorch Wavelets库实现小波分解:

```python
self.dwt = DWT1DForward(wave=wave, J=decompose_layer, mode=mode)
self.idwt = DWT1DInverse(wave=wave)
```

输入数据首先进行维度转置,然后通过DWT分解为低频成分和多个高频成分:

```python
in_dwt = x_enc.permute(0,2,1)  # (B,L,N) -> (B,N,L)
yl, yhs = self.dwt(in_dwt)     # yl:(B,N,L_low), yhs:[(B,N,L_high1),(B,N,L_high2),...]
coefs = [yl] + yhs            # 所有小波系数
```

小波变换将信号分解为:
- 低频系数 $y_l$:捕获信号的长期趋势
- 高频系数 $y_h$:捕获信号的细节和波动

### 2.3 图增强处理模块 (GPModule)

对每个小波系数,模型使用独立的GPModule进行处理:

```python
self.nets = nn.ModuleList()
for i in range(decompose_layer + 1):
    self.nets.append(GPModule(...))
```

#### 2.3.1 GPModule结构

每个GPModule包含以下组件:

1. **图构建**:使用GraphConstructor构建或使用提供的图结构
2. **膨胀卷积层**:通过DilatedInception实现多尺度时序建模
3. **混合传播层**:通过MixProp实现图上的消息传递
4. **跳跃连接**:结合多层表示增强特征提取

#### 2.3.2 DilatedInception模块

使用不同核大小(2,3,6,7)的膨胀卷积捕获多尺度时间模式:

$$X_{\text{out},i} = \text{Conv2d}(X_{\text{in}}, \text{kernel}=k_i, \text{dilation}=d)$$

$$ X_{\text{out}} = \text{Concat}(X_{\text{out},1}, X_{\text{out},2}, ..., X_{\text{out},n})$$

#### 2.3.3 MixProp模块

在图结构上进行混合传播,结合节点自身特征和邻居特征:

$$A' = A + I$$
$$d = \sum_{j} A'_{ij}$$
$$\hat{A} = A' / d$$
$$H^{(0)} = X$$
$$H^{(l)} = \alpha \cdot X + (1-\alpha) \cdot \hat{A}H^{(l-1)}$$
$$H_{\text{out}} = \text{MLP}([H^{(0)}, H^{(1)}, ..., H^{(L)}])$$

其中:
- $\alpha$ 是混合系数
- $L$ 是传播深度(gcn_depth)

#### 2.3.4 GPModuleLayer

每层GPModuleLayer实现双重门控机制:

$$X_{\text{filter}} = \tanh(\text{DilatedInception}_{\text{filter}}(X))$$
$$X_{\text{gate}} = \sigma(\text{DilatedInception}_{\text{gate}}(X))$$
$$X_{\text{gated}} = X_{\text{filter}} \odot X_{\text{gate}}$$

然后通过MixProp或残差连接进行特征增强:

$$X_{\text{out}} = \text{MixProp1}(X_{\text{gated}}, A) + \text{MixProp2}(X_{\text{gated}}, A^T)$$

最后通过层归一化和残差连接:

$$X_{\text{out}} = \text{LayerNorm}(X_{\text{out}} + X_{\text{residual}})$$

### 2.4 预测生成与小波重构

#### 2.4.1 小波域预测

每个GPModule网络生成对应小波系数的预测部分:

```python
coefs_new = self.model(coefs)  # 生成每个小波系数的预测部分
```

#### 2.4.2 系数拼接

将原始小波系数与预测部分拼接:

```python
coefs_idwt = []
for i in range(len(coefs_new)):
    coefs_idwt.append(torch.cat((coefs[i], coefs_new[i]), 2))
```

#### 2.4.3 逆小波变换

使用逆离散小波变换将小波系数重构为时间序列:

```python
out = self.idwt((coefs_idwt[0], coefs_idwt[1:]))  # 小波重构
```

#### 2.4.4 输出处理

恢复维度顺序并截取预测部分:

```python
pred_out = out.permute(0, 2, 1)  # (B,N,L+pred_len) -> (B,L+pred_len,N)
pred_out = self.projection(pred_out[:, -self.pred_len:, :])  # 线性投影到目标维度
```

## 3. 关键参数配置

```python
configs = {
    "seq_len": 96,            # 输入序列长度
    "pred_len": 24,           # 预测长度
    "n_points": 7,            # 时间序列变量数量
    "dropout": 0.1,           # Dropout率
    "c_out": 7,               # 输出维度
    
    "wavelet_j": 3,           # 小波分解层数
    "wavelet": "db4",         # 小波类型(如Daubechies4)
    
    "subgraph_size": 3,       # 子图大小(每个节点的邻居数)
    "node_dim": 40,           # 节点嵌入维度
    
    "n_gnn_layer": 3,         # GNN层数
}
```

## 4. 前向传播流程

模型的完整前向传播过程如下:

1. **输入处理**:
   ```python
   in_dwt = x_enc.permute(0,2,1)  # 维度转置
   ```

2. **小波分解**:
   ```python
   yl, yhs = self.dwt(in_dwt)  # 离散小波变换
   coefs = [yl] + yhs          # 合并所有小波系数
   ```

3. **小波域处理**:
   ```python
   coefs_new = self.model(coefs)  # 对每个小波系数应用GPModule
   ```

4. **小波系数拼接**:
   ```python
   coefs_idwt = []
   for i in range(len(coefs_new)):
       coefs_idwt.append(torch.cat((coefs[i], coefs_new[i]), 2))
   ```

5. **小波重构**:
   ```python
   out = self.idwt((coefs_idwt[0], coefs_idwt[1:]))  # 逆离散小波变换
   ```

6. **输出生成**:
   ```python
   pred_out = out.permute(0, 2, 1)  # 维度转置回原始顺序
   pred_out = self.projection(pred_out[:, -self.pred_len:, :])  # 截取预测部分并投影
   ```

## 5. 应用场景与优势

### 5.1 适用场景

- **多变量时序预测**:工业生产指标、传感器数据、能源需求等
- **长序列预测**:需要利用长历史序列进行预测的应用
- **包含多频率模式**:具有季节性、趋势和噪声等多种频率成分的数据

### 5.2 模型优势

1. **多尺度建模**:通过小波分解分别处理不同频率的模式
2. **关系发现**:通过图结构自动学习变量间的依赖关系
3. **长序列处理能力**:小波分解减少了序列长度,有效应对长序列问题
4. **鲁棒性**:小波分解天然具有抗噪能力,提高模型的稳定性

## 6. 训练建议

1. **小波类型选择**:
   - 平滑序列:选择较高阶的小波(如'db8'、'sym8')
   - 尖锐变化序列:选择较低阶的小波(如'db1'、'haar')

2. **分解层数**:
   - 通常3-4层适合大多数应用场景
   - 更长的序列可能需要更多层数

3. **图结构参数**:
   - 子图大小(k):根据变量间的实际依赖关系调整
   - 节点维度:一般设置为20-60,视数据复杂度而定

4. **学习率设置**:
   - 初始学习率:建议使用0.001
   - 采用学习率调度策略,如ReduceLROnPlateau
