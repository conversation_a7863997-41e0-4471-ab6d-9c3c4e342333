# WaveMoE: 基于小波变换与混合专家系统的多变量时序预测模型

## 网络结构图

```mermaid
graph TD
    subgraph 输入处理
        A[("输入数据 (B, L, N)")] --> B1["RevIN标准化处理<br/>(可选, 仅用于输入通道)"]
        A --> B2["标准均值-方差标准化<br/>(可选)"]
        B1 --> C["维度调整<br/>(B, L, N) → (B, N, L)"]
        B2 --> C
    end

    subgraph 小波分解与处理
        C --> D["小波分解<br/>DWT1DForward"]
        D --> E1["近似系数 yl<br/>(B, N, L_0)"]
        D --> E2["细节系数 yh_1<br/>(B, N, L_1)"]
        D --> E3["细节系数 yh_2<br/>(B, N, L_2)"]
        
        E1 --> F1["图网络处理 1<br/>GPModule"]
        E2 --> F2["图网络处理 2<br/>GPModule"]
        E3 --> F3["图网络处理 3<br/>GPModule"]
        
        F1 --> G1["预测系数 yl'<br/>(B, N, L_0')"]
        F2 --> G2["预测系数 yh_1'<br/>(B, N, L_1')"]
        F3 --> G3["预测系数 yh_2'<br/>(B, N, L_2')"]
        
        E1 --> H1["系数拼接<br/>(B, N, L_0+L_0')"]
        E2 --> H2["系数拼接<br/>(B, N, L_1+L_1')"]
        E3 --> H3["系数拼接<br/>(B, N, L_2+L_2')"]
        
        G1 --> H1
        G2 --> H2
        G3 --> H3
        
        H1 --> I["小波重构<br/>DWT1DInverse"]
        H2 --> I
        H3 --> I
    end
    
    subgraph 混合专家系统增强
        I --> J["维度调整<br/>(B, N, L+pred_len) → (B, pred_len, N)"]
        J --> K["SparseMoEBlock"]
        
        K --> L["MoE前馈网络"]
        L --> M["专家路由<br/>TopNGating"]
        M --> |Expert 1| N1["专家网络 1"]
        M --> |Expert 2| N2["专家网络 2"]
        M --> |...| N3["..."]
        M --> |Expert n| N4["专家网络 n"]
        N1 --> O["输出聚合"]
        N2 --> O
        N3 --> O
        N4 --> O
        O --> P["MoE后馈网络"]
    end
    
    subgraph 输出生成
        P --> Q["主通道线性投影<br/>(B, pred_len, N) → (B, pred_len, c_out)"]
        P --> R["辅助通道线性投影<br/>(B, pred_len, N) → (B, pred_len, dim_var)"]
        
        Q --> S1["RevIN反标准化<br/>(仅用于对应输入维度)"]
        Q --> S2["标准反标准化<br/>(可选)"]
        R --> T["输出拼接<br/>(当 c_out > in_channels)"]
        S1 --> T
        S2 --> T
        
        S1 --> U["输出裁剪<br/>(当 c_out < in_channels)"]
        S2 --> U
    end
    
    T --> V[("预测输出 (B, pred_len, c_out)")]
    U --> V
    
    style 小波分解与处理 fill:#f9f,stroke:#333,stroke-width:1px
    style 混合专家系统增强 fill:#fcf,stroke:#333,stroke-width:1px
    style 输出生成 fill:#ffc,stroke:#333,stroke-width:1px
```

## 1. 模型概述

WaveMoE是在WaveForM基础上的增强版本,结合了小波变换、图神经网络和混合专家系统的优势,专为多变量时序序列的长期预测而设计。该模型通过小波分解捕获时序数据的多尺度特性,利用图神经网络建模变量间的空间依赖关系,并引入混合专家系统提升模型容量和处理复杂模式的能力。

### 核心特点

1. **多尺度时频分析**:通过小波变换将时序信号分解为不同频率成分,有效捕获长短期模式
2. **图增强空间建模**:利用图神经网络刻画变量间的依赖关系和交互模式
3. **混合专家系统**:引入多个专家网络和动态路由机制,提升模型表达能力和预测精度
4. **自适应输出维度**:灵活支持输入维度与输出维度不一致的情况
5. **双重标准化选项**:提供RevIN和传统标准化两种方式,提高训练稳定性和模型泛化能力

## 2. 模型架构详解

### 2.1 输入处理

#### 2.1.1 数据标准化

模型提供两种标准化方式:

**选项1: 可逆实例标准化 (RevIN)**

RevIN是一种专为时间序列设计的可逆标准化方法,能够有效处理分布偏移问题:

```python
# RevIN标准化
x_in = x_enc[:, :, :self.in_channels]
x_in = self.revin_layer(x_in, mode='norm')
```

RevIN的标准化操作:

$$\text{RevIN}_{\text{norm}}(x) = \frac{x - \mu}{\sigma}$$

其中,$\mu$ 和 $\sigma$ 是在实例(样本)维度上计算的均值和标准差。

**选项2: 传统标准化**

传统的均值-方差标准化:

$$\hat{x} = \frac{x - \mu}{\sigma}$$

其中:
- $x$ 是原始输入,形状为 $(B, L, N)$
- $\mu$ 是每个特征的均值,形状为 $(B, 1, N)$
- $\sigma$ 是每个特征的标准差,形状为 $(B, 1, N)$

#### 2.1.2 维度调整

调整输入数据的维度顺序,以适应小波变换的要求:

```python
# 输入: (batch_size, seq_len, n_points) -> (B, L, N)
# 输出: (batch_size, n_points, seq_len) -> (B, N, L)
in_dwt = x_in.permute(0, 2, 1)
```

### 2.2 小波分解与处理

#### 2.2.1 离散小波变换 (DWT)

对输入序列进行小波分解,将时序信号分解为近似系数和多层细节系数:

```python
# 输入: (B, N, L)
# 输出: yl (B, N, L_0), yhs [(B, N, L_1), (B, N, L_2), ...]
yl, yhs = self.dwt(in_dwt)
coefs = [yl] + yhs
```

DWT的数学表示:

$$\text{DWT}(x) = \{a_J, \{d_j\}_{j=1}^J\}$$

其中:
- $a_J$ 是近似系数,表示低频成分
- $d_j$ 是细节系数,表示不同尺度的高频成分
- $J$ 是分解层数

#### 2.2.2 图增强网络处理

每个小波系数通过独立的图神经网络(GPModule)进行处理,学习变量间的依赖关系:

```python
# 输入: 系数列表 [(B, N, L_i)]
# 输出: 预测系数列表 [(B, N, L_i')]
coefs_new = self.model(coefs)
```

GPModule的核心过程:

1. **图构建**:通过GraphConstructor构建变量间的关系图
   $$A = f_{\text{GC}}(V, E)$$
   其中 $V$ 是节点集合,$E$ 是边集合

2. **图卷积**:利用构建的图进行消息传递
   $$H^{(l+1)} = \sigma(D^{-1/2}AD^{-1/2}H^{(l)}W^{(l)})$$
   其中 $H^{(l)}$ 是第 $l$ 层的节点特征,$A$ 是邻接矩阵,$D$ 是度矩阵

3. **时间卷积**:采用扩张卷积捕获时间维度上的依赖
   $$Z = \text{TCN}(H)$$

#### 2.2.3 系数拼接与重构

将原始系数与预测系数拼接,然后通过逆小波变换重构完整序列:

```python
# 系数拼接: [(B, N, L_i)] + [(B, N, L_i')] -> [(B, N, L_i+L_i')]
coefs_idwt = []
for i in range(len(coefs_new)):
    coefs_idwt.append(torch.cat((coefs[i], coefs_new[i]), 2))

# 小波重构: [(B, N, L_i+L_i')] -> (B, N, L+pred_len)
out = self.idwt((coefs_idwt[0], coefs_idwt[1:]))
```

IDWT的数学表示:

$$\text{IDWT}(\{a_J, \{d_j\}_{j=1}^J\}) = x$$

### 2.3 混合专家系统增强

#### 2.3.1 特征处理与维度调整

从重构序列中提取预测部分,并进行维度调整:

```python
# 提取预测部分: (B, N, L+pred_len) -> (B, pred_len, N)
pred_out = out.permute(0, 2, 1)[:, -self.pred_len:, :]
```

#### 2.3.2 MoE架构

MoE由以下组件构成:

1. **前馈网络**:MoE前的前馈层
2. **专家路由器**:决定输入应该由哪些专家处理
3. **专家网络**:多个并行的专家网络,每个都是独立的前馈网络
4. **输出聚合**:将选定专家的输出进行加权聚合
5. **后馈网络**:MoE后的前馈层

```python
# 混合专家处理: (B, pred_len, N) -> (B, pred_len, N)
moe_result = self.moe_block(pred_out)[0]
```

#### 2.3.3 路由机制

路由过程可以表示为:

$$g_i(x) = \text{softmax}(W_g \cdot x)_i$$
$$y = \sum_{i=1}^{n} g_i(x) \cdot E_i(x)$$

其中:
- $g_i(x)$ 是第i个专家的路由权重
- $E_i(x)$ 是第i个专家的输出
- $W_g$ 是路由器的权重矩阵
- $n$ 是选定的专家数量(默认为2)

#### 2.3.4 负载均衡

为了确保专家网络负载均衡,引入了辅助损失:

$$\mathcal{L}_{balance} = \alpha \cdot \text{CV}(\text{expert\_usage})^2$$
$$\mathcal{L}_{z} = \beta \cdot \text{mean}(\text{router\_logits}^2)$$

其中:
- $\text{CV}$ 是变异系数,衡量专家使用分布的不均匀性
- $\alpha, \beta$ 分别是平衡损失和Z损失的系数

### 2.4 输出生成

#### 2.4.1 主通道处理

通过线性投影将特征映射到输出通道:

```python
# 主通道投影: (B, pred_len, N) -> (B, pred_len, c_out)
main_out = self.projection(pred_out)
```

#### 2.4.2 根据输入输出维度关系进行处理

模型可以灵活处理三种不同的输入输出维度关系情况:

**情况1: 输入维度 = 输出维度**

当输入和输出维度完全匹配时,模型采用最简单的处理方式:

```python
# 当输入和输出维度相同时
pred_out = self.projection(pred_out)

# 反标准化处理
if self.use_revin:
    # 使用和输入维度相同的RevIN层进行反标准化
    pred_out = self.revin_layer(pred_out, mode='denorm')
```

**情况2: 输出维度 > 输入维度**

当需要产生比输入更多的输出维度时,模型通过MoE生成额外的维度:

```python
# 主通道产生与输入相同维度的输出
main_out = self.projection(pred_out)

# 辅助通道投影: (B, pred_len, N) -> (B, pred_len, dim_var)
extra_out = self.output_proj(moe_result)[:, :, :self.dim_var]

# 输出拼接: (B, pred_len, in_channels) + (B, pred_len, dim_var) -> (B, pred_len, c_out)
pred_out = torch.cat([main_out, extra_out], dim=2)
```

**情况3: 输出维度 < 输入维度**

通过裁剪获得所需的较少维度:

```python
# 主通道投影后的输出维度大于需求
main_out = self.projection(pred_out)

# 输出裁剪: (B, pred_len, in_channels) -> (B, pred_len, c_out)
pred_out = main_out[:, :, :self.c_out]
```

#### 2.4.3 反标准化处理

根据所使用的标准化方法和维度关系,模型提供相应的反标准化操作:

**选项1: RevIN反标准化**

RevIN反标准化操作根据不同维度关系有不同处理策略:

1. **输入维度 = 输出维度**:直接使用输入的RevIN层进行反标准化
   ```python
   pred_out = self.revin_layer(pred_out, mode='denorm')
   ```

2. **输出维度 > 输入维度**:仅对与输入维度对应的部分进行反标准化
   ```python
   main_part = main_out[:, :, :self.in_channels]
   main_part = self.revin_layer(main_part, mode='denorm')
   main_out = torch.cat([main_part, main_out[:, :, self.in_channels:]], dim=2)
   ```

3. **输出维度 < 输入维度**:使用专门为输出维度创建的RevIN层
   ```python
   main_out = self.revin_layer_out(main_out, mode='denorm')
   ```

**选项2: 传统反标准化**

传统的均值-方差反标准化也根据维度关系调整:

```python
# 根据输出维度选择对应的统计量
stdev_rep = stdev[:, 0, :self.c_out].unsqueeze(1).repeat(1, self.pred_len, 1)
means_rep = means[:, 0, :self.c_out].unsqueeze(1).repeat(1, self.pred_len, 1)
main_out = main_out * stdev_rep + means_rep
```

## 3. 关键参数配置

### 3.1 基础参数

```python
configs = {
    "seq_len": 96,          # 输入序列长度
    "pred_len": 24,         # 预测长度
    "n_points": 18,         # 变量数量
    "dropout": 0.1,         # Dropout率
    "c_out": 18,            # 输出特征维度
    "wavelet_j": 2,         # 小波分解层数
    "wavelet": "db4",       # 小波类型
    "subgraph_size": 3,     # 子图大小
    "node_dim": 40,         # 节点维度
    "n_gnn_layer": 2,       # GNN层数
    "in_channels": 18,      # 输入特征维度
    "use_norm": False,      # 是否使用标准标准化
    "use_revin": True,      # 是否使用RevIN标准化
}
```

### 3.2 MoE相关参数

```python
moe_params = {
    "num_experts": 4,               # 专家数量
    "gating_top_n": 2,              # 每个输入选择的专家数
    "threshold_train": 0.2,         # 训练阈值
    "threshold_eval": 0.2,          # 评估阈值
    "capacity_factor_train": 1.25,  # 训练容量因子
    "capacity_factor_eval": 2.0,    # 评估容量因子
    "balance_loss_coef": 1e-2,      # 平衡损失系数
    "router_z_loss_coef": 1e-3      # 路由器z损失系数
}
```

### 3.3 模型结构参数

根据torchinfo摘要,模型的主要结构参数如下:

```python
model_structure = {
    "total_params": 1,290,378,      # 总参数量
    "trainable_params": 1,290,378,  # 可训练参数量
    "non_trainable_params": 0,      # 不可训练参数量
    "total_mult_adds": 906.39,      # 总乘加运算量(MB)
    "input_size": 0.17,             # 输入大小(MB)
    "forward_backward_size": 70.12,  # 前向/反向传播大小(MB)
    "params_size": 2.92,            # 参数大小(MB)
    "estimated_total_size": 73.21,  # 估计总大小(MB)
}
```

## 4. 前向传播流程

模型的完整前向传播可以分为以下步骤:

1. **输入标准化**:根据配置使用RevIN或传统标准化对输入数据进行处理
2. **维度调整**:调整输入维度以适应小波变换
3. **小波分解**:将输入序列分解为近似系数和细节系数
4. **图网络处理**:每个小波系数通过独立的图神经网络处理
5. **系数拼接**:将原始系数与预测系数拼接
6. **小波重构**:通过逆小波变换重构完整序列
7. **维度调整**:提取预测部分并调整维度
8. **混合专家处理**:通过SparseMoEBlock进一步增强特征表示
9. **输出生成**:根据输入输出维度关系生成最终预测
10. **反标准化**:对应标准化方法将输出转换回原始数据尺度,特别注意RevIN仅应用于与输入对应的维度

## 5. 应用场景与优势

### 5.1 适用场景

- **多变量工业时序预测**:生产指标、传感器数据、设备状态等
- **长期预测任务**:电力负载、市场需求、资源消耗等
- **具有复杂时频特性的数据**:包含趋势、周期性和不规则波动的时序数据
- **变量间存在复杂关系的场景**:变量之间具有动态依赖关系的系统
- **分布偏移严重的场景**:数据分布随时间变化明显的应用场景

### 5.2 模型优势

1. **多尺度分析能力**:小波变换能有效捕获不同时间尺度上的模式和特征
2. **空间关系建模**:图神经网络显式建模变量间的依赖关系
3. **计算效率高**:相比传统时序模型,能更高效地处理长序列
4. **专家分工协作**:混合专家系统能处理复杂和多样的时序模式
5. **维度灵活性**:支持输入输出维度不一致的应用场景
6. **分布偏移鲁棒性**:RevIN能有效处理时序数据中的分布偏移问题

## 6. 训练建议

1. **学习率设置**:
   - 使用warmup策略,从较小的学习率开始
   - 推荐初始学习率:1e-4,warmup步数:500

2. **批量大小选择**:
   - 根据硬件资源调整,推荐16-64
   - 较大的批量有助于图网络和MoE训练稳定

3. **标准化选择**:
   - 对于分布偏移明显的数据,优先选择RevIN
   - 对于分布相对稳定的场景,可尝试传统标准化

4. **小波参数选择**:
   - 小波类型:根据数据特性选择,常用db4、sym4等
   - 分解层数:根据序列长度确定,通常2-4层

5. **训练监控**:
   - 关注专家使用分布是否均衡
   - 监控图结构的稀疏性和连接模式
   - 跟踪不同小波系数的预测误差
