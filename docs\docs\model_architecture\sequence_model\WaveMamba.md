# WaveMamba: 基于小波变换与状态空间模型的多变量时序预测模型

## 网络结构图

```mermaid
graph TD
    subgraph 输入处理
        A[("输入数据 (B, L, N)")] --> B1["RevIN标准化处理<br/>(可选, 仅用于输入通道)"]
        A --> B2["标准均值-方差标准化<br/>(可选)"]
        B1 --> C["维度调整<br/>(B, L, N) → (B, N, L)"]
        B2 --> C
    end

    subgraph 小波分解与处理
        C --> D["小波分解<br/>DWT1DForward"]
        D --> E1["近似系数 yl<br/>(B, N, L_0)"]
        D --> E2["细节系数 yh_1<br/>(B, N, L_1)"]
        D --> E3["细节系数 yh_2<br/>(B, N, L_2)"]
        
        E1 --> F1["图网络处理 1<br/>GPModule"]
        E2 --> F2["图网络处理 2<br/>GPModule"]
        E3 --> F3["图网络处理 3<br/>GPModule"]
        
        F1 --> G1["预测系数 yl'<br/>(B, N, L_0')"]
        F2 --> G2["预测系数 yh_1'<br/>(B, N, L_1')"]
        F3 --> G3["预测系数 yh_2'<br/>(B, N, L_2')"]
        
        E1 --> H1["系数拼接<br/>(B, N, L_0+L_0')"]
        E2 --> H2["系数拼接<br/>(B, N, L_1+L_1')"]
        E3 --> H3["系数拼接<br/>(B, N, L_2+L_2')"]
        
        G1 --> H1
        G2 --> H2
        G3 --> H3
        
        H1 --> I["小波重构<br/>DWT1DInverse"]
        H2 --> I
        H3 --> I
    end
    
    subgraph Mamba状态空间建模
        I --> J["维度调整<br/>(B, N, L+pred_len) → (B, pred_len, N)"]
        J --> K["MambaBlock"]
        
        K --> L["LayerNorm"]
        L --> M["Input Projection"]
        M --> N["Mamba SSM<br/>(Selective Scan)"]
        N --> O["Dropout"]
        O --> P["Output Projection + Residual"]
    end
    
    subgraph 输出生成
        P --> Q["主通道线性投影<br/>(B, pred_len, N) → (B, pred_len, c_out)"]
        P --> R["辅助通道线性投影<br/>(B, pred_len, N) → (B, pred_len, dim_var)"]
        
        Q --> S1["RevIN反标准化<br/>(仅用于对应输入维度)"]
        Q --> S2["标准反标准化<br/>(可选)"]
        R --> T["输出拼接<br/>(当 c_out > in_channels)"]
        S1 --> T
        S2 --> T
        
        S1 --> U["输出裁剪<br/>(当 c_out < in_channels)"]
        S2 --> U
    end
    
    T --> V[("预测输出 (B, pred_len, c_out)")]
    U --> V
    
    style 小波分解与处理 fill:#f9f,stroke:#333,stroke-width:1px
    style Mamba状态空间建模 fill:#ccf,stroke:#333,stroke-width:1px
    style 输出生成 fill:#ffc,stroke:#333,stroke-width:1px
```

## 1. 模型概述

WaveMamba是一种结合了小波变换、图神经网络和Mamba(结构化状态空间模型)优势的多变量时序预测模型,专为多变量时序序列的长期预测而设计。该模型通过小波分解捕获时序数据的多尺度特性,利用图神经网络建模变量间的空间依赖关系,并引入Mamba状态空间模型高效地处理序列依赖。

### 核心特点

1.  **多尺度时频分析**:通过小波变换将时序信号分解为不同频率成分,有效捕获长短期模式。
2.  **图增强空间建模**:利用图神经网络刻画变量间的依赖关系和交互模式。
3.  **高效序列建模**:引入Mamba状态空间模型,以线性时间复杂度 \(O(L)\) 处理长序列依赖。
4.  **自适应输出维度**:灵活支持输入维度与输出维度不一致的情况。
5.  **双重标准化选项**:提供RevIN和传统标准化两种方式,提高训练稳定性和模型泛化能力。

## 2. 模型架构详解

### 2.1 输入处理

#### 2.1.1 数据标准化

模型提供两种标准化方式:

**选项1: 可逆实例标准化 (RevIN)**

RevIN是一种专为时间序列设计的可逆标准化方法,能够有效处理分布偏移问题:

```python
# RevIN标准化
x_in = x_enc[:, :, :self.in_channels]
x_in = self.revin_layer(x_in, mode='norm')
```

RevIN的标准化操作:

$$
\text{RevIN}_{\text{norm}}(x) = \frac{x - \mu}{\sigma}
$$

其中,\(\mu\) 和 \(\sigma\) 是在实例(样本)维度上计算的均值和标准差。

**选项2: 传统标准化**

传统的均值-方差标准化:

$$
\hat{x} = \frac{x - \mu}{\sigma}
$$

其中:
- \(x\) 是原始输入,形状为 \((B, L, N)\)
- \(\mu\) 是每个特征的均值,形状为 \((B, 1, N)\)
- \(\sigma\) 是每个特征的标准差,形状为 \((B, 1, N)\)

#### 2.1.2 维度调整

调整输入数据的维度顺序,以适应小波变换的要求:

```python
# 输入: (batch_size, seq_len, n_points) -> (B, L, N)
# 输出: (batch_size, n_points, seq_len) -> (B, N, L)
in_dwt = x_in.permute(0, 2, 1)
```

### 2.2 小波分解与处理

#### 2.2.1 离散小波变换 (DWT)

对输入序列进行小波分解,将时序信号分解为近似系数和多层细节系数:

```python
# 输入: (B, N, L)
# 输出: yl (B, N, L_0), yhs [(B, N, L_1), (B, N, L_2), ...]
yl, yhs = self.dwt(in_dwt)
coefs = [yl] + yhs
```

DWT的数学表示:

$$
\text{DWT}(x) = \{a_J, \{d_j\}_{j=1}^J\}
$$

其中:
- \(a_J\) 是近似系数,表示低频成分
- \(d_j\) 是细节系数,表示不同尺度的高频成分
- \(J\) 是分解层数

#### 2.2.2 图增强网络处理

每个小波系数通过独立的图神经网络(GPModule)进行处理,学习变量间的依赖关系:

```python
# 输入: 系数列表 [(B, N, L_i)]
# 输出: 预测系数列表 [(B, N, L_i')]
coefs_new = self.model(coefs)
```

GPModule的核心过程:

1.  **图构建**:通过GraphConstructor构建变量间的关系图
    $$
    A = f_{\text{GC}}(V, E)
    $$
    其中 \(V\) 是节点集合,\(E\) 是边集合

2.  **图卷积**:利用构建的图进行消息传递
    $$
    H^{(l+1)} = \sigma(D^{-1/2}AD^{-1/2}H^{(l)}W^{(l)})
    $$
    其中 \(H^{(l)}\) 是第 \(l\) 层的节点特征,\(A\) 是邻接矩阵,\(D\) 是度矩阵

3.  **时间卷积**:采用扩张卷积捕获时间维度上的依赖
    $$
    Z = \text{TCN}(H)
    $$

#### 2.2.3 系数拼接与重构

将原始系数与预测系数拼接,然后通过逆小波变换重构完整序列:

```python
# 系数拼接: [(B, N, L_i)] + [(B, N, L_i')] -> [(B, N, L_i+L_i')]
coefs_idwt = []
for i in range(len(coefs_new)):
    coefs_idwt.append(torch.cat((coefs[i], coefs_new[i]), 2))

# 小波重构: [(B, N, L_i+L_i')] -> (B, N, L+pred_len)
out = self.idwt((coefs_idwt[0], coefs_idwt[1:]))
```

IDWT的数学表示:

$$
\text{IDWT}(\{a_J, \{d_j\}_{j=1}^J\}) = x
$$

### 2.3 Mamba状态空间建模

#### 2.3.1 特征处理与维度调整

从重构序列中提取预测部分,并进行维度调整:

```python
# 提取预测部分: (B, N, L+pred_len) -> (B, pred_len, N)
pred_out = out.permute(0, 2, 1)[:, -self.pred_len:, :]
```

#### 2.3.2 MambaBlock架构

MambaBlock替代了原有的MoE模块,用于增强序列建模能力。其核心是Mamba SSM(Structured State Space Model)。

MambaBlock的内部流程:

1.  **Layer Normalization**:对输入进行归一化
2.  **Input Projection**:线性变换输入特征
3.  **Mamba SSM**:核心的状态空间模型,包含选择性扫描机制(Selective Scan)
4.  **Dropout**:进行正则化
5.  **Output Projection + Residual Connection**:线性变换输出并加上残差连接

```python
# Mamba处理: (B, pred_len, N) -> (B, pred_len, N)
pred_out = self.mamba_block(pred_out)
```

#### 2.3.3 Mamba SSM 核心原理

Mamba基于结构化状态空间模型 (SSM),其核心是离散化的状态空间方程:

$$
\begin{aligned}
h_t &= \bar{A} h_{t-1} + \bar{B} x_t \\
y_t &= \bar{C} h_t + D x_t
\end{aligned}
$$

其中:
- \(h_t\) 是时间步 \(t\) 的隐藏状态
- \(x_t\) 是时间步 \(t\) 的输入
- \(y_t\) 是时间步 \(t\) 的输出
- \(\bar{A}, \bar{B}, \bar{C}\) 是通过零阶保持 (ZOH) 离散化的连续时间参数 \(A, B, C\)
- \(D\) 是跳跃连接参数

Mamba的关键创新在于**选择性扫描机制 (Selective Scan)**:参数 \(\bar{A}, \bar{B}, \bar{C}\) 不是固定的,而是**输入依赖**的。这使得模型能够根据输入内容动态地调整其状态转换和输出生成,从而选择性地关注或忽略序列中的信息。

$$
\begin{aligned}
\Delta_t &= \text{Proj}_{\Delta}(x_t) \\
B_t &= \text{Proj}_{B}(x_t) \\
C_t &= \text{Proj}_{C}(x_t) \\
\bar{A}_t &= \exp(\Delta_t A) \\
\bar{B}_t &= (\Delta_t A)^{-1}(\exp(\Delta_t A) - I) \Delta_t B_t \approx \Delta_t B_t \quad (\text{Simplified Euler}) \\
\bar{C}_t &= C_t
\end{aligned}
$$

选择性扫描通过高效的并行扫描算法实现,使其在硬件上具有良好的计算效率,时间复杂度为 \(O(L)\)。

### 2.4 输出生成

#### 2.4.1 主通道处理

通过线性投影将Mamba处理后的特征映射到所需的输出通道数(通常是 `in_channels`):

```python
# 主通道投影: (B, pred_len, N) -> (B, pred_len, N)
# 在这种结构中,Mamba的输出维度通常与输入相同,所以不需要单独的投影
# 如果需要调整维度,可以在MambaBlock内部或外部添加投影层
# 在当前实现中,投影层用于生成最终的c_out和dim_var维度
main_out = pred_out 
```
*注意: WaveMamba的当前实现中,`self.projection` 和 `self.output_proj` 在 Mamba 处理之后应用。*

#### 2.4.2 根据输入输出维度关系进行处理

模型可以灵活处理三种不同的输入输出维度关系情况:

**情况1: 输入维度 = 输出维度 (`in_channels == c_out`)**

```python
# 当输入和输出维度相同时
# 直接使用Mamba输出,后续反标准化
# 反标准化处理
if self.use_revin:
    pred_out = self.revin_layer(main_out, mode='denorm')
else:
    # Apply standard denormalization if used
    pred_out = main_out # Assuming standard norm was applied earlier and needs denorm
```

**情况2: 输出维度 > 输入维度 (`c_out > in_channels`)**

当需要产生比输入更多的输出维度时,模型通过额外的线性投影生成差异维度 (`dim_var`):

```python
# 主通道输出 (对应输入维度)
main_part = main_out # Shape: (B, pred_len, in_channels)

# 辅助通道投影: (B, pred_len, N) -> (B, pred_len, dim_var)
extra_out = self.output_proj(main_out)[:, :, :self.dim_var]

# 反标准化 (仅对主通道)
if self.use_revin:
    main_part_denorm = self.revin_layer(main_part, mode='denorm')
else:
    main_part_denorm = main_part # Apply standard denorm if needed

# 输出拼接: (B, pred_len, in_channels) + (B, pred_len, dim_var) -> (B, pred_len, c_out)
pred_out = (main_part_denorm, extra_out) # Model returns a tuple
```

**情况3: 输出维度 < 输入维度 (`c_out < in_channels`)**

这种情况在当前 `WaveMamba` 的 `forward` 实现中没有显式处理逻辑表明需要裁剪 `main_out`。`self.projection` 会直接将 `pred_out` (Mamba 输出) 投影到 `c_out` 维度。

```python
# Mamba输出后,直接投影到 c_out 维度
pred_out = self.projection(main_out) # (B, pred_len, c_out)

# 反标准化处理
if self.use_revin:
    # RevIN needs input dimension, so we only apply if c_out == in_channels
    # If c_out < in_channels, RevIN denorm cannot be directly applied to pred_out
    # This scenario might require adjustments in RevIN handling or model structure
    pass # Or apply standard denorm if applicable
```
*注意:RevIN 反标准化的应用在 `c_out < in_channels` 时需要特别注意,因为它需要原始的 `in_channels` 维度。当前代码在 `dim_var != 0` 时会返回元组,没有单独处理 `c_out < in_channels` 的情况。*

#### 2.4.3 反标准化处理

根据所使用的标准化方法和维度关系,模型提供相应的反标准化操作:

**选项1: RevIN反标准化**

- 当 `in_channels == c_out` 且 `use_revin=True` 时,直接对最终输出进行反标准化。
- 当 `c_out > in_channels` 且 `use_revin=True` 时,仅对与输入维度对应的 `main_part` 进行反标准化。
- 当 `c_out < in_channels` 且 `use_revin=True` 时,直接应用 RevIN 反标准化可能不正确,因为维度不匹配。

**选项2: 传统反标准化**

如果使用了传统标准化,反标准化过程如下:

```python
# 假设 stdev 和 means 是在输入处理时保存的
pred_out = pred_out * stdev + means
```
这需要在 `forward` 函数中传递或保存 `stdev` 和 `means`。

## 3. 关键参数配置

### 3.1 基础参数

```python
configs = {
    "seq_len": 120,         # 输入序列长度
    "pred_len": 60,         # 预测长度
    "n_points": 18,         # 变量数量 (等同于 Mamba 的 d_model)
    "dropout": 0.1,         # Dropout率
    "c_out": 18,            # 输出特征维度
    "in_channels": 18,      # 输入特征维度
    "use_revin": False,     # 是否使用RevIN标准化
    "wavelet_j": 2,         # 小波分解层数
    "wavelet": "db4",       # 小波类型
    "subgraph_size": 3,     # 图构造器的子图大小
    "node_dim": 40,         # 图构造器的节点维度
    "n_gnn_layer": 2,       # GPModule 中的 GNN 层数
}
```

### 3.2 Mamba相关参数

```python
mamba_params = {
    "d_model": configs.n_points, # Mamba 模型的维度 (等于变量数量)
    "d_state": 16,               # SSM 状态维度
    "d_conv": 4,                 # 内部卷积核大小
    "expand": 2,                 # 内部扩展因子
}
```
*注意:在 `WaveMamba` 实现中,MambaBlock 的 `d_model` 被设置为 `self.points` (即 `n_points`)*。

### 3.3 模型结构参数 (示例)

模型结构参数会根据具体配置变化,以下为示例:
*(需要运行 `torchinfo.summary` 获取实际值)*

```python
model_structure_example = {
    "total_params": 1_500_000,      # 总参数量 (示例)
    "trainable_params": 1_500_000,  # 可训练参数量 (示例)
    # ... 其他 torchinfo 输出
}
```

## 4. 前向传播流程

模型的完整前向传播可以分为以下步骤:

1.  **输入标准化**:根据配置使用RevIN或传统标准化对输入数据进行处理。
2.  **维度调整**:调整输入维度以适应小波变换 \((B, L, N) \rightarrow (B, N, L)\)。
3.  **小波分解**:将输入序列分解为近似系数和细节系数。
4.  **图网络处理**:每个小波系数通过独立的图神经网络(GPModule)处理,生成预测系数。
5.  **系数拼接**:将原始系数与预测系数拼接。
6.  **小波重构**:通过逆小波变换(IDWT)重构完整序列 \((B, N, L+\text{pred\_len})\)。
7.  **维度调整**:提取预测部分并调整维度 \((B, N, L+\text{pred\_len}) \rightarrow (B, \text{pred\_len}, N)\)。
8.  **Mamba处理**:通过 `MambaBlock` 对预测部分进行序列建模 \((B, \text{pred\_len}, N) \rightarrow (B, \text{pred\_len}, N)\)。
9.  **输出生成**:根据 `in_channels` 和 `c_out` 的关系,使用线性投影生成最终输出。如果 `c_out != in_channels`,可能需要辅助投影并返回元组。
10. **反标准化**:对应标准化方法将输出转换回原始数据尺度,特别注意RevIN仅应用于与输入对应的维度。

## 5. 应用场景与优势

### 5.1 适用场景

-   **多变量工业时序预测**:生产指标、传感器数据、设备状态等。
-   **长期预测任务**:电力负载、市场需求、资源消耗等。
-   **具有复杂时频特性的数据**:包含趋势、周期性和不规则波动的时序数据。
-   **变量间存在复杂关系的场景**:变量之间具有动态依赖关系的系统。
-   **需要高效处理长序列的场景**:Mamba 的线性复杂度优势。
-   **分布偏移严重的场景**:RevIN有助于处理数据分布变化。

### 5.2 模型优势

1.  **多尺度分析能力**:小波变换能有效捕获不同时间尺度上的模式和特征。
2.  **空间关系建模**:图神经网络显式建模变量间的依赖关系。
3.  **高效长序列处理**:Mamba 以 \(O(L)\) 的线性时间复杂度高效处理长序列依赖。
4.  **选择性建模**:Mamba 的选择性扫描机制使其能根据输入内容动态调整状态。
5.  **维度灵活性**:支持输入输出维度不一致的应用场景。
6.  **分布偏移鲁棒性**:RevIN能有效处理时序数据中的分布偏移问题。

## 6. 训练建议

1.  **学习率设置**:
    -   使用AdamW优化器,结合warmup和cosine衰减策略。
    -   推荐初始学习率:1e-4 到 5e-4。

2.  **批量大小选择**:
    -   根据硬件资源调整,推荐16-64。
    -   较大的批量有助于图网络和Mamba训练稳定。

3.  **标准化选择**:
    -   对于分布偏移明显的数据,优先选择RevIN。
    -   对于分布相对稳定的场景,可尝试传统标准化。

4.  **小波参数选择**:
    -   小波类型:根据数据特性选择,常用db4、sym4等。
    -   分解层数:根据序列长度确定,通常2-4层。

5.  **Mamba参数调整**:
    -   `d_state`:影响模型容量和计算量,可尝试8, 16, 32。
    -   `d_conv`:控制局部卷积范围,通常设置为2, 3, 4。
    -   `expand`:控制内部维度扩展,通常为2。

6.  **训练监控**:
    -   监控训练和验证损失。
    -   监控图结构的稀疏性和连接模式。
    -   跟踪不同小波系数的预测误差。
